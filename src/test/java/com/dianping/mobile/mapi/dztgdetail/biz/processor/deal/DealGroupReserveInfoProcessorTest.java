package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.google.common.collect.Lists;
import com.sankuai.clr.content.process.gateway.thrift.common.user.trade.request.DealGroupInfoDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ Lion.class })
public class DealGroupReserveInfoProcessorTest {

    @InjectMocks
    private DealGroupReserveInfoProcessor dealGroupReserveInfoProcessor;

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private DealCtx ctx;

    // @Test
    // public void testGetWashReserveExpResult() throws InvocationTargetException, IllegalAccessException {
    // ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
    // moduleAbConfig.setKey("MtWashReserveExp");
    // AbConfig abConfig = new AbConfig();
    // abConfig.setExpId("expId");
    // abConfig.setExpResult("c");
    // moduleAbConfig.setConfigs(Lists.newArrayList(abConfig));
    //
    // when(ctx.getModuleAbConfigs()).thenReturn(Lists.newArrayList(moduleAbConfig));
    //
    // Method method = PowerMockito.method(DealGroupReserveInfoProcessor.class, "getWashReserveExpResult");
    // boolean result = (boolean) method.invoke(dealGroupReserveInfoProcessor, ctx);
    // assert !result;
    // }
    private static final String dpPoiDtoJson = "{\"@class\":\"com.sankuai.sinai.data.api.dto.DpPoiDTO\",\"shopId\":607001018,\"shopGroupId\":null,\"branchName\":null,\"altName\":null,\"shopName\":null,\"mainRegionName\":null,\"mainRegionId\":null,\"mainCategoryName\":null,\"mainCategoryId\":null,\"crossRoad\":null,\"address\":null,\"lng\":null,\"lat\":null,\"cityId\":null,\"shopType\":null,\"districtId\":null,\"power\":null,\"clientType\":null,\"status\":null,\"useType\":null,\"srcName\":null,\"shopDesc\":null,\"freshBusinessHours\":null,\"businessHours\":null,\"publicTransit\":null,\"mapType\":null,\"coordType\":null,\"addUserId\":null,\"addUser\":null,\"lastUserId\":null,\"lastUser\":null,\"addTime\":null,\"updateTime\":null,\"shopRegionList\":null,\"shopCategoryList\":null,\"shopBackCategoryList\":null,\"backMainCategoryPath\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO\",\"categoryId\":3,\"categoryName\":\"休闲娱乐\",\"parentId\":0,\"hot\":0,\"categoryLevel\":1,\"leaf\":false,\"main\":null},{\"@class\":\"com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO\",\"categoryId\":817,\"categoryName\":\"按摩/足疗\",\"parentId\":3,\"hot\":0,\"categoryLevel\":2,\"leaf\":true,\"main\":true}]],\"mainCategoryPath\":null,\"uuid\":null,\"normPhones\":null,\"shopPower\":null,\"avgPrice\":null,\"score\":null,\"score1\":null,\"score2\":null,\"score3\":null,\"score4\":null,\"refinedScore1\":\"\",\"refinedScore2\":\"\",\"refinedScore3\":\"\",\"fiveScore\":0,\"sub5\":0,\"sub6\":0,\"fiveSub1\":0,\"fiveSub2\":0,\"fiveSub3\":0,\"fiveSub4\":0,\"fiveSub5\":0,\"fiveSub6\":0,\"defaultPic\":null,\"defaultPicKey\":null,\"hits\":null,\"weeklyHits\":null,\"prevWeeklyHits\":null,\"todayHits\":null,\"monthlyHits\":null,\"poiBizAttrValues\":null,\"sensitiveLevel\":null,\"newPoiSensLevel\":null,\"mid\":null,\"createSubSourceStr\":null,\"hospitalInfo\":null,\"isRevealPic\":null,\"hsStatus\":null,\"poiBrandId\":null,\"appSides\":null,\"legalStatus\":null,\"crtMntSrc\":null,\"dataMntSrc\":null,\"geomConfidence\":null,\"statusConfidence\":null,\"kindConfidence\":null,\"phoneConfidence\":null,\"subScores\":null,\"mtPoiId\":null,\"onDoorInfo\":null,\"shopGuideSteps\":null,\"suspectPhones\":null,\"suspectBizHour\":null}";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(Lion.class);
    }

    /**
     * Helper method to invoke the private method using reflection.
     */
    private boolean invokeIsShowReserveEntrance(DealGroupReserveInfoProcessor processor, Boolean shopSupportReserve, String expResult) throws Exception {
        Method method = DealGroupReserveInfoProcessor.class.getDeclaredMethod("isShowReserveEntrance", Boolean.class, String.class);
        method.setAccessible(true);
        return (boolean) method.invoke(processor, shopSupportReserve, expResult);
    }

    private String invokePrivateGetExpResult(DealCtx ctx, String mtKey, String dpKey) throws Exception {
        Method method = DealGroupReserveInfoProcessor.class.getDeclaredMethod("getExpResult", DealCtx.class, String.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(dealGroupReserveInfoProcessor, ctx, mtKey, dpKey);
    }

    /**
     * 测试DealGroupDTO为null时，返回null的情况
     */
    @Test
    public void testBuildDealGroupInfoDTO_WhenDealGroupDTOIsNull() {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(null);
        // act
        DealGroupInfoDTO result = dealGroupReserveInfoProcessor.buildDealGroupInfoDTO(ctx);
        // assert
        assertNull(result);
    }

    /**
     * 测试DealGroupDTO不为null，但其他属性为null时，返回的DealGroupInfoDTO只包含基本信息的情况
     */
    @Test
    public void testBuildDealGroupInfoDTO_WhenDealGroupDTOIsNotNullButOthersAreNull() {
        // arrange
        // 假设ctx.getDealGroupDTO()返回一个只设置了基本信息的DealGroupDTO对象
        // 为简化示例，这里不展示DealGroupDTO的mock设置，实际使用时需要根据实际情况设置
        when(ctx.getDealGroupDTO()).thenReturn(new DealGroupDTO());
        // act
        DealGroupInfoDTO result = dealGroupReserveInfoProcessor.buildDealGroupInfoDTO(ctx);
        // assert
        assertNotNull(result);
        // 根据实际情况添加更多的断言来验证返回的DealGroupInfoDTO对象的状态
    }

    /**
     * 测试DealGroupDTO不为null，且包含完整属性时，返回的DealGroupInfoDTO包含所有信息的情况
     */
    @Test
    public void testBuildDealGroupInfoDTO_WhenDealGroupDTOIsComplete() {
        // arrange
        // 假设ctx.getDealGroupDTO()返回一个设置了完整信息的DealGroupDTO对象
        // 为简化示例，这里不展示DealGroupDTO的mock设置，实际使用时需要根据实际情况设置
        when(ctx.getDealGroupDTO()).thenReturn(JSON.parseObject("{\"dpDealGroupId\":1026428192,\"mtDealGroupId\":1026428192,\"basic\":{\"categoryId\":2001,\"title\":\"一张精修·成人证件照·仅含服饰·立等可取\",\"brandName\":\"生活服务快找POIID升级测试\",\"titleDesc\":\"仅售30元，价值39元一张精修·成人证件照·仅含服饰·立等可取！\",\"beginSaleDate\":\"2024-06-27 15:23:50\",\"endSaleDate\":\"2025-06-27 15:08:35\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/8ea585e6090cf318bc38c214bca7082063584.jpg\",\"allPicPaths\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/8ea585e6090cf318bc38c214bca7082063584.jpg\"},\"category\":{\"categoryId\":2001,\"serviceType\":\"成人证件照\",\"serviceTypeId\":122003},\"serviceProject\":{\"title\":\"团购详情\",\"salePrice\":\"30.00\",\"marketPrice\":\"39.00\",\"mustGroups\":[{\"groups\":[{\"skuId\":0,\"categoryId\":2104784,\"name\":\"成人证件照\",\"amount\":1,\"marketPrice\":\"39.0\",\"status\":10,\"attrs\":[{\"metaAttrId\":1423,\"attrName\":\"photoStyle\",\"chnName\":\"拍摄类型\",\"attrValue\":\"单底色证件照\",\"rawAttrValue\":\"单底色证件照\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":852,\"attrName\":\"applyscene\",\"chnName\":\"适用场景\",\"attrValue\":\"简历、身份证、签证、入园、升学、毕业、考试、报名、考级、头像、研究生考试、法考、公考、艺考、驾驶证\",\"rawAttrValue\":\"简历、身份证、签证、入园、升学、毕业、考试、报名、考级、头像、研究生考试、法考、公考、艺考、驾驶证\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":2349,\"attrName\":\"dressNumStr\",\"chnName\":\"服装提供\",\"attrValue\":\"含\",\"rawAttrValue\":\"含\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1382,\"attrName\":\"dressNum\",\"chnName\":\"服装套数\",\"attrValue\":\"1\",\"rawAttrValue\":\"1\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":1381,\"attrName\":\"poseProduct\",\"chnName\":\"化妆造型\",\"attrValue\":\"不含\",\"rawAttrValue\":\"不含\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1387,\"attrName\":\"intensiveRepairNum\",\"chnName\":\"精修张数\",\"attrValue\":\"1\",\"rawAttrValue\":\"1\",\"valueType\":401,\"sequence\":0},{\"metaAttrId\":2699,\"attrName\":\"printPhotos\",\"chnName\":\"照片打印\",\"attrValue\":\"提供打印\",\"rawAttrValue\":\"提供打印\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1389,\"attrName\":\"photoOutput\",\"chnName\":\"打印规格\",\"attrValue\":\"自行填写\",\"rawAttrValue\":\"自行填写\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":3075,\"attrName\":\"fetchingDuration\",\"chnName\":\"取片周期\",\"attrValue\":\"立等可取\",\"rawAttrValue\":\"立等可取\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":1390,\"attrName\":\"serviceDuration\",\"chnName\":\"服务时长\",\"attrValue\":\"30分钟\",\"rawAttrValue\":\"30分钟\",\"valueType\":500,\"sequence\":0},{\"metaAttrId\":184469,\"attrName\":\"provideRefinementService\",\"chnName\":\"是否提供精修\",\"attrValue\":\"是\",\"rawAttrValue\":\"是\",\"valueType\":500,\"sequence\":0}]}]}],\"optionGroups\":[],\"structType\":\"uniform-structure-table\"},\"channel\":{\"channelId\":20,\"channelEn\":\"snapshot\",\"channelCn\":\"快照\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"service_type_leaf_id\",\"value\":[\"122003\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"成人证件照\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"是\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"11000\"],\"source\":0}],\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2024-08-28 14:49:52\",\"receiptEndDate\":\"2024-11-26 23:59:59\",\"showText\":\"购买后90天内有效\"}},\"refundRule\":{\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"dpDisplayShopIds\":[1055916787112237],\"mtDisplayShopIds\":[1055916787112237]},\"tags\":[{\"id\":100087623,\"tagName\":\"证件照在线团购\"},{\"id\":100003427,\"tagName\":\"证件照\"},{\"id\":100079072,\"tagName\":\"法考证件照\"},{\"id\":100079071,\"tagName\":\"考研证件照\"},{\"id\":100079073,\"tagName\":\"公考证件照\"},{\"id\":100079065,\"tagName\":\"入学照\"},{\"id\":100079075,\"tagName\":\"驾驶证证件照\"},{\"id\":100079061,\"tagName\":\"简历照\"},{\"id\":100079067,\"tagName\":\"毕业照\"},{\"id\":100081004,\"tagName\":\"证件照立等可取\"},{\"id\":100079068,\"tagName\":\"考试照\"},{\"id\":100079064,\"tagName\":\"入园照\"},{\"id\":100079063,\"tagName\":\"考级照\"},{\"id\":100079060,\"tagName\":\"身份证照\"},{\"id\":100079066,\"tagName\":\"升学照\"},{\"id\":100079062,\"tagName\":\"签证照\"},{\"id\":100079074,\"tagName\":\"艺考证件照\"},{\"id\":100079070,\"tagName\":\"头像照\"},{\"id\":100079069,\"tagName\":\"报名照\"},{\"id\":100232092,\"tagName\":\"tab-成人证件照-立等可取\"},{\"id\":100200320,\"tagName\":\"tab-成人证件照49元\"},{\"id\":100232095,\"tagName\":\"tab快照-立等可取\"}],\"customer\":{\"originCustomerId\":41264456,\"platformCustomerId\":1030348237},\"regions\":[{\"dpCityId\":1,\"mtCityId\":10},{\"dpCityId\":10,\"mtCityId\":40}],\"deals\":[{\"dealId\":457811439,\"basic\":{\"title\":\"一张精修·成人证件照·仅含服饰·立等可取\",\"originTitle\":\"一张精修·成人证件照·仅含服饰·立等可取\",\"status\":1},\"price\":{\"salePrice\":\"30.00\",\"marketPrice\":\"39.00\",\"version\":5160968502},\"stock\":{\"dpSales\":5,\"dpTotal\":100000000,\"dpRemain\":99999995,\"mtSales\":11,\"mtTotal\":100000000,\"mtRemain\":99999989,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":0,\"sharedRemain\":0,\"isSharedSoldOut\":false},\"attrs\":[{\"name\":\"sku_receipt_type\",\"value\":[\"1\"],\"source\":0}],\"dealIdInt\":457811439}],\"price\":{\"salePrice\":\"30.00\",\"marketPrice\":\"39.00\",\"version\":5160968502},\"stock\":{\"dpSales\":5,\"dpTotal\":100000000,\"dpRemain\":99999995,\"mtSales\":11,\"mtTotal\":100000000,\"mtRemain\":99999989,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"detail\":{\"dealGroupPics\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/8ea585e6090cf318bc38c214bca7082063584.jpg\",\"images\":[\"https://qcloud.dpfile.com/pc/vbgO2dmiPCl7Q-Ws31Mo1PxCkBDWdWldGjQ-lJcI4ru-pRs1n5zSDVQ8EDbuZgc0Txq4kKRPxu2Gq1lq24bRpA.jpg\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"title\":\"产品介绍\",\"content\":\"<div><p>图文详情介绍：</p><p>1、测试POIID升级</p><p>2、测测测测测测图文详情介绍</p></div>\\n\\n\",\"type\":5,\"blockId\":0},{\"title\":\"团购详情\",\"content\":\"        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>成人证件照</td>\\n                                    <td class=\\\"tc\\\">1</td>\\n                                    <td class=\\\"tc\\\">39元</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">39元<br><strong>30元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n\\n\",\"type\":1,\"blockId\":0},{\"title\":\"购买须知\",\"content\":\"<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">请您提前1小时预约\\n</p>\\n                <p class=\\\"listitem\\\">预约到店时间后不可取消或修改预约，团购退款需商家同意</p>\\n                <p class=\\\"listitem\\\">预约最晚保留60分钟，过期商家可能无法接待</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">不再与其他优惠同享\\n</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\",\"type\":2,\"blockId\":0}]},\"saleChannelAggregation\":{\"allSupport\":true,\"supportChannels\":[],\"notSupportChannels\":[]},\"dpDealGroupIdInt\":1026428192,\"mtDealGroupIdInt\":1026428192}", DealGroupDTO.class));
        // act
        DealGroupInfoDTO result = dealGroupReserveInfoProcessor.buildDealGroupInfoDTO(ctx);
        // assert
        assertNotNull(result);
        // 根据实际情况添加更多的断言来验证返回的DealGroupInfoDTO对象的状态
    }

    /**
     * 测试DealGroupDTO不为null，但在处理过程中发生异常时，返回null的情况
     */
    @Test
    public void testBuildDealGroupInfoDTO_WhenExceptionOccurs() {
        // arrange
        // 假设在处理DealGroupDTO时发生异常，可以通过模拟抛出异常来模拟这种情况
        when(ctx.getDealGroupDTO()).thenThrow(new RuntimeException());
        // act
        DealGroupInfoDTO result = null;
        try {
            result = dealGroupReserveInfoProcessor.buildDealGroupInfoDTO(ctx);
        } catch (Exception e) {
            // 处理异常
        }
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：moduleAbConfig为null
     */
    @Test
    public void testEnableReserveEntranceAbTest_NullModuleAbConfig() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("testUnionId");
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(ctx.isMt()).thenReturn(true);
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(null);
        boolean result = dealGroupReserveInfoProcessor.enableReserveEntranceAbTest(ctx);
        assertFalse(result);
    }

    /**
     * 测试场景：moduleAbConfig.configs为空
     */
    @Test
    public void testEnableReserveEntranceAbTest_EmptyConfigs() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("testUnionId");
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(ctx.isMt()).thenReturn(true);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(new ArrayList<>());
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);
        boolean result = dealGroupReserveInfoProcessor.enableReserveEntranceAbTest(ctx);
        assertFalse(result);
    }

    /**
     * 测试场景：expResult不为"c"
     */
    @Test
    public void testEnableReserveEntranceAbTest_ExpResultNotC() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("testUnionId");
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(ctx.isMt()).thenReturn(true);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);
        boolean result = dealGroupReserveInfoProcessor.enableReserveEntranceAbTest(ctx);
        assertFalse(result);
    }

    /**
     * 测试场景：expResult为"c"
     */
    @Test
    public void testEnableReserveEntranceAbTest_ExpResultC() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setUnionId("testUnionId");
        when(ctx.getEnvCtx()).thenReturn(envCtx);
        when(ctx.isMt()).thenReturn(true);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("c");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);
        boolean result = dealGroupReserveInfoProcessor.enableReserveEntranceAbTest(ctx);
        assertTrue(result);
    }

    // @Test
    // public void testPoiWhiteListJudge() throws InvocationTargetException, IllegalAccessException {
    // DpPoiDTO dpPoiDTO = JacksonUtils.deserialize(dpPoiDtoJson, DpPoiDTO.class);
    // when(Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",
    // "com.sankuai.dzu.tpbase.dztgdetailweb.wash.backlevel2CategoryId", Integer.class,
    // new ArrayList<>())).thenAnswer((Answer<?>) invocation -> Lists.newArrayList(817));
    // when(ctx.getDpPoiDTO()).thenReturn(dpPoiDTO);
    // Method method = PowerMockito.method(DealGroupReserveInfoProcessor.class, "poiWhiteListJudge");
    // boolean result = (boolean) method.invoke(dealGroupReserveInfoProcessor,ctx);
    // assert result;
    // }
    @Test
    public void testGetDouHuModuleAbConfig() throws InvocationTargetException, IllegalAccessException {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setKey("MtWashReserveExp");
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("expId");
        abConfig.setExpResult("c");
        moduleAbConfig.setConfigs(Lists.newArrayList(abConfig));
        when(douHuBiz.getAbExpResult(ctx, "MtWashReserveExp")).thenReturn(moduleAbConfig);
        Method method = PowerMockito.method(DealGroupReserveInfoProcessor.class, "getDouHuModuleAbConfig");
        method.invoke(dealGroupReserveInfoProcessor, ctx, "MtWashReserveExp", "DpWashReserveExp");
        assertNotNull(ctx.getModuleAbConfigs());
        ctx.setModuleAbConfigs(null);
        method.invoke(dealGroupReserveInfoProcessor, ctx, "MtWashReserveExp", "DpWashReserveExp");
        assertNotNull(ctx.getModuleAbConfigs());
    }

    /**
     * Test Scenario 1.1: Both shopSupportReserve and expResult are not null,
     * shopSupportReserve is true and expResult is "b".
     */
    @Test
    public void testIsShowReserveEntrance_BothNotNull_ShopSupportTrue_ExpResultB() throws Throwable {
        // arrange
        Boolean shopSupportReserve = true;
        String expResult = "b";
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertTrue(result);
    }

    /**
     * Test Scenario 1.2: Both shopSupportReserve and expResult are not null,
     * shopSupportReserve is true and expResult is not "b".
     */
    @Test
    public void testIsShowReserveEntrance_BothNotNull_ShopSupportTrue_ExpResultNotB() throws Throwable {
        // arrange
        Boolean shopSupportReserve = true;
        String expResult = "c";
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test Scenario 1.3: Both shopSupportReserve and expResult are not null,
     * shopSupportReserve is false and expResult is "b".
     */
    @Test
    public void testIsShowReserveEntrance_BothNotNull_ShopSupportFalse_ExpResultB() throws Throwable {
        // arrange
        Boolean shopSupportReserve = false;
        String expResult = "b";
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test Scenario 1.4: Both shopSupportReserve and expResult are not null,
     * shopSupportReserve is false and expResult is not "b".
     */
    @Test
    public void testIsShowReserveEntrance_BothNotNull_ShopSupportFalse_ExpResultNotB() throws Throwable {
        // arrange
        Boolean shopSupportReserve = false;
        String expResult = "c";
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test Scenario 2.1: shopSupportReserve is not null, expResult is null,
     * shopSupportReserve is true.
     */
    @Test
    public void testIsShowReserveEntrance_ShopSupportNotNull_ExpResultNull_ShopSupportTrue() throws Throwable {
        // arrange
        Boolean shopSupportReserve = true;
        String expResult = null;
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertTrue(result);
    }

    /**
     * Test Scenario 2.2: shopSupportReserve is not null, expResult is null,
     * shopSupportReserve is false.
     */
    @Test
    public void testIsShowReserveEntrance_ShopSupportNotNull_ExpResultNull_ShopSupportFalse() throws Throwable {
        // arrange
        Boolean shopSupportReserve = false;
        String expResult = null;
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test Scenario 3: Both shopSupportReserve and expResult are null.
     */
    @Test
    public void testIsShowReserveEntrance_BothNull() throws Throwable {
        // arrange
        Boolean shopSupportReserve = null;
        String expResult = null;
        DealGroupReserveInfoProcessor processor = new DealGroupReserveInfoProcessor();
        // act
        boolean result = invokeIsShowReserveEntrance(processor, shopSupportReserve, expResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test when moduleAbConfigs is null
     */
    @Test
    public void testGetExpResult_WhenModuleAbConfigsIsNull() throws Throwable {
        // arrange
        when(ctx.getModuleAbConfigs()).thenReturn(null);
        // act
        String result = invokePrivateGetExpResult(ctx, "mtKey", "dpKey");
        // assert
        assertNull(result);
    }

    /**
     * Test when moduleAbConfigs is empty
     */
    @Test
    public void testGetExpResult_WhenModuleAbConfigsIsEmpty() throws Throwable {
        // arrange
        when(ctx.getModuleAbConfigs()).thenReturn(new ArrayList<>());
        // act
        String result = invokePrivateGetExpResult(ctx, "mtKey", "dpKey");
        // assert
        assertNull(result);
    }

    /**
     * Test when moduleAbConfigs contains null config
     */
    @Test
    public void testGetExpResult_WhenModuleAbConfigsContainsNullConfig() throws Throwable {
        // arrange
        List<ModuleAbConfig> configs = Collections.singletonList(null);
        when(ctx.getModuleAbConfigs()).thenReturn(configs);
        // act
        String result = invokePrivateGetExpResult(ctx, "mtKey", "dpKey");
        // assert
        assertNull(result);
    }

    /**
     * Test when no matching key found
     */
    @Test
    public void testGetExpResult_WhenNoMatchingKeyFound() throws Throwable {
        // arrange
        ModuleAbConfig config = new ModuleAbConfig();
        config.setKey("otherKey");
        when(ctx.getModuleAbConfigs()).thenReturn(Collections.singletonList(config));
        when(ctx.isMt()).thenReturn(true);
        // act
        String result = invokePrivateGetExpResult(ctx, "mtKey", "dpKey");
        // assert
        assertNull(result);
    }

    /**
     * Test when matching key found but configs is empty
     */
    @Test
    public void testGetExpResult_WhenMatchingKeyFoundButConfigsEmpty() throws Throwable {
        // arrange
        ModuleAbConfig config = new ModuleAbConfig();
        config.setKey("mtKey");
        config.setConfigs(new ArrayList<>());
        when(ctx.getModuleAbConfigs()).thenReturn(Collections.singletonList(config));
        when(ctx.isMt()).thenReturn(true);
        // act
        String result = invokePrivateGetExpResult(ctx, "mtKey", "dpKey");
        // assert
        assertNull(result);
    }

    /**
     * Test when matching key found but first config is null
     */
    @Test
    public void testGetExpResult_WhenMatchingKeyFoundButFirstConfigNull() throws Throwable {
        // arrange
        ModuleAbConfig config = new ModuleAbConfig();
        config.setKey("mtKey");
        config.setConfigs(Collections.singletonList(null));
        when(ctx.getModuleAbConfigs()).thenReturn(Collections.singletonList(config));
        when(ctx.isMt()).thenReturn(true);
        // act
        String result = invokePrivateGetExpResult(ctx, "mtKey", "dpKey");
        // assert
        assertNull(result);
    }

    /**
     * Test successful MT key match with valid config
     */
    @Test
    public void testGetExpResult_WhenMtKeyMatchWithValidConfig() throws Throwable {
        // arrange
        ModuleAbConfig config = new ModuleAbConfig();
        config.setKey("mtKey");
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("mtResult");
        config.setConfigs(Collections.singletonList(abConfig));
        when(ctx.getModuleAbConfigs()).thenReturn(Collections.singletonList(config));
        when(ctx.isMt()).thenReturn(true);
        // act
        String result = invokePrivateGetExpResult(ctx, "mtKey", "dpKey");
        // assert
        assertEquals("mtResult", result);
    }

    /**
     * Test successful DP key match with valid config
     */
    @Test
    public void testGetExpResult_WhenDpKeyMatchWithValidConfig() throws Throwable {
        // arrange
        ModuleAbConfig config = new ModuleAbConfig();
        config.setKey("dpKey");
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("dpResult");
        config.setConfigs(Collections.singletonList(abConfig));
        when(ctx.getModuleAbConfigs()).thenReturn(Collections.singletonList(config));
        when(ctx.isMt()).thenReturn(false);
        // act
        String result = invokePrivateGetExpResult(ctx, "mtKey", "dpKey");
        // assert
        assertEquals("dpResult", result);
    }
}
