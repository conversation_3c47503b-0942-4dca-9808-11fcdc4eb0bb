package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.PassParamBuilderService;
import com.google.common.collect.Lists;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomDistributionInfo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/12/18 17:29
 */
@RunWith(MockitoJUnitRunner.class)
public class PassParamBuilderServiceTest {
    
    @InjectMocks
    private PassParamBuilderService passParamBuilderService; 
    
    @Test
    public void testPassParamBuilderService(){
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);
        DealCtx ctx = new DealCtx(envCtx);
        LiveRoomDistributionInfo liveRoomDistributionInfo = new LiveRoomDistributionInfo();
        liveRoomDistributionInfo.setEncodedPassParam("a");
        ctx.setLiveRoomDistributionInfo(liveRoomDistributionInfo);
        ctx.isMtLiveMinApp();
        DealGroupPBO result = new DealGroupPBO();
        List<DealBuyBtn> buyBtns = Lists.newArrayList();
        DealBuyBtn btn = new DealBuyBtn(true, "");
        btn.setRedirectUrl("url");
        buyBtns.add(btn);
        DealBuyBar buyBar = new DealBuyBar(1, buyBtns);
        result.setBuyBar(buyBar);
        passParamBuilderService.buildPassParam(ctx, result);
        Assert.assertTrue(result.getBuyBar().getBuyBtns().get(0).getRedirectUrl().contains("pass_param"));
    }
}
