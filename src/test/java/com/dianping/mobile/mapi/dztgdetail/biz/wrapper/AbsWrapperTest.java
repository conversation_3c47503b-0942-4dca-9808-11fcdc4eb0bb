package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.faulttolerance.utils.FaultToleranceUtils;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AbsWrapperTest {

    @Mock
    private Future future;

    private AbsWrapper absWrapper = new AbsWrapper() {
    };

    @Mock
    private Function<List<String>, Future> function;

    /**
     * 测试 getFutureResult 方法，当 serviceFuture 为 null 时
     */
    @Test
    public void testGetFutureResultWhenServiceFutureIsNull() throws Throwable {
        // arrange
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        // act
        Object result = absWrapper.getFutureResult(1, null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getFutureResult 方法，当 serviceFuture 不为 null 时
     */
    @Test
    public void testGetFutureResultWhenServiceFutureIsNotNull() throws Throwable {
        // arrange
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        when(future.get()).thenReturn("test");
        // act
        Object result = absWrapper.getFutureResult(1, future);
        // assert
        assertEquals("test", result);
    }

    /**
     * 测试 getFutureResult 方法，当 serviceFuture.get() 抛出异常时
     */
    @Test
    public void testGetFutureResultWhenServiceFutureGetThrowsException() throws Throwable {
        // arrange
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        when(future.get()).thenThrow(new InterruptedException());
        // act
        Object result = absWrapper.getFutureResult(1, future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getFutureResult 方法，当 serviceFuture 为 null 时
     */
    @Test
    public void testGetFutureResultWhenFutureIsNull() throws Throwable {
        // arrange
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        Future serviceFuture = null;
        String className = "TestClassName";
        // act
        Object result = absWrapper.getFutureResult(serviceFuture, className);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getFutureResult 方法，当 serviceFuture 不为 null 时
     */
    @Test
    public void testGetFutureResultWhenFutureIsNotNull() throws Throwable {
        // arrange
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        when(future.get()).thenReturn("test");
        String className = "TestClassName";
        // act
        Object result = absWrapper.getFutureResult(future, className);
        // assert
        assertEquals("test", result);
    }

    /**
     * 测试 getFutureResult 方法，当 serviceFuture.get() 抛出异常时
     */
    @Test
    public void testGetFutureResultWhenFutureGetThrowsException() throws Throwable {
        // arrange
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        when(future.get()).thenThrow(new InterruptedException());
        String className = "TestClassName";
        // act
        Object result = absWrapper.getFutureResult(future, className);
        // assert
        assertNull(result);
    }

    @Test
    public void testBatchQueryMapEmptyRequests() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        List<Object> requests = Lists.newArrayList();
        Function<Object, Future> function = mock(Function.class);
        Map<Object, Object> result = absWrapper.batchQueryMap(requests, function);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBatchQueryMapAllFuturesNull() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        List<Object> requests = Lists.newArrayList("request1", "request2");
        Function<Object, Future> function = mock(Function.class);
        when(function.apply(any())).thenReturn(null);
        Map<Object, Object> result = absWrapper.batchQueryMap(requests, function);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBatchQueryMapSomeFuturesNull() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        List<Object> requests = Lists.newArrayList("request1", "request2");
        Function<Object, Future> function = mock(Function.class);
        when(function.apply("request1")).thenReturn(null);
        when(function.apply("request2")).thenReturn(future);
        when(absWrapper.getFutureResult(future)).thenReturn("result2");
        Map<Object, Object> result = absWrapper.batchQueryMap(requests, function);
        assertEquals(1, result.size());
        assertEquals("result2", result.get("request2"));
    }

    @Test
    public void testBatchQueryMapAllFuturesNotNull() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        List<Object> requests = Lists.newArrayList("request1", "request2");
        Function<Object, Future> function = mock(Function.class);
        when(function.apply("request1")).thenReturn(future);
        when(function.apply("request2")).thenReturn(future);
        when(absWrapper.getFutureResult(future)).thenReturn("result1", "result2");
        Map<Object, Object> result = absWrapper.batchQueryMap(requests, function);
        assertEquals(2, result.size());
        assertEquals("result1", result.get("request1"));
        assertEquals("result2", result.get("request2"));
    }

    @Test(expected = RuntimeException.class)
    public void testBatchQueryMapGetFutureResultThrowsException() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        List<Object> requests = Lists.newArrayList("request1");
        Function<Object, Future> function = mock(Function.class);
        doThrow(new RuntimeException()).when(absWrapper).getFutureResult(future);
        absWrapper.batchQueryMap(requests, function);
    }

    /**
     * 测试preBatchQuery方法，当requests列表为空时
     */
    @Test
    public void testPreBatchQueryWhenRequestsIsEmpty() throws Throwable {
        // arrange
        List<Object> requests = Arrays.asList();
        Function<Object, Future> function = mock(Function.class);
        // act
        List<Future> result = absWrapper.preBatchQuery(requests, function);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试preBatchQuery方法，当function对所有元素都返回null时
     */
    @Test
    public void testPreBatchQueryWhenFunctionReturnsNullForAllElements() throws Throwable {
        // arrange
        List<Object> requests = Arrays.asList(new Object(), new Object());
        Function<Object, Future> function = mock(Function.class);
        when(function.apply(any())).thenReturn(null);
        // act
        List<Future> result = absWrapper.preBatchQuery(requests, function);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试preBatchQuery方法，当function对某些元素返回null，对某些元素返回非null时
     */
    @Test
    public void testPreBatchQueryWhenFunctionReturnsNonNullForSomeElements() throws Throwable {
        // arrange
        List<Object> requests = Arrays.asList(new Object(), new Object());
        Function<Object, Future> function = mock(Function.class);
        when(function.apply(any())).thenReturn(null, mock(Future.class));
        // act
        List<Future> result = absWrapper.preBatchQuery(requests, function);
        // assert
        assertEquals(1, result.size());
    }

    /**
     * 测试preBatchQuery方法，当function对所有元素都返回非null时
     */
    @Test
    public void testPreBatchQueryWhenFunctionReturnsNonNullForAllElements() throws Throwable {
        // arrange
        List<Object> requests = Arrays.asList(new Object(), new Object());
        Function<Object, Future> function = mock(Function.class);
        when(function.apply(any())).thenReturn(mock(Future.class));
        // act
        List<Future> result = absWrapper.preBatchQuery(requests, function);
        // assert
        assertEquals(2, result.size());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPreBatchQueryMaxQuerySizeNotPositive() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        absWrapper.preBatchQuery(Lists.newArrayList(), 0, function);
    }

    @Test
    public void testPreBatchQueryRequestsEmpty() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        List<Future> result = absWrapper.preBatchQuery(Lists.newArrayList(), 1, function);
        assertNull(result);
    }

    @Test
    public void testPreBatchQueryAllFuturesNotNull() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        List<String> requests = Lists.newArrayList("request1", "request2");
        Future future1 = mock(Future.class);
        Future future2 = mock(Future.class);
        when(function.apply(ArgumentMatchers.anyList())).thenReturn(future1, future2);
        List<Future> result = absWrapper.preBatchQuery(requests, 1, function);
        assertEquals(2, result.size());
        assertEquals(future1, result.get(0));
        assertEquals(future2, result.get(1));
    }

    @Test
    public void testPreBatchQuerySomeFuturesNull() throws Throwable {
        AbsWrapper absWrapper = new AbsWrapper() {
        };
        List<String> requests = Lists.newArrayList("request1", "request2");
        Future future1 = mock(Future.class);
        when(function.apply(ArgumentMatchers.anyList())).thenReturn(future1, null);
        List<Future> result = absWrapper.preBatchQuery(requests, 1, function);
        assertEquals(1, result.size());
        assertEquals(future1, result.get(0));
    }
}
