package com.dianping.mobile.mapi.dztgdetail.rcf.repository.pagetype;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.sankuai.dz.product.detail.gateway.api.page.type.ProductDetailPageTypeService;
import com.sankuai.dz.product.detail.gateway.api.page.type.response.PageTypeResponse;
import com.sankuai.dz.product.detail.gateway.api.page.type.request.PageTypeRequest;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ShoppingGuideProductInfoServiceTest {

    @Mock
    private DealNativeSnapshotReq mockRequest;

    @Mock
    private EnvCtx mockEnvCtx;

    private final ShoppingGuideProductInfoService service = new ShoppingGuideProductInfoService();

    @Mock
    private ProductDetailPageTypeService productInfoService;

    @InjectMocks
    private ShoppingGuideProductInfoService shoppingGuideProductInfoService;

    @Test
    public void testBuildRequestForMtPlatform() throws Throwable {
        when(mockRequest.getDealGroupId()).thenReturn(12345);
        when(mockEnvCtx.isMt()).thenReturn(true);
        when(mockEnvCtx.getPragmaToken()).thenReturn("mt-token");
        when(mockEnvCtx.getUuid()).thenReturn("mt-uuid");
        when(mockEnvCtx.getUnionId()).thenReturn("mt-union");
        when(mockEnvCtx.getUserAgent()).thenReturn("mt-agent");
        PageTypeRequest result = service.buildRequest(mockRequest, mockEnvCtx);
        assertEquals(12345, result.getProductId());
        Map<String, String> headers = result.getHeaderMap();
        assertEquals("mt-token", headers.get("pragma-token"));
        assertEquals("mt-uuid", headers.get("pragma-uuid"));
        assertEquals("mt-union", headers.get("pragma-unionid"));
        assertEquals("mt-agent", headers.get("user-agent"));
        assertNull(headers.get("pragma-dpid"));
    }

    @Test
    public void testBuildRequestForDpPlatform() throws Throwable {
        when(mockRequest.getDealGroupId()).thenReturn(67890);
        when(mockEnvCtx.isMt()).thenReturn(false);
        when(mockEnvCtx.getPragmaToken()).thenReturn("dp-token");
        when(mockEnvCtx.getDpId()).thenReturn("dp-id");
        when(mockEnvCtx.getUnionId()).thenReturn("dp-union");
        when(mockEnvCtx.getUserAgent()).thenReturn("dp-agent");
        PageTypeRequest result = service.buildRequest(mockRequest, mockEnvCtx);
        assertEquals(67890, result.getProductId());
        Map<String, String> headers = result.getHeaderMap();
        assertEquals("dp-token", headers.get("pragma-token"));
        assertEquals("dp-id", headers.get("pragma-dpid"));
        assertEquals("dp-union", headers.get("pragma-unionid"));
        assertEquals("dp-agent", headers.get("user-agent"));
        assertNull(headers.get("pragma-uuid"));
    }

    @Test(expected = NullPointerException.class)
    public void testBuildRequestWithNullRequest() throws Throwable {
        service.buildRequest(null, mockEnvCtx);
    }

    @Test(expected = NullPointerException.class)
    public void testBuildRequestWithNullEnvCtx() throws Throwable {
        service.buildRequest(mockRequest, null);
    }

    @Test(expected = NullPointerException.class)
    public void testBuildRequestWithNullDealGroupId() throws Throwable {
        when(mockRequest.getDealGroupId()).thenReturn(null);
        service.buildRequest(mockRequest, mockEnvCtx);
    }

    @Test
    public void testBuildRequestWithNullHeaderFields() throws Throwable {
        when(mockRequest.getDealGroupId()).thenReturn(11111);
        when(mockEnvCtx.isMt()).thenReturn(true);
        when(mockEnvCtx.getPragmaToken()).thenReturn(null);
        when(mockEnvCtx.getUuid()).thenReturn(null);
        when(mockEnvCtx.getUnionId()).thenReturn(null);
        when(mockEnvCtx.getUserAgent()).thenReturn(null);
        PageTypeRequest result = service.buildRequest(mockRequest, mockEnvCtx);
        PageTypeResponse response = null;
        service.getPageType(response);
        response = new PageTypeResponse("deal");
        service.getPageType(response);
        assertEquals(11111, result.getProductId());
        Map<String, String> headers = result.getHeaderMap();
        assertNull(headers.get("pragma-token"));
        assertNull(headers.get("pragma-uuid"));
        assertNull(headers.get("pragma-unionid"));
        assertNull(headers.get("user-agent"));
    }

    @Test
    public void testIsDealWhenQueryPageTypeReturnsDeal() throws Throwable {
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        request.setDealGroupId(123);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setPragmaToken("test-token");
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setUserAgent("test-agent");
        envCtx.setUnionId("test-union");
        when(productInfoService.queryPageType(any(PageTypeRequest.class))).thenReturn(new PageTypeResponse("deal"));
        boolean result = shoppingGuideProductInfoService.isNewDeal(request, envCtx);
        assertTrue(result);
        verify(productInfoService).queryPageType(any(PageTypeRequest.class));
    }

    @Test
    public void testIsDealWhenQueryPageTypeReturnsNonDeal() throws Throwable {
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        request.setDealGroupId(123);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setPragmaToken("test-token");
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        envCtx.setUserAgent("test-agent");
        envCtx.setUnionId("test-union");
        when(productInfoService.queryPageType(any(PageTypeRequest.class))).thenReturn(new PageTypeResponse("other"));
        boolean result = shoppingGuideProductInfoService.isNewDeal(request, envCtx);
        assertFalse(result);
        verify(productInfoService).queryPageType(any(PageTypeRequest.class));
    }

    @Test
    public void testIsDealWhenQueryPageTypeThrowsException() throws Throwable {
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        request.setDealGroupId(123);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setPragmaToken("test-token");
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setUserAgent("test-agent");
        envCtx.setUnionId("test-union");
        when(productInfoService.queryPageType(any(PageTypeRequest.class))).thenThrow(new RuntimeException("test exception"));
        boolean result = shoppingGuideProductInfoService.isNewDeal(request, envCtx);
        assertFalse(result);
        verify(productInfoService).queryPageType(any(PageTypeRequest.class));
    }

    @Test
    public void testIsDealWithMtClientBuildsCorrectHeader() throws Throwable {
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        request.setDealGroupId(123);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setPragmaToken("mt-token");
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setUuid("mt-uuid");
        envCtx.setUserAgent("mt-agent");
        envCtx.setUnionId("mt-union");
        when(productInfoService.queryPageType(any(PageTypeRequest.class))).thenReturn(new PageTypeResponse("deal"));
        boolean result = shoppingGuideProductInfoService.isNewDeal(request, envCtx);
        assertTrue(result);
        verify(productInfoService).queryPageType(argThat(req -> {
            Map<String, String> headers = req.getHeaderMap();
            return headers != null && "mt-token".equals(headers.get("pragma-token")) && "mt-uuid".equals(headers.get("pragma-uuid")) && "mt-union".equals(headers.get("pragma-unionid")) && "mt-agent".equals(headers.get("user-agent")) && !headers.containsKey("pragma-dpid");
        }));
    }

    @Test
    public void testIsDealWithDpClientBuildsCorrectHeader() throws Throwable {
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        request.setDealGroupId(123);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setPragmaToken("dp-token");
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        envCtx.setDpId("dp-id");
        envCtx.setUserAgent("dp-agent");
        envCtx.setUnionId("dp-union");
        when(productInfoService.queryPageType(any(PageTypeRequest.class))).thenReturn(new PageTypeResponse("deal"));
        boolean result = shoppingGuideProductInfoService.isNewDeal(request, envCtx);
        assertTrue(result);
        verify(productInfoService).queryPageType(argThat(req -> {
            Map<String, String> headers = req.getHeaderMap();
            return headers != null && "dp-token".equals(headers.get("pragma-token")) && "dp-id".equals(headers.get("pragma-dpid")) && "dp-union".equals(headers.get("pragma-unionid")) && "dp-agent".equals(headers.get("user-agent")) && !headers.containsKey("pragma-uuid");
        }));
    }
}
