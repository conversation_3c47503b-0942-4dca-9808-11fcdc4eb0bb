package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.poi.dto.DpInfoDTO;
import com.dianping.poi.dto.RegionAllDTO;
import com.dianping.poi.mtDto.MtLocationDTO;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RgcServiceWrapperGetDpDistrictByFutureTest {

    @Mock
    private Future future;

    private RgcServiceWrapper rgcServiceWrapper;

    @Before
    public void setUp() {
        rgcServiceWrapper = new RgcServiceWrapper();
    }

    /**
     * 测试 future 为 null 的情况
     */
    @Test
    public void testGetDpDistrictByFutureFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        DpInfoDTO result = rgcServiceWrapper.getDpDistrictByFuture(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 不为 null，且 getFutureResult 方法返回 null 的情况
     */
    @Test
    public void testGetDpDistrictByFutureFutureResultIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        DpInfoDTO result = rgcServiceWrapper.getDpDistrictByFuture(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 不为 null，且 getFutureResult 方法返回非 null 的 DpInfoDTO 对象的情况
     */
    @Test
    public void testGetDpDistrictByFutureFutureResultIsNotNull() throws Throwable {
        // arrange
        DpInfoDTO dpInfoDTO = new DpInfoDTO();
        when(future.get()).thenReturn(dpInfoDTO);
        // act
        DpInfoDTO result = rgcServiceWrapper.getDpDistrictByFuture(future);
        // assert
        assertEquals(dpInfoDTO, result);
    }

    /**
     * 测试 future 为 null 的情况
     */
    @Test
    public void testGetDpRegionByFutureFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        RegionAllDTO result = rgcServiceWrapper.getDpRegionByFuture(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 不为 null，且 future.get() 能够正常返回 RegionAllDTO 对象的情况
     */
    @Test
    public void testGetDpRegionByFutureFutureIsNotNullAndGetWorks() throws Throwable {
        // arrange
        when(future.get()).thenReturn(new RegionAllDTO());
        // act
        RegionAllDTO result = rgcServiceWrapper.getDpRegionByFuture(future);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 future 不为 null，但 future.get() 抛出异常的情况
     */
    @Test
    public void testGetDpRegionByFutureFutureIsNotNullAndGetThrowsException() throws Throwable {
        // arrange
        when(future.get()).thenThrow(new InterruptedException());
        // act
        RegionAllDTO result = rgcServiceWrapper.getDpRegionByFuture(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 为 null 的情况
     */
    @Test
    public void testGetMtLocationByFutureFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        MtLocationDTO result = rgcServiceWrapper.getMtLocationByFuture(future);
        // assert
        assertNull(result);
    }

    /**
     * 测试 future 不为 null，且 future.get() 能够正常返回结果的情况
     */
    @Test
    public void testGetMtLocationByFutureFutureIsNotNullAndGetResultSuccess() throws Throwable {
        // arrange
        when(future.get()).thenReturn(new MtLocationDTO());
        // act
        MtLocationDTO result = rgcServiceWrapper.getMtLocationByFuture(future);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 future 不为 null，但 future.get() 抛出异常的情况
     */
    @Test
    public void testGetMtLocationByFutureFutureIsNotNullAndGetResultThrowException() throws Throwable {
        // arrange
        when(future.get()).thenThrow(new InterruptedException());
        // act
        MtLocationDTO result = rgcServiceWrapper.getMtLocationByFuture(future);
        // assert
        assertNull(result);
    }
}
