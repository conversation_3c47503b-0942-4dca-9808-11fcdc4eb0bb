package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.CleaningProductLionConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ImageTextStrategyRule;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ImageTextDetailHandlerTest {

    @InjectMocks
    private ImageTextDetailHandler imageTextDetailHandler;

    @Mock
    private ImageTextDetailStrategyFactory factory;

    @Mock
    private HaimaWrapper haimaWrapper;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
        lionMockedStatic.close();
    }

    /**
     * 测试dealGroupDTO和contents为空的情况
     */
    @Test
    public void testBuildImageTextDetailWithNullDealGroupDTOAndContents() {
        EnvCtx envCtx = new EnvCtx();
        ImageTextDetailPBO result = imageTextDetailHandler.buildImageTextDetail(null, null, envCtx, "source");
        assertNotNull("结果不应该为null", result);
    }

    /**
     * 测试contents为空的情况
     */
    @Test
    public void testBuildImageTextDetailWithNullContents() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        EnvCtx envCtx = new EnvCtx();
        ImageTextDetailPBO result = imageTextDetailHandler.buildImageTextDetail(dealGroupDTO, null, envCtx, "source");
        assertNotNull("结果不应该为null", result);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testBuildImageTextDetailWithValidInputs() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<ContentPBO> contents = new ArrayList<>();
        ContentPBO contentPBO = new ContentPBO(0, "测试文本");
        contents.add(contentPBO);
        EnvCtx envCtx = new EnvCtx();
        String pageSource = "source";

        ImageTextDetailStrategy strategyMock = mock(ImageTextDetailStrategy.class);
        when(factory.getImageTextDetailStrategy(anyString())).thenReturn(strategyMock);
        when(strategyMock.buildImageTextDetail(eq(dealGroupDTO), eq(contents), anyInt(), eq(envCtx))).thenReturn(new ImageTextDetailPBO());

        ImageTextStrategyRule rule = new ImageTextStrategyRule();
        rule.setStrategyName("expand");
        rule.setPageSource("source");
        rule.setPriority(1);
        rule.setThreshold(2);
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getImageTextStrategyRules)
                .thenReturn(Lists.newArrayList(rule));

        CleaningProductLionConfig config = new CleaningProductLionConfig();
        lionMockedStatic.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(config);


        ImageTextDetailPBO result = imageTextDetailHandler.buildImageTextDetail(dealGroupDTO, contents, envCtx, pageSource);

        assertNotNull("结果不应该为null", result);
    }
}
