package com.dianping.mobile.mapi.dztgdetail.rcf.domian;

import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.bff.cache.response.DealBffCacheQueryResponse;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto.DealBffResponseDTO;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.dealbaseinfo.DealCategoryCacheService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.deallayout.DealLayoutService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.switcher.DealRcfSwitcherService;
import com.dianping.mobile.mapi.dztgdetail.rcf.repository.switcher.dto.DealRcfSwitcherResult;
import com.meituan.dorado.common.util.JacksonUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class DealNativeSnapshotMainProcessorTest {

    @InjectMocks
    private DealNativeSnapshotMainProcessor processor;

    @Mock
    private DealRcfSwitcherService dealRcfSwitcher;

    @Mock
    private DealLayoutService dealLayoutService;
    @Mock
    private DealCategoryCacheService dealCategoryCacheService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试dealGroupId为null时返回默认快照
     */
    @Test
    public void testProcessDealGroupIdIsNull() {
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        EnvCtx envCtx = new EnvCtx();

        DealNativeSnapshotMainProcessor.Response result = processor.process(request, envCtx);

        assertFalse(result.getDealRcfNativeSnapshot().getDealFlexBoxConfig().isRender());
    }

    /**
     * 测试dealGroupId小于等于0时返回默认快照
     */
    @Test
    public void testProcessDealGroupIdIsInvalid() {
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        request.setDealGroupId(-1);
        EnvCtx envCtx = new EnvCtx();

        DealNativeSnapshotMainProcessor.Response result = processor.process(request, envCtx);

        assertFalse(result.getDealRcfNativeSnapshot().getDealFlexBoxConfig().isRender());
    }

    /**
     * 测试dealGroupCategoryDTO为null时返回默认快照
     */
    @Test
    public void testProcessDealGroupCategoryDTONull() throws TException {
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        request.setDealGroupId(1);
        EnvCtx envCtx = new EnvCtx();

        when(dealCategoryCacheService.get(anyInt(), anyBoolean())).thenReturn(null);

        DealNativeSnapshotMainProcessor.Response result = processor.process(request, envCtx);

        assertFalse(result.getDealRcfNativeSnapshot().getDealFlexBoxConfig().isRender());
    }

    /**
     * 测试dealRcfSwitcherResult.isRender为false时返回默认快照
     */
    @Test
    public void testProcessDealRcfSwitcherResultIsRenderFalse() throws TException {
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        request.setDealGroupId(1);
        EnvCtx envCtx = new EnvCtx();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(1L);

        when(dealCategoryCacheService.get(anyInt(), anyBoolean())).thenReturn(dealGroupCategoryDTO);
        when(dealRcfSwitcher.get(anyLong(),anyLong(), any(), anyLong(), anyString(), anyString())).thenReturn(new DealRcfSwitcherResult(false, false, "111"));

        DealNativeSnapshotMainProcessor.Response result = processor.process(request, envCtx);

        assertFalse(result.getDealRcfNativeSnapshot().getDealFlexBoxConfig().isRender());
    }

    /**
     * 测试方法抛出异常时返回默认快照
     */
    @Test
    public void testProcessThrowsException() throws TException {
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        request.setDealGroupId(1);
        EnvCtx envCtx = new EnvCtx();

        when(dealCategoryCacheService.get(anyInt(), anyBoolean())).thenThrow(RuntimeException.class);

        DealNativeSnapshotMainProcessor.Response result = processor.process(request, envCtx);

        assertFalse(result.getDealRcfNativeSnapshot().getDealFlexBoxConfig().isRender());
    }

    @Test
    public void testProcessThrowsException2() throws TException, IOException {
        String json= "{\"@class\":\"com.dianping.deal.bff.cache.response.DealBffCacheQueryResponse\",\"cacheDataMap\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dzgoodreview\\\"}\":\"{\\\"userIcons\\\":[\\\"https://p0.meituan.net/travelcube/34c81dbcfc46b64d8148edfe8d7a8d5e7896.png\\\"],\\\"reviewScore\\\":0,\\\"goodReviewRatio\\\":\\\"\\\"}\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"queryexposureresources\\\"}\":\"{\\\"code\\\":\\\"000000\\\",\\\"data\\\":[],\\\"desc\\\":\\\"成功\\\"}\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"leadbuycardtips\\\"}\":\"{\\\"code\\\":200,\\\"flag\\\":0,\\\"icon\\\":0,\\\"title\\\":\\\"提示\\\",\\\"content\\\":\\\"未查询到卡信息\\\",\\\"statusCode\\\":{\\\"code\\\":200}}\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"activity\\\"}\":\"{\\\"success\\\":true}\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dzdealstyle\\\"}\":\"{\\\"moduleConfigsModule\\\":{\\\"isDzx\\\":true,\\\"isDpOrder\\\":true,\\\"generalInfo\\\":\\\"card_style_v2\\\",\\\"isTort\\\":false,\\\"key\\\":\\\"default\\\",\\\"extraInfo\\\":\\\"newtuandeal\\\"}}\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"timescardmiddleinfo\\\"}\":\"{}\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"queryproductranklabel\\\"}\":\"{\\\"rankType\\\":0,\\\"ranking\\\":0}\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"dzdealbase\\\"}\":\"{\\\"hasReserveEntrance\\\":false,\\\"serviceType\\\":\\\"儿童补牙\\\",\\\"pnPurchaseNoteDTO\\\":{\\\"subTitle\\\":\\\"周一至周日可用\\\",\\\"pnTitle\\\":\\\"购买须知\\\",\\\"pnModules\\\":[{\\\"pnIcon\\\":\\\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\\\",\\\"pnModuleName\\\":\\\"适用时间\\\",\\\"pnItems\\\":[{\\\"pnItemValues\\\":[{\\\"pnValue\\\":\\\"购买后90天内有效\\\",\\\"pnType\\\":1}],\\\"pnItemName\\\":\\\"有效时间\\\"}]},{\\\"pnIcon\\\":\\\"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\\\",\\\"pnModuleName\\\":\\\"预约规则\\\",\\\"pnItems\\\":[{\\\"pnItemValues\\\":[{\\\"pnValue\\\":\\\"无需预约，如遇消费高峰时段您可能需要排队\\\",\\\"pnType\\\":1}],\\\"pnItemName\\\":\\\"\\\"}]},{\\\"pnIcon\\\":\\\"https://p0.meituan.net/travelcube/1f18aa2c0100e75060daf348283f3c861351.png\\\",\\\"pnModuleName\\\":\\\"适用人数\\\",\\\"pnItems\\\":[{\\\"pnItemValues\\\":[{\\\"pnValue\\\":\\\"每张团购券不限使用人数\\\",\\\"pnType\\\":1}],\\\"pnItemName\\\":\\\"\\\"}]},{\\\"pnIcon\\\":\\\"https://p0.meituan.net/travelcube/2d571b1ec9fc46bfa1bf41a1801f57fc1608.png\\\",\\\"pnModuleName\\\":\\\"适用人群\\\",\\\"pnItems\\\":[{\\\"pnItemValues\\\":[{\\\"pnValue\\\":\\\"仅儿童可用\\\",\\\"pnType\\\":1}],\\\"pnItemName\\\":\\\"适用人群\\\"},{\\\"pnItemValues\\\":[{\\\"pnValue\\\":\\\"详情请咨询在线客服\\\",\\\"pnType\\\":1}],\\\"pnItemName\\\":\\\"禁忌人群\\\"}]},{\\\"pnIcon\\\":\\\"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\\\",\\\"pnModuleName\\\":\\\"其他规则\\\",\\\"pnItems\\\":[{\\\"pnItemValues\\\":[{\\\"pnValue\\\":\\\"不再与其他优惠同享\\\",\\\"pnType\\\":1}],\\\"pnItemName\\\":\\\"\\\"}]},{\\\"pnIcon\\\":\\\"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\\\",\\\"pnModuleName\\\":\\\"温馨提示\\\",\\\"pnItems\\\":[{\\\"pnItemValues\\\":[{\\\"pnValue\\\":\\\"如需团购券发票，请您在消费时向商户咨询\\\",\\\"pnType\\\":1},{\\\"pnValue\\\":\\\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\\\",\\\"pnType\\\":1}],\\\"pnItemName\\\":\\\"\\\"}]}]},\\\"type\\\":0,\\\"mtId\\\":1024835476,\\\"features\\\":[\\\"随时退\\\",\\\"过期退\\\"],\\\"specialFeatures\\\":[],\\\"titleTagIcon\\\":\\\"\\\",\\\"userCardState\\\":4,\\\"isStandardDealGroup\\\":false,\\\"adModule\\\":{\\\"mtDealGroupId\\\":1024835476,\\\"dealID\\\":0,\\\"shopId\\\":*********},\\\"skuId\\\":\\\"*********\\\",\\\"featuresLayer\\\":{\\\"layerConfigs\\\":[{\\\"icon\\\":\\\"https://p0.meituan.net/ingee/2860c04f209c5ebe48ad7e05a726de711937.png\\\",\\\"textType\\\":0,\\\"title\\\":\\\"随时退·过期退\\\",\\\"type\\\":1,\\\"jumpUrl\\\":\\\"https://shangou.meituan.net/v1/mss_24c1e05b968a4937bf34e2f4ff68639e/shangou-fe-maker-html/sg/html/1611211020310_d5e044/index.html\\\",\\\"desc\\\":\\\"未消费随时退款、过期未消费自动退款。\\\"}]},\\\"meetPurchaseLimit\\\":false,\\\"dpDealId\\\":*********,\\\"saleDesc\\\":\\\"已售37\\\",\\\"subTitleList\\\":[],\\\"displayPriceDesc\\\":\\\"门市价\\\",\\\"skuModule\\\":{\\\"skuAttrCnNameList\\\":[],\\\"skuAttrValueList\\\":[],\\\"url\\\":\\\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=group-order-submit&mrn_component=GroupOrderSubmit&dealid=1024835476&shopid=*********&shopidEncrypt=h1yj567a2b515a4c4b2c117f8dc68f876c0119c1ebf0603c4c78dec03d58439ed5ebf0e159013c8379a57c3962185517h7fh&pagesource=dealGroupDetail&is_sku=0\\\"},\\\"reminderExtend\\\":[],\\\"ssrExperimentEnabled\\\":false,\\\"businessFigure\\\":\\\"\\\",\\\"moduleConfigsModule\\\":{\\\"isDzx\\\":true,\\\"isDpOrder\\\":true,\\\"generalInfo\\\":\\\"card_style_v2\\\",\\\"isTort\\\":false,\\\"key\\\":\\\"default\\\",\\\"extraInfo\\\":\\\"newtuandeal\\\"},\\\"responseTimestamp\\\":1733145830909,\\\"structedDetails\\\":[{\\\"name\\\":\\\"<div>\\\\n<div class=\\\\\\\"detail-tit\\\\\\\"></div>\\\\n<table width=\\\\\\\"100%\\\\\\\" cellpadding=\\\\\\\"0\\\\\\\" cellspacing=\\\\\\\"0\\\\\\\" class=\\\\\\\"detail-table\\\\\\\">\\\\n\\\\t<thead>\\\\n\\\\t<tr>\\\\n\\\\t\\\\t<th width=\\\\\\\"50%\\\\\\\">名称</th>\\\\n\\\\t\\\\t<th width=\\\\\\\"25%\\\\\\\">数量</th>\\\\n\\\\t\\\\t<th width=\\\\\\\"25%\\\\\\\">价值</th>\\\\n\\\\t</tr>\\\\n\\\\t</thead>\\\\n\\\\t<tbody>\\\\n\\\\t\\\\t<tr>\\\\n            <td>挂号</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">1份</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">20.0元</td>\\\\n\\\\t\\\\t</tr>\\\\n\\\\t\\\\t<tr>\\\\n            <td>口腔检查（医生看诊）</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">1份</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">20.0元</td>\\\\n\\\\t\\\\t</tr>\\\\n\\\\t\\\\t<tr>\\\\n            <td>小牙片</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">1份</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">20.0元</td>\\\\n\\\\t\\\\t</tr>\\\\n\\\\t\\\\t<tr>\\\\n            <td>口腔健康档案</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">1份</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">10.0元</td>\\\\n\\\\t\\\\t</tr>\\\\n\\\\t\\\\t<tr>\\\\n            <td>进口3M树脂补牙</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">1份</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">298.0元</td>\\\\n\\\\t\\\\t</tr>\\\\n\\\\t\\\\t<tr>\\\\n            <td>一次性医疗器械</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">1份</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">20.0元</td>\\\\n\\\\t\\\\t</tr>\\\\n\\\\t\\\\t<tr>\\\\n            <td>口腔健康指导</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">1份</td>\\\\n            <td class=\\\\\\\"tc\\\\\\\">10.0元</td>\\\\n\\\\t\\\\t</tr>\\\\n\\\\t<tr class=\\\\\\\"total\\\\\\\">\\\\n\\\\t\\\\t<td></td>\\\\n        <td class=\\\\\\\"tc\\\\\\\">总价<br><strong>团购价</strong></td>\\\\n\\\\t\\\\t<td class=\\\\\\\"tc\\\\\\\">39800元<br><strong>123元</strong></td>\\\\n\\\\t</tr>\\\\n\\\\t</tbody>\\\\n</table>\\\\n\\\\n\\\\n</div>\\\",\\\"id\\\":\\\"套餐\\\",\\\"iD\\\":\\\"套餐\\\",\\\"type\\\":1,\\\"isUsed\\\":false,\\\"key\\\":\\\"套餐\\\"},{\\\"subTitle\\\":\\\"周一至周日可用\\\",\\\"name\\\":\\\"<div>\\\\n<div class=\\\\\\\"detail-box\\\\\\\">\\\\n    <div class=\\\\\\\"purchase-notes\\\\\\\">\\\\n\\\\t\\\\t        <dl>\\\\n            <dt>有效期</dt>\\\\n            <dd>\\\\n                <p class=\\\\\\\"listitem\\\\\\\">\\\\n    购买后90天内有效\\\\n        </p>\\\\n            </dd>\\\\n        </dl>\\\\n        <dl>\\\\n            <dt>预约信息</dt>\\\\n            <dd>\\\\n                <p class=\\\\\\\"listitem\\\\\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\\\n            </dd>\\\\n        </dl>\\\\n        <dl>\\\\n            <dt>适用人数</dt>\\\\n            <dd>\\\\n                <p class=\\\\\\\"listitem\\\\\\\">每张团购券不限使用人数</p>\\\\n            </dd>\\\\n        </dl>\\\\n        <dl>\\\\n            <dt>适用人群</dt>\\\\n            <dd>\\\\n                <p class=\\\\\\\"listitem\\\\\\\">仅儿童可用</p>\\\\n            </dd>\\\\n        </dl>\\\\n        <dl>\\\\n            <dt>禁忌人群</dt>\\\\n            <dd>\\\\n                <p class=\\\\\\\"listitem\\\\\\\">详情请咨询在线客服</p>\\\\n            </dd>\\\\n        </dl>\\\\n        <dl>\\\\n            <dt>规则提醒</dt>\\\\n            <dd>\\\\n                <p class=\\\\\\\"listitem\\\\\\\">不再与其他优惠同享\\\\n</p>\\\\n            </dd>\\\\n        </dl>\\\\n        <dl>\\\\n            <dt>温馨提示</dt>\\\\n            <dd>\\\\n                <p class=\\\\\\\"listitem\\\\\\\">如需团购券发票，请您在消费时向商户咨询</p>\\\\n                <p class=\\\\\\\"listitem\\\\\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\\\n            </dd>\\\\n        </dl>\\\\n    </div>\\\\n</div>\\\\n\\\\n</div>\\\",\\\"id\\\":\\\"购买须知\\\",\\\"iD\\\":\\\"购买须知\\\",\\\"type\\\":2,\\\"isUsed\\\":false,\\\"key\\\":\\\"购买须知\\\"}],\\\"promoDetailModule\\\":{\\\"showPriceCompareEntrance\\\":false,\\\"marketPrice\\\":\\\"39800\\\",\\\"bestPromoDetails\\\":[{\\\"promoName\\\":\\\"团购优惠39677元\\\",\\\"promoAmount\\\":\\\"39677\\\",\\\"promoDesc\\\":\\\"下单立省39677元\\\",\\\"iconUrl\\\":\\\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\\\",\\\"promoTag\\\":\\\"团购优惠\\\"}],\\\"marketPricePromo\\\":\\\"39677\\\",\\\"promoAbstractList\\\":[\\\"团购优惠39677元\\\",\\\"[{\\\\\\\"backgroundcolor\\\\\\\":\\\\\\\"#00FFFFFF\\\\\\\",\\\\\\\"strikethrough\\\\\\\":false,\\\\\\\"text\\\\\\\":\\\\\\\"金融券test环境使用\\\\\\\",\\\\\\\"textcolor\\\\\\\":\\\\\\\"#222222\\\\\\\",\\\\\\\"textsize\\\\\\\":12,\\\\\\\"textstyle\\\\\\\":\\\\\\\"Default\\\\\\\",\\\\\\\"underline\\\\\\\":false}]\\\"],\\\"finalPrice\\\":\\\"123\\\",\\\"priceDisplayType\\\":0,\\\"showBestPromoDetails\\\":true,\\\"promoPrice\\\":\\\"123\\\",\\\"promoNewStyle\\\":true,\\\"couponList\\\":[],\\\"exposureList\\\":[],\\\"showMarketPrice\\\":true,\\\"dealGroupPrice\\\":\\\"123\\\",\\\"promoActivityList\\\":[{\\\"leadUrl\\\":\\\"https://stable.pay.test.sankuai.com/portal/bindcard/bindcard.html?merchant_no=1&ext_dim_stat_entry=1&callback_type=close_webview&_mtcq=0&utm_source=pay_app-pay-banner410419_540904&campaignId=1477039\\\",\\\"shortText\\\":\\\"[{\\\\\\\"backgroundcolor\\\\\\\":\\\\\\\"#00FFFFFF\\\\\\\",\\\\\\\"strikethrough\\\\\\\":false,\\\\\\\"text\\\\\\\":\\\\\\\"金融券test环境使用\\\\\\\",\\\\\\\"textcolor\\\\\\\":\\\\\\\"#222222\\\\\\\",\\\\\\\"textsize\\\\\\\":12,\\\\\\\"textstyle\\\\\\\":\\\\\\\"Default\\\\\\\",\\\\\\\"underline\\\\\\\":false}]\\\",\\\"style\\\":0,\\\"text\\\":\\\"[{\\\\\\\"backgroundcolor\\\\\\\":\\\\\\\"#00FFFFFF\\\\\\\",\\\\\\\"strikethrough\\\\\\\":false,\\\\\\\"text\\\\\\\":\\\\\\\"金融券test环境使用\\\\\\\",\\\\\\\"textcolor\\\\\\\":\\\\\\\"#222222\\\\\\\",\\\\\\\"textsize\\\\\\\":12,\\\\\\\"textstyle\\\\\\\":\\\\\\\"Default\\\\\\\",\\\\\\\"underline\\\\\\\":false}]\\\",\\\"bonusType\\\":\\\"省\\\"}],\\\"marketPromoDiscount\\\":\\\"0.1折\\\"},\\\"position\\\":\\\"2104\\\",\\\"shop\\\":{\\\"shopPic\\\":\\\"http://p0.meituan.net/searchscenerec/d2f43577e06c094f2c4bc5f89bcc063a238883.png%40300w_0e_1l\\\",\\\"buyBarIconType\\\":0,\\\"shopIdEncrypt\\\":\\\"h1yj777373426642cc853b69bff6732bc6e94091f0fdc4c1a476023af1483d006be613f991371eec948f434549edccde4f37f1b34ca930a6275e5d3b2ee8c1984b8c19fa2282de902f90541f398cbf9829b68e842ec34c0ah7fh\\\",\\\"shopNum\\\":4,\\\"isLyyShop\\\":false,\\\"shopCategoryId\\\":0,\\\"avgPrice\\\":\\\"\\\",\\\"hideStars\\\":false,\\\"shopListUrl\\\":\\\"imeituan://www.meituan.com/gc/mrn?mrn_biz=gc&mrn_entry=gcdealmrnmodules&mrn_component=shoplistnextpage&dealid=1024663027&poiid=*********&frompage=&cityId=1&locatedCityId=0&lat=0.0&lng=0.0&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=&entrypage=2\\\",\\\"shopName\\\":\\\"神会员口腔医疗机构测试门店-*********\\\",\\\"businessState\\\":\\\"\\\",\\\"showType\\\":\\\"medicine\\\",\\\"shopId\\\":*********,\\\"displayPosition\\\":1,\\\"lat\\\":31.219265826967963,\\\"address\\\":\\\"蒲松北路\\\",\\\"shopListDesc\\\":\\\"4家门店适用\\\",\\\"lng\\\":121.36678440710057,\\\"businessHour\\\":\\\"\\\",\\\"branchName\\\":\\\"\\\",\\\"shopPower\\\":0,\\\"shopUrl\\\":\\\"imeituan://www.meituan.com/gc/poi/detail?id=*********&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=&channelType=\\\",\\\"phoneNos\\\":[],\\\"mapUrl\\\":\\\"imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=*********&latitude=31.219265826967963&longitude=121.36678440710057\\\",\\\"shopType\\\":85,\\\"hideAddrEnable\\\":false,\\\"shopBizType\\\":0},\\\"guarantee\\\":[{\\\"iconHeight\\\":0,\\\"iconWidth\\\":0,\\\"text\\\":\\\"随时退\\\",\\\"type\\\":0},{\\\"iconHeight\\\":0,\\\"iconWidth\\\":0,\\\"text\\\":\\\"过期退\\\",\\\"type\\\":0}],\\\"title\\\":\\\"AutoCreateByWatson\\\",\\\"buyBar\\\":{\\\"styleType\\\":1,\\\"buyType\\\":0,\\\"buyBtns\\\":[{\\\"btnTitle\\\":\\\"立即抢购\\\",\\\"usePhone\\\":false,\\\"btnIcons\\\":[{\\\"borderColor\\\":\\\"#646464\\\",\\\"titleColor\\\":\\\"#646464\\\",\\\"bgColor\\\":\\\"#FFFFFF\\\",\\\"style\\\":\\\"#646464\\\",\\\"title\\\":\\\"共省¥39677\\\",\\\"type\\\":1}],\\\"priceStr\\\":\\\"123\\\",\\\"detailBuyType\\\":1,\\\"redirectUrl\\\":\\\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=group-order-submit&mrn_component=GroupOrderSubmit&dealid=1024835476&shopid=*********&shopidEncrypt=h1yj567a2b515a4c4b2c117f8dc68f876c0119c1ebf0603c4c78dec03d58439ed5ebf0e159013c8379a57c3962185517h7fh&pagesource=dealGroupDetail&is_sku=0&isMagicalPrice=false&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUGE5ylRMsuAdmZ8PctW2IiPj57uHc0qHDeF0COYpQdrsl9ciErfpUBxz3lgbJopxXVDPCjrpd95d91QZtGisfkNlJaXGZmcohZ3JhigOdt0WbBBckEo2xz_tlu1ugUnzRCbL3NmoJtm5906kKSVW65xzrHXTsesVdVkpIlLCclZfZzySC1VgmKZpdnk0Xe_YE0u3LuEfDiiDmTY2uCBs0nKIPY1xzc9uVFN3afsWfpqr9CnvYNS66ozH6kKRmjG_OTUmwY6BiRnFJCjtzLD4-tTXVBWycMX1BmKFUUYuOISFmWRpc3zFzHL1lIX_n1FUg\\\",\\\"btnText\\\":\\\"￥123 立即购买\\\",\\\"addShoppingCartStatus\\\":1,\\\"btnEnable\\\":true,\\\"btnDesc\\\":\\\"[{\\\\\\\"text\\\\\\\":\\\\\\\"门市价 ￥\\\\\\\",\\\\\\\"textsize\\\\\\\":12,\\\\\\\"textcolor\\\\\\\":\\\\\\\"#FF999999\\\\\\\",\\\\\\\"backgroundcolor\\\\\\\":\\\\\\\"#00FFFFFF\\\\\\\",\\\\\\\"textstyle\\\\\\\":\\\\\\\"Default\\\\\\\",\\\\\\\"strikethrough\\\\\\\":false,\\\\\\\"underline\\\\\\\":false},{\\\\\\\"text\\\\\\\":\\\\\\\"39800\\\\\\\",\\\\\\\"textsize\\\\\\\":12,\\\\\\\"textcolor\\\\\\\":\\\\\\\"#FF999999\\\\\\\",\\\\\\\"backgroundcolor\\\\\\\":\\\\\\\"#00FFFFFF\\\\\\\",\\\\\\\"textstyle\\\\\\\":\\\\\\\"Default\\\\\\\",\\\\\\\"strikethrough\\\\\\\":true,\\\\\\\"underline\\\\\\\":false}]\\\",\\\"btnTag\\\":\\\"共省¥39677\\\",\\\"priceRuleModule\\\":{\\\"priceRuleTags\\\":[\\\"团购价\\\",\\\"￥123\\\"],\\\"priceRuleType\\\":1,\\\"promoDesc\\\":\\\"共省￥39677\\\"}}]},\\\"mLiveInfoVo\\\":{\\\"mLiveId\\\":0,\\\"goodsTypeId\\\":\\\"0\\\",\\\"channelIdentity\\\":false},\\\"saleDescStr\\\":\\\"已售37\\\",\\\"hitStructuredPurchaseNote\\\":true,\\\"picAspectRatio\\\":0,\\\"displayPrice\\\":\\\"39,800\\\",\\\"moduleExtra\\\":{\\\"moduleConfigDos\\\":[{\\\"value\\\":\\\"gcdealdetail_newtuandealtab_dentis_tuandetail\\\",\\\"key\\\":\\\"团购详情\\\"},{\\\"value\\\":\\\"gcdealdetail_newtuandealtab_dentis_buyrules\\\",\\\"key\\\":\\\"购买须知\\\"},{\\\"value\\\":\\\"gcdealdetail_newtuandealtab_reviews\\\",\\\"key\\\":\\\"网友评价\\\"}],\\\"success\\\":true,\\\"expResults\\\":[{\\\"configs\\\":[{\\\"expResult\\\":\\\"a\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"9a7a5731-4acd-45a4-b06c-8538123a456d\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"exp001434_a\\\\\\\"}\\\",\\\"expId\\\":\\\"exp001434\\\"}],\\\"key\\\":\\\"MTSalesGeneralSection\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"c\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"0cb32b15-14f3-4469-8e41-8754ac2356a9\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"EXP2024082700008_c\\\\\\\"}\\\",\\\"expId\\\":\\\"EXP2024082700008\\\"}],\\\"key\\\":\\\"MtCouponAlleviate2Exp\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"b\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"0fee7328-b878-4f61-a3e7-9f8207f7ee41\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"exp001510_b\\\\\\\"}\\\",\\\"expId\\\":\\\"exp001510\\\"}],\\\"key\\\":\\\"MTShoppingCartBuyBar\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"a\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"126a06d8-48d6-424b-8036-0e0ba4ea0d62\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"exp001692_a\\\\\\\"}\\\",\\\"expId\\\":\\\"exp001692\\\"}],\\\"key\\\":\\\"MTShoppingCartBuyBarNew\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"c\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"9e8d1ca2-6329-4dba-bf9d-b6f9875bcd9e\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"exp001707_c\\\\\\\"}\\\",\\\"expId\\\":\\\"exp001707\\\"}],\\\"key\\\":\\\"MTCouponBar\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"b\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"49ea6154-95ec-4d0e-8197-7b87055411b8\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"exp001872_b\\\\\\\"}\\\",\\\"expId\\\":\\\"exp001872\\\"}],\\\"key\\\":\\\"MtPurchaseNoteStructure\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"b\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"746dbef5-a447-4949-b196-f93da205c383\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"exp001872_b\\\\\\\"}\\\",\\\"expId\\\":\\\"exp001872\\\"}],\\\"key\\\":\\\"CardStyleAB_V2_MT_impr_v1\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"c\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"6c060090-ab6e-4821-81a0-e96ace6a31f7\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"EXP2024082700008_c\\\\\\\"}\\\",\\\"expId\\\":\\\"EXP2024082700008\\\"}],\\\"key\\\":\\\"MtCouponAlleviate1Exp\\\"}]},\\\"serviceTypeId\\\":845,\\\"tradeType\\\":1,\\\"purchaseLimitDeal\\\":false,\\\"reminderInfo\\\":[\\\"免预约\\\",\\\"周一至周日全天可用\\\",\\\"购买后90天内有效\\\"],\\\"priceDisplayModuleDo\\\":{\\\"enableDisplay\\\":false,\\\"marketPrice\\\":\\\"39,800\\\",\\\"price\\\":\\\"123\\\",\\\"dealGroupPrice\\\":\\\"123\\\"},\\\"moduleAbConfigs\\\":[{\\\"configs\\\":[{\\\"expResult\\\":\\\"a\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"9a7a5731-4acd-45a4-b06c-8538123a456d\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"exp001434_a\\\\\\\"}\\\",\\\"expId\\\":\\\"exp001434\\\"}],\\\"key\\\":\\\"MTSalesGeneralSection\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"c\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"0cb32b15-14f3-4469-8e41-8754ac2356a9\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"EXP2024082700008_c\\\\\\\"}\\\",\\\"expId\\\":\\\"EXP2024082700008\\\"}],\\\"key\\\":\\\"MtCouponAlleviate2Exp\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"b\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"0fee7328-b878-4f61-a3e7-9f8207f7ee41\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"exp001510_b\\\\\\\"}\\\",\\\"expId\\\":\\\"exp001510\\\"}],\\\"key\\\":\\\"MTShoppingCartBuyBar\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"a\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"126a06d8-48d6-424b-8036-0e0ba4ea0d62\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"exp001692_a\\\\\\\"}\\\",\\\"expId\\\":\\\"exp001692\\\"}],\\\"key\\\":\\\"MTShoppingCartBuyBarNew\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"c\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"9e8d1ca2-6329-4dba-bf9d-b6f9875bcd9e\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"exp001707_c\\\\\\\"}\\\",\\\"expId\\\":\\\"exp001707\\\"}],\\\"key\\\":\\\"MTCouponBar\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"b\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"49ea6154-95ec-4d0e-8197-7b87055411b8\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"exp001872_b\\\\\\\"}\\\",\\\"expId\\\":\\\"exp001872\\\"}],\\\"key\\\":\\\"MtPurchaseNoteStructure\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"b\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"746dbef5-a447-4949-b196-f93da205c383\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"exp001872_b\\\\\\\"}\\\",\\\"expId\\\":\\\"exp001872\\\"}],\\\"key\\\":\\\"CardStyleAB_V2_MT_impr_v1\\\"},{\\\"configs\\\":[{\\\"expResult\\\":\\\"c\\\",\\\"expBiInfo\\\":\\\"{\\\\\\\"query_id\\\\\\\":\\\\\\\"6c060090-ab6e-4821-81a0-e96ace6a31f7\\\\\\\",\\\\\\\"ab_id\\\\\\\":\\\\\\\"EXP2024082700008_c\\\\\\\"}\\\",\\\"expId\\\":\\\"EXP2024082700008\\\"}],\\\"key\\\":\\\"MtCouponAlleviate1Exp\\\"}],\\\"showNewReserveEntrance\\\":false,\\\"shareModule\\\":{\\\"mt\\\":{\\\"imgUrl\\\":\\\"https://p1.meituan.net/bmlsubjectimage/e65ec5674551383dc745806529ec586f53474.jpg%40180w_132h_1e_1c_1l%7Cwatermark%3D0\\\",\\\"brandName\\\":\\\"AutoCreateByWatson\\\",\\\"mtDealGroupId\\\":1024835476,\\\"price\\\":123},\\\"shareId\\\":\\\"m472a1nwajs0\\\",\\\"screenShotShareEnable\\\":true},\\\"dpId\\\":1024835476,\\\"needLogin\\\":false,\\\"dealName\\\":\\\"AutoCreateByWatson\\\",\\\"shareAble\\\":true,\\\"shopCardState\\\":4,\\\"moreDealsModule\\\":{\\\"publishCategoryId\\\":506,\\\"buCode\\\":16},\\\"extraStyles\\\":[],\\\"bgName\\\":\\\"general\\\",\\\"dealContents\\\":[{\\\"scale\\\":\\\"16:9\\\",\\\"type\\\":1,\\\"content\\\":\\\"https://p1.meituan.net/bmlsubjectimage/e65ec5674551383dc745806529ec586f53474.jpg%40960w_540h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D2%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\"},{\\\"scale\\\":\\\"16:9\\\",\\\"type\\\":1,\\\"content\\\":\\\"https://p0.meituan.net/bmlsubjectimage/5d95e6b50a3d81e1b5c858d2da932f6d92633.jpg%40960w_540h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D2%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\"}],\\\"categoryId\\\":506,\\\"maxPerUser\\\":0}\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"grouponmembermodule\\\"}\":\"{\\\"msg\\\":\\\"成功\\\",\\\"code\\\":200,\\\"data\\\":{\\\"commonResp\\\":{\\\"msg\\\":\\\"成功\\\",\\\"code\\\":200},\\\"displayMemberModule\\\":false}}\"}}";
        DealBffCacheQueryResponse response = JacksonUtils.deserializeUnchecked(json, DealBffCacheQueryResponse.class);
        Class clazz = processor.getClass();
        DealBffResponseDTO dto =   null;
        processor.parseBffData(response);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("key","key");
        jsonObject.put("extraInfo", "");
        jsonObject.put("generalInfo","");
        try{
            processor.buildDealLayoutComponents(jsonObject);

        }catch (Exception e){}
        assertNull(dto);
    }

    @Test
    public void testProcess() throws TException {
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        request.setDealGroupId(123);
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setMtUserId(123);
        envCtx.setDpUserId(123);
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setCategoryId(1L);

        when(dealCategoryCacheService.get(anyInt(), anyBoolean())).thenReturn(dealGroupCategoryDTO);

        processor.process(request, envCtx);
        assertNotNull(dealGroupCategoryDTO);

    }
}
