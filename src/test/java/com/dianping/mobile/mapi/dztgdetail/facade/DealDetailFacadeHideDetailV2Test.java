package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.helper.DealAttrHelper;
import com.dianping.mobile.mapi.dztgdetail.helper.SwitchHelper;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(MockitoJUnitRunner.class)
@PrepareForTest({ SwitchHelper.class, DealAttrHelper.class })
public class DealDetailFacadeHideDetailV2Test {

    @Mock
    private SwitchHelper switchHelper;

    private DealDetailFacade dealDetailFacade = new DealDetailFacade();

    private boolean invokeHideDetailV2(Object arg1, Object arg2) throws Exception {
        Method method = DealDetailFacade.class.getDeclaredMethod("hideDetailV2", java.util.List.class, int.class);
        method.setAccessible(true);
        return (boolean) method.invoke(dealDetailFacade, arg1, arg2);
    }

    @Test
    public void testHideDetailV2WhenAttrDTOsIsEmpty() throws Throwable {
        boolean result = invokeHideDetailV2(Collections.emptyList(), 1);
        assertFalse(result);
    }

    @Test
    public void testHideDetailV2WhenAttrDTOsDoesNotContainVoucher() throws Throwable {
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getName()).thenReturn("OTHER");
        boolean result = invokeHideDetailV2(Arrays.asList(attrDTO), 1);
        assertFalse(result);
    }

    @Test
    public void testHideDetailV2WhenAttrDTOsContainsVoucherAndCategoryIdIsNotInHiddenCategories() throws Throwable {
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getName()).thenReturn("VOUCHER");
        boolean result = invokeHideDetailV2(Arrays.asList(attrDTO), 1);
        assertFalse(result);
    }

}
