package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.mockito.Mockito.*;

public class DealGroupWrapper_BatchMtDealGroupIdByDpTest {

    @InjectMocks
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealIdMapperService dealIdMapperService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 dpDealGroupIds 为空的情况
     */
    @Test
    public void testBatchMtDealGroupIdByDpEmpty() {
        List<IdMapper> result = dealGroupWrapper.batchMtDealGroupIdByDp(Collections.emptyList());
        assertEquals(0, result.size());
    }

    /**
     * 测试 dpDealGroupIds 不为空，但 dealIdMapperService.queryByDpDealGroupIds(partition) 返回空列表的情况
     */
    @Test
    public void testBatchMtDealGroupIdByDpNotEmptyButQueryResultEmpty() {
        when(dealIdMapperService.queryByDpDealGroupIds(anyList())).thenReturn(Collections.emptyList());
        List<IdMapper> result = dealGroupWrapper.batchMtDealGroupIdByDp(Arrays.asList(1, 2, 3));
        assertEquals(0, result.size());
    }



    /**
     * 测试 dealIdMapperService.queryByDpDealGroupIds(partition) 在执行过程中抛出异常的情况
     */
    @Test
    public void testBatchMtDealGroupIdByDpException() {
        when(dealIdMapperService.queryByDpDealGroupIds(anyList())).thenThrow(new RuntimeException());
        List<IdMapper> result = dealGroupWrapper.batchMtDealGroupIdByDp(Arrays.asList(1, 2, 3));
        assertEquals(0, result.size());
    }
}
