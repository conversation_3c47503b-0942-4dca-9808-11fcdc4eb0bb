package com.dianping.mobile.mapi.dztgdetail.biz.service.recommend;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.model.RecommendUserBaseInfoModel;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.RelatedRecommendCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedRecommendReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.LeCrossRecommendConfig;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class LeCrossRecommendHandlerBuildExtParamsTest {

    @InjectMocks
    private LeCrossRecommendHandler leCrossRecommendHandler;

    @Mock
    private RelatedRecommendCtx ctx;

    @Mock
    private LeCrossRecommendConfig leCrossRecommendConfig;

    public RelatedRecommendCtx removeRelatedRecommendCtx(RelatedRecommendCtx ctx, LeCrossRecommendConfig leCrossRecommendConfig) {
        if (CollectionUtils.isNotEmpty(ctx.getModuleAbConfigs())) {
            String targetKey = leCrossRecommendConfig.getMtDouHuKey();
            List<ModuleAbConfig> moduleAbConfigs = new ArrayList<>(ctx.getModuleAbConfigs());
            moduleAbConfigs.removeIf(item -> item.getKey().equals(targetKey));
            ctx.setModuleAbConfigs(moduleAbConfigs);
        }
        return ctx;
    }

    /**
     * Test buildExtParams when envCtx is null
     */
    @Test
    public void testBuildExtParams_WhenEnvCtxNull() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        ctx.setEnvCtx(null);
        ctx.setReq(new RelatedRecommendReq());
        ctx.setRecommendUserBaseInfoModel(new RecommendUserBaseInfoModel());
        LeCrossRecommendConfig config = new LeCrossRecommendConfig();
        // act
        Map<String, Object> result = leCrossRecommendHandler.buildExtParams(ctx, config);
        // assert
        assertNull(result);
    }

    /**
     * Test buildExtParams when req is null
     */
    @Test
    public void testBuildExtParams_WhenReqNull() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        ctx.setEnvCtx(new EnvCtx());
        ctx.setReq(null);
        ctx.setRecommendUserBaseInfoModel(new RecommendUserBaseInfoModel());
        LeCrossRecommendConfig config = new LeCrossRecommendConfig();
        // act
        Map<String, Object> result = leCrossRecommendHandler.buildExtParams(ctx, config);
        // assert
        assertNull(result);
    }

    /**
     * Test buildExtParams when userBaseInfoModel is null
     */
    @Test
    public void testBuildExtParams_WhenUserBaseInfoModelNull() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        ctx.setEnvCtx(new EnvCtx());
        ctx.setReq(new RelatedRecommendReq());
        ctx.setRecommendUserBaseInfoModel(null);
        LeCrossRecommendConfig config = new LeCrossRecommendConfig();
        // act
        Map<String, Object> result = leCrossRecommendHandler.buildExtParams(ctx, config);
        // assert
        assertNull(result);
    }

    /**
     * Test buildExtParams when config is null
     */
    @Test
    public void testBuildExtParams_WhenConfigNull() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        ctx.setEnvCtx(new EnvCtx());
        ctx.setReq(new RelatedRecommendReq());
        ctx.setRecommendUserBaseInfoModel(new RecommendUserBaseInfoModel());
        // act
        Map<String, Object> result = leCrossRecommendHandler.buildExtParams(ctx, null);
        // assert
        assertNull(result);
    }

    /**
     * Test case for Scenario 1: The ModuleAbConfig list in the context is empty.
     * Expected Behavior: The list should be set to a singleton list containing the provided moduleAbConfig.
     */
    @Test
    public void testBuildRelatedRecommendCtx_EmptyModuleAbConfigList() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        LeCrossRecommendHandler handler = new LeCrossRecommendHandler();
        // act
        RelatedRecommendCtx result = handler.buildRelatedRecommendCtx(ctx, moduleAbConfig);
        // assert
        assertNotNull(result);
        List<ModuleAbConfig> moduleAbConfigs = result.getModuleAbConfigs();
        assertNotNull(moduleAbConfigs);
        assertEquals(1, moduleAbConfigs.size());
        assertEquals(moduleAbConfig, moduleAbConfigs.get(0));
    }

    /**
     * Test case for Scenario 2: The ModuleAbConfig list in the context is not empty.
     * Expected Behavior: The provided moduleAbConfig should be added to the existing list.
     */
    @Test
    public void testBuildRelatedRecommendCtx_NonEmptyModuleAbConfigList() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        ModuleAbConfig existingConfig = new ModuleAbConfig();
        List<ModuleAbConfig> moduleAbConfigs = new ArrayList<>();
        moduleAbConfigs.add(existingConfig);
        ctx.setModuleAbConfigs(moduleAbConfigs);
        ModuleAbConfig newConfig = new ModuleAbConfig();
        LeCrossRecommendHandler handler = new LeCrossRecommendHandler();
        // act
        RelatedRecommendCtx result = handler.buildRelatedRecommendCtx(ctx, newConfig);
        // assert
        assertNotNull(result);
        List<ModuleAbConfig> resultModuleAbConfigs = result.getModuleAbConfigs();
        assertNotNull(resultModuleAbConfigs);
        assertEquals(2, resultModuleAbConfigs.size());
        assertTrue(resultModuleAbConfigs.contains(existingConfig));
        assertTrue(resultModuleAbConfigs.contains(newConfig));
    }

    /**
     * Test case for Scenario 3: The RelatedRecommendCtx is null.
     * Expected Behavior: The method should handle it gracefully, possibly by throwing an exception or returning null.
     */
    @Test(expected = NullPointerException.class)
    public void testBuildRelatedRecommendCtx_NullContext() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = null;
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        LeCrossRecommendHandler handler = new LeCrossRecommendHandler();
        // act
        handler.buildRelatedRecommendCtx(ctx, moduleAbConfig);
        // assert
        // Expecting NullPointerException to be thrown
    }

    /**
     * Test case for Scenario 4: The ModuleAbConfig is null.
     * Expected Behavior: The method should handle it gracefully, possibly by throwing an exception or ignoring the null value.
     */
    @Test
    public void testBuildRelatedRecommendCtx_NullModuleAbConfig() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = new RelatedRecommendCtx();
        ModuleAbConfig moduleAbConfig = null;
        LeCrossRecommendHandler handler = new LeCrossRecommendHandler();
        // act
        RelatedRecommendCtx result = handler.buildRelatedRecommendCtx(ctx, moduleAbConfig);
        // assert
        assertNotNull(result);
        List<ModuleAbConfig> moduleAbConfigs = result.getModuleAbConfigs();
        assertNotNull(moduleAbConfigs);
        assertEquals(1, moduleAbConfigs.size());
        assertNull(moduleAbConfigs.get(0));
    }

    /**
     * 测试场景：ctx.getModuleAbConfigs() 为空
     */
    @Test
    public void testRemoveRelatedRecommendCtx_ModuleAbConfigsEmpty() throws Throwable {
        // arrange
        when(ctx.getModuleAbConfigs()).thenReturn(Collections.emptyList());
        // act
        RelatedRecommendCtx result = leCrossRecommendHandler.removeRelatedRecommendCtx(ctx, leCrossRecommendConfig);
        // assert
        assertSame(ctx, result);
        verify(ctx, never()).setModuleAbConfigs(anyList());
    }

    /**
     * 测试场景：ctx.getModuleAbConfigs() 不为空，且 targetKey 存在
     */
    @Test
    public void testRemoveRelatedRecommendCtx_TargetKeyExists() throws Throwable {
        // arrange
        List<ModuleAbConfig> moduleAbConfigs = new ArrayList<>();
        ModuleAbConfig config1 = new ModuleAbConfig();
        config1.setKey("key1");
        AbConfig abConfig1 = new AbConfig();
        abConfig1.setExpResult("expResult1");
        abConfig1.setExpBiInfo("expBiInfo1");
        config1.setConfigs(Collections.singletonList(abConfig1));
        moduleAbConfigs.add(config1);
        ModuleAbConfig config2 = new ModuleAbConfig();
        config2.setKey("key2");
        AbConfig abConfig2 = new AbConfig();
        abConfig2.setExpResult("expResult2");
        abConfig2.setExpBiInfo("expBiInfo2");
        config2.setConfigs(Collections.singletonList(abConfig2));
        moduleAbConfigs.add(config2);
        when(ctx.getModuleAbConfigs()).thenReturn(moduleAbConfigs);
        when(leCrossRecommendConfig.getMtDouHuKey()).thenReturn("key1");
        // act
        RelatedRecommendCtx result = leCrossRecommendHandler.removeRelatedRecommendCtx(ctx, leCrossRecommendConfig);
        // assert
        assertSame(ctx, result);
        verify(ctx).setModuleAbConfigs(argThat(list -> list.size() == 1 && list.get(0).getKey().equals("key2")));
    }

    /**
     * 测试场景：ctx.getModuleAbConfigs() 不为空，且 targetKey 不存在
     */
    @Test
    public void testRemoveRelatedRecommendCtx_TargetKeyNotExists() throws Throwable {
        // arrange
        List<ModuleAbConfig> moduleAbConfigs = new ArrayList<>();
        ModuleAbConfig config1 = new ModuleAbConfig();
        config1.setKey("key1");
        AbConfig abConfig1 = new AbConfig();
        abConfig1.setExpResult("expResult1");
        abConfig1.setExpBiInfo("expBiInfo1");
        config1.setConfigs(Collections.singletonList(abConfig1));
        moduleAbConfigs.add(config1);
        ModuleAbConfig config2 = new ModuleAbConfig();
        config2.setKey("key2");
        AbConfig abConfig2 = new AbConfig();
        abConfig2.setExpResult("expResult2");
        abConfig2.setExpBiInfo("expBiInfo2");
        config2.setConfigs(Collections.singletonList(abConfig2));
        moduleAbConfigs.add(config2);
        when(ctx.getModuleAbConfigs()).thenReturn(moduleAbConfigs);
        when(leCrossRecommendConfig.getMtDouHuKey()).thenReturn("key3");
        // act
        RelatedRecommendCtx result = leCrossRecommendHandler.removeRelatedRecommendCtx(ctx, leCrossRecommendConfig);
        // assert
        assertSame(ctx, result);
        verify(ctx).setModuleAbConfigs(argThat(list -> list.size() == 2));
    }

    /**
     * 测试场景：ctx 为 null
     */
    @Test(expected = NullPointerException.class)
    public void testRemoveRelatedRecommendCtx_CtxNull() throws Throwable {
        // arrange
        RelatedRecommendCtx ctx = null;
        // act
        leCrossRecommendHandler.removeRelatedRecommendCtx(ctx, leCrossRecommendConfig);
        // assert
        // 预期抛出 NullPointerException
    }

    /**
     * 测试场景：leCrossRecommendConfig 为 null
     */
    @Test
    public void testRemoveRelatedRecommendCtx_LeCrossRecommendConfigNull() throws Throwable {
        // arrange
        LeCrossRecommendConfig leCrossRecommendConfig = null;
        // act
        RelatedRecommendCtx result = leCrossRecommendHandler.removeRelatedRecommendCtx(ctx, leCrossRecommendConfig);
        // assert
        assertSame(ctx, result);
        verify(ctx, never()).setModuleAbConfigs(anyList());
    }

    /**
     * 测试场景：ctx.getModuleAbConfigs() 返回 null
     */
    @Test
    public void testRemoveRelatedRecommendCtx_ModuleAbConfigsNull() throws Throwable {
        // arrange
        when(ctx.getModuleAbConfigs()).thenReturn(null);
        // act
        RelatedRecommendCtx result = leCrossRecommendHandler.removeRelatedRecommendCtx(ctx, leCrossRecommendConfig);
        // assert
        assertSame(ctx, result);
        verify(ctx, never()).setModuleAbConfigs(anyList());
    }

    /**
     * 测试场景：leCrossRecommendConfig.getMtDouHuKey() 返回 null
     */
    @Test
    public void testRemoveRelatedRecommendCtx_TargetKeyNull() throws Throwable {
        // arrange
        List<ModuleAbConfig> moduleAbConfigs = new ArrayList<>();
        ModuleAbConfig config1 = new ModuleAbConfig();
        config1.setKey("key1");
        AbConfig abConfig1 = new AbConfig();
        abConfig1.setExpResult("expResult1");
        abConfig1.setExpBiInfo("expBiInfo1");
        config1.setConfigs(Collections.singletonList(abConfig1));
        moduleAbConfigs.add(config1);
        ModuleAbConfig config2 = new ModuleAbConfig();
        config2.setKey("key2");
        AbConfig abConfig2 = new AbConfig();
        abConfig2.setExpResult("expResult2");
        abConfig2.setExpBiInfo("expBiInfo2");
        config2.setConfigs(Collections.singletonList(abConfig2));
        moduleAbConfigs.add(config2);
        when(ctx.getModuleAbConfigs()).thenReturn(moduleAbConfigs);
        when(leCrossRecommendConfig.getMtDouHuKey()).thenReturn(null);
        // act
        RelatedRecommendCtx result = leCrossRecommendHandler.removeRelatedRecommendCtx(ctx, leCrossRecommendConfig);
        // assert
        assertSame(ctx, result);
        verify(ctx).setModuleAbConfigs(argThat(list -> list.size() == 2));
    }
}
