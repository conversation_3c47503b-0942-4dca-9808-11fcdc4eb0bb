package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.RelatedDealsReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.RelatedDeals;
import com.dianping.mobile.mapi.dztgdetail.tab.RelateDeals;
import com.dianping.mobile.mapi.dztgdetail.tab.RelateDealsFactory;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.BaseData;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTab;
import com.dianping.mobile.mapi.dztgdetail.tab.bo.DealTabHolder;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ Lion.class })
public class DealQueryFacadeBuildHeguiNoticeTest {

    @InjectMocks
    private DealQueryFacade dealQueryFacade;

    @Mock
    private Cat cat;

    @Mock
    private RelateDealsFactory relateDealsFactory;

    @Mock
    private RelateDeals relateDeals;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private RelatedDealsReq request;

    @Mock
    private BaseData baseData;

    @Mock
    private DealTabHolder dealTabHolder;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockStatic(Lion.class);
    }

//    /**
//     * Test case for the scenario where the customer name is successfully retrieved and is not blank.
//     */
//    @Test
//    public void testBuildHeguiNotice_CustomerNameNotEmpty() throws Throwable {
//        // arrange
//        EnvCtx envCtx = new EnvCtx();
//        DealCtx dealCtx = new DealCtx(envCtx);
//        dealCtx.setCustomerId(12345L);
//        Map<String, String> customerIdToNameMap = new HashMap<>();
//        customerIdToNameMap.put("12345", "CustomerName");
//        when(Lion.getMap(LionConstants.HEGUI_NOTICE_CUSTOMER_NAME_MAP, String.class, new HashMap<>())).thenReturn(customerIdToNameMap);
//        // act
//        Method method = DealQueryFacade.class.getDeclaredMethod("buildHeguiNotice", DealCtx.class);
//        method.setAccessible(true);
//        String result = (String) method.invoke(dealQueryFacade, dealCtx);
//        // assert
//        assertEquals("本服务套餐由CustomerName（第三方代理公司）提供", result);
//    }

    /**
     * Test case for the scenario where the customer name is retrieved but is blank.
     */
//    @Test
//    public void testBuildHeguiNotice_CustomerNameBlank() throws Throwable {
//        // arrange
//        EnvCtx envCtx = new EnvCtx();
//        DealCtx dealCtx = new DealCtx(envCtx);
//        dealCtx.setCustomerId(12345L);
//        Map<String, String> customerIdToNameMap = new HashMap<>();
//        customerIdToNameMap.put("12345", "");
//        when(Lion.getMap(LionConstants.HEGUI_NOTICE_CUSTOMER_NAME_MAP, String.class, new HashMap<>())).thenReturn(customerIdToNameMap);
//        // act
//        Method method = DealQueryFacade.class.getDeclaredMethod("buildHeguiNotice", DealCtx.class);
//        method.setAccessible(true);
//        String result = (String) method.invoke(dealQueryFacade, dealCtx);
//        // assert
//        assertEquals("本服务套餐由第三方代理公司提供", result);
//    }

    /**
     * Test case for the scenario where an exception occurs while retrieving the customer name.
     */
    @Test
    public void testBuildHeguiNotice_ExceptionOccurs() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setCustomerId(12345L);
        when(Lion.getMap(LionConstants.HEGUI_NOTICE_CUSTOMER_NAME_MAP, String.class, new HashMap<>())).thenThrow(new RuntimeException("Error fetching customer name"));
        // act
        Method method = DealQueryFacade.class.getDeclaredMethod("buildHeguiNotice", DealCtx.class);
        method.setAccessible(true);
        String result = (String) method.invoke(dealQueryFacade, dealCtx);
        // assert
        assertEquals("本服务套餐由第三方代理公司提供", result);
    }

    /**
     * 测试版本检查失败的情况
     */
    @Test
    public void testQueryRelatedDealsVersionCheckFailed() throws Throwable {
        // arrange
        when(envCtx.getVersion()).thenReturn("1.0.0");
        when(envCtx.isMt()).thenReturn(false);
        // act
        RelatedDeals result = dealQueryFacade.queryRelatedDeals(request, envCtx);
        // assert
        assertNull(result);
    }

    /**
     * 测试获取相关团单失败的情况
     */
    @Test
    public void testQueryRelatedDealsGetRelateDealsFailed() throws Throwable {
        // arrange
        when(envCtx.getVersion()).thenReturn("2.0.0");
        when(envCtx.isMt()).thenReturn(false);
        when(request.getShopIdLong()).thenReturn(12345L);
        when(request.getDealGroupId()).thenReturn(67890);
        when(baseData.getPublishCategoryId()).thenReturn(1);
        when(relateDealsFactory.getRelateDeals(baseData.getPublishCategoryId())).thenReturn(null);
        // act
        RelatedDeals result = dealQueryFacade.queryRelatedDeals(request, envCtx);
        // assert
        assertNull(result);
    }

    /**
     * 测试获取相关团单标签失败的情况
     */
    @Test
    public void testQueryRelatedDealsListRelatedDealTabsFailed() throws Throwable {
        // arrange
        when(envCtx.getVersion()).thenReturn("2.0.0");
        when(envCtx.isMt()).thenReturn(false);
        when(request.getShopIdLong()).thenReturn(12345L);
        when(request.getDealGroupId()).thenReturn(67890);
        when(baseData.getPublishCategoryId()).thenReturn(1);
        when(relateDealsFactory.getRelateDeals(baseData.getPublishCategoryId())).thenReturn(relateDeals);
        when(relateDeals.listRelatedDealTabs(baseData, envCtx)).thenReturn(null);
        // act
        RelatedDeals result = dealQueryFacade.queryRelatedDeals(request, envCtx);
        // assert
        assertNull(result);
    }
}
