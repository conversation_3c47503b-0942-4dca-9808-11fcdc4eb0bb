package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.DecryptBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.ShopUuidUtils;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgPlatformEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.DecryptVO;
import org.apache.commons.lang.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealQueryFacadeInitDealsBaseDataTest {

    @InjectMocks
    private DealQueryFacade dealQueryFacade;

    @Mock
    private DecryptBiz decryptBiz;

    @Mock
    private SkuWrapper skuWrapper;

    @Mock
    private EnvCtx envCtx;

    private DealBaseReq req;

    @Before
    public void setUp() {
        req = new DealBaseReq();
    }

    /**
     * Test case for Kuaishou mini program with invalid encrypted shop string
     */
    @Test
    public void testInitDealsBaseData_KuaishouInvalidEncryptedShop() throws Throwable {
        // arrange
        String encryptedShopStr = "encrypted123";
        String decryptedStr = "invalid";
        Integer dealGroupId = 1001;
        Integer cityId = 2001;
        req.setEncryptedShopStr(encryptedShopStr);
        req.setDealgroupid(dealGroupId);
        req.setCityid(cityId);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_KUAISHOU_MINIAPP);
        when(envCtx.isMt()).thenReturn(true);
        DecryptVO decryptVO = new DecryptVO();
        decryptVO.setDecryptedStr(decryptedStr);
        when(skuWrapper.getDefaultSkuId(req, envCtx)).thenReturn("sku001");
        // act
        DealCtx result = dealQueryFacade.initDealsBaseData(req, envCtx);
        // assert
        assertEquals(dealGroupId.intValue(), result.getMtId());
        assertEquals(cityId.intValue(), result.getMtCityId());
        assertEquals(0L, result.getMtLongShopId());
        assertEquals("sku001", result.getSkuId());
    }

    /**
     * Test case for setting dpShopId when shopUuid exists
     */
    @Test
    public void testInitDealsBaseData_WithShopUuid() throws Throwable {
        // arrange
        String shopUuid = "roga-ulsv-351";
        Integer dealGroupId = 1001;
        Integer cityId = 2001;
        req.setShopUuid(shopUuid);
        req.setDealgroupid(dealGroupId);
        req.setCityid(cityId);
        req.setPoiid(0L);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
        when(envCtx.isMt()).thenReturn(false);
        when(skuWrapper.getDefaultSkuId(req, envCtx)).thenReturn("sku001");
        // act
        DealCtx result = dealQueryFacade.initDealsBaseData(req, envCtx);
        // assert
        assertEquals(dealGroupId.intValue(), result.getDpId());
        assertEquals(cityId.intValue(), result.getDpCityId());
        assertEquals("sku001", result.getSkuId());
    }
}
