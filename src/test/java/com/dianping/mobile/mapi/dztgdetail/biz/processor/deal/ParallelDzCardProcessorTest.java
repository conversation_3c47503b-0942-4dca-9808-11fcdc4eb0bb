package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzCardPromoWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
import com.sankuai.dzcard.navigation.api.enums.QualifyEventTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

public class ParallelDzCardProcessorTest {

    @InjectMocks
    private ParallelDzCardProcessor parallelDzCardProcessor;

    @Mock
    private DzCardPromoWrapper wrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试场景：当qualifyEventIdDTOList为空时，process方法应直接返回，不进行任何处理
     */
    @Test
    public void testProcess_WhenQualifyEventIdDTOListIsEmpty() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setFutureCtx(new FutureCtx());
        when(wrapper.resolve(any())).thenReturn(new ArrayList<>());

        // act
        parallelDzCardProcessor.process(ctx);

        // assert
        verify(wrapper, times(1)).resolve(any());
        // 由于qualifyEventIdDTOList为空，不应进一步调用其他方法
    }

    /**
     * 测试场景：当qualifyEventIdDTOList不为空，且第一个权益类型为DISCOUNT_CARD，且isJoyDcCardValid返回true时
     */
    @Test
    public void testProcess_WhenQualifyEventIdDTOListIsNotEmptyAndFirstQualifyEventTypeIsDiscountCardAndIsJoyDcCardValidReturnsTrue() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setFutureCtx(new FutureCtx());
        ctx.setExternal(false);

        List<CardQualifyEventIdDTO> qualifyEventIdDTOList = new ArrayList<>();
        CardQualifyEventIdDTO cardQualifyEventIdDTO = new CardQualifyEventIdDTO();
        cardQualifyEventIdDTO.setQualifyEventType(QualifyEventTypeEnum.DISCOUNT_CARD.getCode());
        qualifyEventIdDTOList.add(cardQualifyEventIdDTO);

        when(wrapper.resolve(any())).thenReturn(qualifyEventIdDTOList);

        // act
        parallelDzCardProcessor.process(ctx);

        // assert
        verify(wrapper, times(1)).resolve(any());
        // 由于qualifyEventIdDTOList不为空且第一个权益类型为DISCOUNT_CARD，应进一步处理
    }

    /**
     * 测试场景：当qualifyEventIdDTOList不为空，且第一个权益类型为DAO_ZONG_CARD，且isMemberPriceProcessorEnable返回true时
     */
    @Test
    public void testProcess_WhenQualifyEventIdDTOListIsNotEmptyAndFirstQualifyEventTypeIsDaoZongCardAndIsMemberPriceProcessorEnableReturnsTrue() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setFutureCtx(new FutureCtx());
        ctx.setExternal(false);

        List<CardQualifyEventIdDTO> qualifyEventIdDTOList = new ArrayList<>();
        CardQualifyEventIdDTO cardQualifyEventIdDTO = new CardQualifyEventIdDTO();
        cardQualifyEventIdDTO.setQualifyEventType(QualifyEventTypeEnum.DAO_ZONG_CARD.getCode());
        qualifyEventIdDTOList.add(cardQualifyEventIdDTO);

        when(wrapper.resolve(any())).thenReturn(qualifyEventIdDTOList);

        // act
        parallelDzCardProcessor.process(ctx);

        // assert
        verify(wrapper, times(1)).resolve(any());
        // 由于qualifyEventIdDTOList不为空且第一个权益类型为DAO_ZONG_CARD，应进一步处理
    }
}
