package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.FreeDealEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.CareCenterHouseLeadsCountConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.FreeDealConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.SaleBuilderService;
import com.dianping.mobile.mapi.dztgdetail.helper.DealCtxHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.leads.count.thrift.api.NewLeadsCountService;
import com.sankuai.leads.count.thrift.dto.LeadsCountADTO;
import com.sankuai.leads.count.thrift.dto.LeadsCountRespDTO;
import org.apache.commons.lang.StringUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * @Author: <EMAIL>
 * @Date: 2024/6/9
 */
@RunWith(MockitoJUnitRunner.class)
public class SaleBuilderServiceTest {
    @InjectMocks
    private SaleBuilderService saleBuilderService;
    @Mock
    private NewLeadsCountService newLeadsCountService;
    @Mock
    private DouHuBiz douHuBiz;

    private MockedStatic<Lion> mockedStatic;

    private MockedStatic<DealCtxHelper> dealCtxHelperMockedStatic;

    @Before
    public void setUp() {
        mockedStatic = mockStatic(Lion.class);
        dealCtxHelperMockedStatic = mockStatic(DealCtxHelper.class);
    }

    @After
    public void teardown() {
        mockedStatic.close();
        dealCtxHelperMockedStatic.close();
    }

    @Test
    public void testSetSaleDescAndSaleDescStr_FreeDeal1611() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFreeDeal(true);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1611);
        ctx.setChannelDTO(channelDTO);

        LeadsCountRespDTO leadsCountRespDTO = new LeadsCountRespDTO();
        leadsCountRespDTO.setCount(100);
        when(newLeadsCountService.queryCountByCache(Mockito.any())).thenReturn(leadsCountRespDTO);

        FreeDealConfig freeDealConfig = new FreeDealConfig();
        freeDealConfig.setSaleDescPrefix("saleDesc");
        ctx.setFreeDealConfig(freeDealConfig);
        SalesDisplayDTO salesDisplayDTO = new SalesDisplayDTO();
        salesDisplayDTO.setSales(100);
        ctx.setSalesDisplayDTO(salesDisplayDTO);

        ctx.setDpId(1);

        DealGroupPBO result = new DealGroupPBO();
        saleBuilderService.setSaleDescAndSaleDescStr(ctx, result);
        assertTrue(result.getSaleDesc().contains("100"));
    }

    @Test
    public void testSetSaleDescAndSaleDescStr_FreeDeal() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFreeDeal(true);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(1610);
        ctx.setChannelDTO(channelDTO);

        LeadsCountRespDTO leadsCountRespDTO = new LeadsCountRespDTO();
        leadsCountRespDTO.setCount(100);

        FreeDealConfig freeDealConfig = new FreeDealConfig();
        freeDealConfig.setSaleDescPrefix("saleDesc");
        ctx.setFreeDealType(FreeDealEnum.EDU_TRIAL_BOOKING);
        ctx.setFreeDealConfig(freeDealConfig);
        SalesDisplayDTO salesDisplayDTO = new SalesDisplayDTO();
        salesDisplayDTO.setSales(100);
        ctx.setSalesDisplayDTO(salesDisplayDTO);

        ctx.setDpId(1);

        DealGroupPBO result = new DealGroupPBO();
        saleBuilderService.setSaleDescAndSaleDescStr(ctx, result);
        assertTrue(result.getSaleDesc().contains("100"));
    }

    @Test
    public void testSetSaleDescAndSaleDescStr_Normal() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFreeDeal(false);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(303);
        ctx.setChannelDTO(channelDTO);

        LeadsCountRespDTO leadsCountRespDTO = new LeadsCountRespDTO();
        leadsCountRespDTO.setCount(100);

        FreeDealConfig freeDealConfig = new FreeDealConfig();
        freeDealConfig.setSaleDescPrefix("saleDesc");
        ctx.setFreeDealType(FreeDealEnum.EDU_TRIAL_BOOKING);
        ctx.setFreeDealConfig(freeDealConfig);
        SalesDisplayDTO salesDisplayDTO = new SalesDisplayDTO();
        salesDisplayDTO.setSales(100);
        salesDisplayDTO.setSalesTag("saleTag");
        ctx.setSalesDisplayDTO(salesDisplayDTO);

        ctx.setDpId(1);

        mockedStatic.when(() -> Lion.getList(LionConstants.AB_EXP_CATEGORY_IDS, Integer.class, Lists.newArrayList(303, 304))).thenReturn(Lists.newArrayList(303, 304));
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        moduleAbConfig.setConfigs(Lists.newArrayList(abConfig));
        when(douHuBiz.getAbExpResult(ctx, "DPSalesColorExp")).thenReturn(moduleAbConfig);

        Map<String, String> configMap = Maps.newHashMap();
        configMap.put("spu_atmosphere_bgurl", "xxx");
        configMap.put("spu_atmosphere_enable", "true");
        AttributeDTO mtssAttrDTO = new AttributeDTO();
        mtssAttrDTO.setName("mtss_ref_spu_id");
        mtssAttrDTO.setValue(Lists.newArrayList("123"));
        AttributeDTO spuSceneTypeAttrDTO = new AttributeDTO();
        spuSceneTypeAttrDTO.setName("spuSceneType");
        spuSceneTypeAttrDTO.setValue(Lists.newArrayList("17"));
        List<AttributeDTO> attrs = Lists.newArrayList();
        attrs.add(mtssAttrDTO);
        attrs.add(spuSceneTypeAttrDTO);
        ctx.setAttrs(attrs);
        mockedStatic.when(() -> Lion.getMap(LionConstants.APP_KEY, LionConstants.STANDARD_PRODUCT_ATMOSPHERE_CONFIG, String.class, Collections.emptyMap())).thenReturn(configMap);
        DealGroupPBO result = new DealGroupPBO();
        result.setSaleDesc("saleDesc");
        saleBuilderService.setSaleDescAndSaleDescStr(ctx, result);
        assertTrue(result.getSaleDesc().contains("saleTag"));
    }

    @Test
    public void test_isEnableCareCenterLeadsCount() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setFreeDeal(false);
        DealGroupChannelDTO channelDTO = new DealGroupChannelDTO();
        channelDTO.setCategoryId(303);
        ctx.setChannelDTO(channelDTO);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO dealGroupCategoryDTO = new DealGroupCategoryDTO();
        dealGroupCategoryDTO.setServiceTypeId(126041L);
        dealGroupDTO.setCategory(dealGroupCategoryDTO);
        ctx.setDealGroupDTO(dealGroupDTO);

        LeadsCountRespDTO leadsCountRespDTO = new LeadsCountRespDTO();
        leadsCountRespDTO.setCount(100);

        FreeDealConfig freeDealConfig = new FreeDealConfig();
        freeDealConfig.setSaleDescPrefix("saleDesc");
        ctx.setFreeDealType(FreeDealEnum.EDU_TRIAL_BOOKING);
        ctx.setFreeDealConfig(freeDealConfig);
        SalesDisplayDTO salesDisplayDTO = new SalesDisplayDTO();
        salesDisplayDTO.setSales(100);
        salesDisplayDTO.setSalesTag("saleTag");
        ctx.setSalesDisplayDTO(salesDisplayDTO);

        ctx.setDpId(1);


//        MockedStatic<Lion> mockedStatic = mockStatic(Lion.class);
        mockedStatic.when(() -> Lion.getString(LionConstants.APP_KEY, LionConstants.CARE_CENTER_HOUSE_LEADS_COUNT)).thenReturn("{\n" +
                "    \"enable\": true,\n" +
                "    \"logicExpressionId\": \"111\",\n" +
                "    \"bizId\": \"1002\",\n" +
                "    \"serviceTypeIds\": [\n" +
                "        \"126041\",\n" +
                "        \"142020\",\n" +
                "        \"132046\"\n" +
                "    ]\n" +
                "}");

        // act
        CareCenterHouseLeadsCountConfig result = saleBuilderService.isEnableCareCenterLeadsCount(ctx);

        // assert
        assertNotNull(result);
    }

    @Test
    public void testSetSaleDescAndSaleDesc_PreOrder_Zero() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setPreOrderSales(0);
        dealCtxHelperMockedStatic.when(() -> DealCtxHelper.isPreOrderDeal(ctx)).thenReturn(true);
        mockedStatic.when(() -> Lion.getList(LionConstants.AB_EXP_CATEGORY_IDS, Integer.class, Lists.newArrayList(303, 304))).thenReturn(Lists.newArrayList(303, 304));
        mockedStatic.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.PRE_ORDER_DEAL_SALE_DEAL_GROUP_SWITCH, false)).thenReturn(false);

        // act
        DealGroupPBO result = new DealGroupPBO();
        saleBuilderService.setSaleDescAndSaleDescStr(ctx, result);

        // assert
        assertTrue(StringUtils.isEmpty(result.getSaleDesc()));
        assertTrue(StringUtils.isEmpty(result.getSaleDescStr()));
    }

    @Test
    public void testSetSaleDescAndSaleDesc_PreOrder_LessThan100() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setPreOrderSales(50);
        dealCtxHelperMockedStatic.when(() -> DealCtxHelper.isPreOrderDeal(ctx)).thenReturn(true);
        mockedStatic.when(() -> Lion.getList(LionConstants.AB_EXP_CATEGORY_IDS, Integer.class, Lists.newArrayList(303, 304))).thenReturn(Lists.newArrayList(303, 304));
        mockedStatic.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.PRE_ORDER_DEAL_SALE_DEAL_GROUP_SWITCH, false)).thenReturn(false);

        // act
        DealGroupPBO result = new DealGroupPBO();
        saleBuilderService.setSaleDescAndSaleDescStr(ctx, result);

        // assert
        assertEquals("已订50", result.getSaleDesc());
        assertEquals("已订50", result.getSaleDescStr());
    }

    @Test
    public void testSetSaleDescAndSaleDesc_PreOrder_LessThan1000() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setPreOrderSales(502);
        dealCtxHelperMockedStatic.when(() -> DealCtxHelper.isPreOrderDeal(ctx)).thenReturn(true);
        mockedStatic.when(() -> Lion.getList(LionConstants.AB_EXP_CATEGORY_IDS, Integer.class, Lists.newArrayList(303, 304))).thenReturn(Lists.newArrayList(303, 304));
        mockedStatic.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.PRE_ORDER_DEAL_SALE_DEAL_GROUP_SWITCH, false)).thenReturn(false);

        // act
        DealGroupPBO result = new DealGroupPBO();
        saleBuilderService.setSaleDescAndSaleDescStr(ctx, result);

        // assert
        assertEquals("已订500+", result.getSaleDesc());
        assertEquals("已订500+", result.getSaleDescStr());
    }

    @Test
    public void testSetSaleDescAndSaleDesc_PreOrder_LessThan10000() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setPreOrderSales(1210);
        dealCtxHelperMockedStatic.when(() -> DealCtxHelper.isPreOrderDeal(ctx)).thenReturn(true);
        mockedStatic.when(() -> Lion.getList(LionConstants.AB_EXP_CATEGORY_IDS, Integer.class, Lists.newArrayList(303, 304))).thenReturn(Lists.newArrayList(303, 304));
        mockedStatic.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.PRE_ORDER_DEAL_SALE_DEAL_GROUP_SWITCH, false)).thenReturn(false);

        // act
        DealGroupPBO result = new DealGroupPBO();
        saleBuilderService.setSaleDescAndSaleDescStr(ctx, result);

        // assert
        assertEquals("已订1200+", result.getSaleDesc());
        assertEquals("已订1200+", result.getSaleDescStr());
    }

    @Test
    public void testSetSaleDescAndSaleDesc_PreOrder_MoreThan10000() {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setPreOrderSales(10010);
        dealCtxHelperMockedStatic.when(() -> DealCtxHelper.isPreOrderDeal(ctx)).thenReturn(true);
        mockedStatic.when(() -> Lion.getList(LionConstants.AB_EXP_CATEGORY_IDS, Integer.class, Lists.newArrayList(303, 304))).thenReturn(Lists.newArrayList(303, 304));
        mockedStatic.when(() -> Lion.getBoolean(LionConstants.APP_KEY, LionConstants.PRE_ORDER_DEAL_SALE_DEAL_GROUP_SWITCH, false)).thenReturn(false);

        // act
        DealGroupPBO result = new DealGroupPBO();
        saleBuilderService.setSaleDescAndSaleDescStr(ctx, result);

        // assert
        assertEquals("已订1.0万+", result.getSaleDesc());
        assertEquals("已订1.0万+", result.getSaleDescStr());
    }

}
