package com.dianping.mobile.mapi.dztgdetail.mq;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.dzdealbase.DzDealBaseService;
import com.dianping.deal.dzdealbase.response.DzDealBaseResponse;
import com.dianping.lion.Environment;
import com.dianping.mobile.mapi.dztgdetail.mq.dto.UnifiedMainInterfaceResultDTO;
import com.dianping.mobile.mapi.dztgdetail.util.JsonUtils;
import com.meituan.mafka.client.bean.MafkaConsumer;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageResponseCodeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MafkaConfigMafkaUnifiedMainInterfaceResultConsumerTest {

    // Note: Additional test cases would be similar in nature, focusing on aspects of the MafkaConsumer
    private MafkaConfig mafkaConfig = new MafkaConfig();

    @InjectMocks
    private UnifiedMainInterfaceResultConsumer consumer;

    @Mock
    private DzDealBaseService dzDealBaseService;

    private ProductDetailPageRequest createProductDetailPageRequest() {
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        request.setClientType(ClientTypeEnum.MT_APP.getCode());
        // Set ShepherdGatewayParam
        ShepherdGatewayParam shepherdGatewayParam = new ShepherdGatewayParam();
        shepherdGatewayParam.setDpUserId(123L);
        shepherdGatewayParam.setMtUserId(456L);
        shepherdGatewayParam.setDpVirtualUserId(789L);
        shepherdGatewayParam.setMtVirtualUserId(101L);
        shepherdGatewayParam.setDeviceId("test-device-id");
        shepherdGatewayParam.setUnionid("test-union-id");
        shepherdGatewayParam.setMobileOSType("ios");
        shepherdGatewayParam.setAppVersion("1.0.0");
        request.setShepherdGatewayParam(shepherdGatewayParam);
        // Set CustomParam
        CustomParam customParam = new CustomParam();
        request.setCustomParam(customParam);
        return request;
    }

    private UnifiedMainInterfaceResultDTO createValidDTO() {
        UnifiedMainInterfaceResultDTO dto = new UnifiedMainInterfaceResultDTO();
        // Set request
        dto.setSpiRequest(createProductDetailPageRequest());
        // Create response list
        List<GenericProductDetailPageResponse> responseList = new ArrayList<>();
        GenericProductDetailPageResponse pageResponse = new GenericProductDetailPageResponse();
        pageResponse.setCode(PageResponseCodeEnum.SUCCESS.getCode());
        Map<String, GenericModuleResponse> moduleResponseMap = new HashMap<>();
        GenericModuleResponse moduleResponse = new GenericModuleResponse();
        moduleResponse.setCode(PageResponseCodeEnum.SUCCESS.getCode());
        moduleResponseMap.put("test_module", moduleResponse);
        pageResponse.setModuleResponse(moduleResponseMap);
        responseList.add(pageResponse);
        dto.setSpiResponseList(responseList);
        return dto;
    }

    @Test
    public void testMafkaUnifiedMainInterfaceResultConsumer_NotNull() throws Throwable {
        // Given
        MafkaConsumer consumer = mafkaConfig.mafkaUnifiedMainInterfaceResultConsumer();
        // When
        assertNotNull(consumer);
        // Then
        assertNotNull(consumer.getNamespace());
        assertNotNull(consumer.getAppkey());
        assertNotNull(consumer.getTopic());
        assertNotNull(consumer.getGroup());
        assertNotNull(consumer.getListener());
        assertNotNull(consumer.getClassName());
        assertTrue(consumer.isOpenHook());
    }

    /**
     * Test case for null message
     */
    @Test
    public void testRecvMessage_NullMessage() throws Throwable {
        // arrange
        MafkaMessage message = null;
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = consumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case for empty message body
     */
    @Test
    public void testRecvMessage_EmptyMessageBody() throws Throwable {
        // arrange
        MafkaMessage message = new MafkaMessage("topic", 0, 0, null, "");
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = consumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case for reserve product type
     */
    @Test
    public void testRecvMessage_ReserveProductType() throws Throwable {
        // arrange
        UnifiedMainInterfaceResultDTO dto = new UnifiedMainInterfaceResultDTO();
        ProductDetailPageRequest request = createProductDetailPageRequest();
        request.setProductType(ProductTypeEnum.RESERVE.getCode());
        dto.setSpiRequest(request);
        String messageBody = JsonUtils.toJson(dto);
        MafkaMessage message = new MafkaMessage("topic", 0, 0, null, messageBody);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = consumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case for successful execution with valid response
     */
    @Test
    public void testRecvMessage_SuccessfulExecution() throws Throwable {
        // arrange
        UnifiedMainInterfaceResultDTO dto = createValidDTO();
        String messageBody = JsonUtils.toJson(dto);
        MafkaMessage message = new MafkaMessage("topic", 0, 0, null, messageBody);
        MessagetContext context = new MessagetContext();
        DzDealBaseResponse<String> response = new DzDealBaseResponse<>();
        response.setSuccess(true);
        response.setData("{\"moduleConfigsModule\":{\"tortTitle\":\"\"}}");
        when(dzDealBaseService.execute(any(), any())).thenReturn(response);
        // act
        ConsumeStatus result = consumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case for failed DzDealBase service response
     */
    @Test
    public void testRecvMessage_FailedDzDealBaseResponse() throws Throwable {
        // arrange
        UnifiedMainInterfaceResultDTO dto = createValidDTO();
        String messageBody = JsonUtils.toJson(dto);
        MafkaMessage message = new MafkaMessage("topic", 0, 0, null, messageBody);
        MessagetContext context = new MessagetContext();
        DzDealBaseResponse<String> response = new DzDealBaseResponse<>();
        response.setSuccess(false);
        when(dzDealBaseService.execute(any(), any())).thenReturn(response);
        // act
        ConsumeStatus result = consumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case for invalid deal response
     */
    @Test
    public void testRecvMessage_InvalidDealResponse() throws Throwable {
        // arrange
        UnifiedMainInterfaceResultDTO dto = createValidDTO();
        String messageBody = JsonUtils.toJson(dto);
        MafkaMessage message = new MafkaMessage("topic", 0, 0, null, messageBody);
        MessagetContext context = new MessagetContext();
        DzDealBaseResponse<String> response = new DzDealBaseResponse<>();
        response.setSuccess(true);
        response.setData("{\"moduleConfigsModule\":{\"tortTitle\":\"当前团购已失效\"}}");
        when(dzDealBaseService.execute(any(), any())).thenReturn(response);
        // act
        ConsumeStatus result = consumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case for exception handling
     */
    @Test
    public void testRecvMessage_ExceptionHandling() throws Throwable {
        // arrange
        UnifiedMainInterfaceResultDTO dto = createValidDTO();
        String messageBody = JsonUtils.toJson(dto);
        MafkaMessage message = new MafkaMessage("topic", 0, 0, null, messageBody);
        MessagetContext context = new MessagetContext();
        when(dzDealBaseService.execute(any(), any())).thenThrow(new RuntimeException("Test exception"));
        // act
        ConsumeStatus result = consumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
