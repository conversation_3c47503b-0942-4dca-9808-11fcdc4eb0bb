package com.dianping.mobile.mapi.dztgdetail.biz.processor.promo;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pay.promo.common.enums.promo.DisplayScene;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.pay.promo.display.api.dto.request.ReturnControl;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class PromoProcessorBuildPromoReqTest {

    private PromoProcessor promoProcessor;

    private DealCtx dealCtx;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        promoProcessor = new PromoProcessor();
        dealCtx = Mockito.mock(DealCtx.class);
        envCtx = Mockito.mock(EnvCtx.class);
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
    }

    private QueryPromoDisplayRequest invokeBuildPromoReq(DealCtx ctx, int templateId, DisplayScene displayScene, Boolean merge) throws Throwable {
        Method method = PromoProcessor.class.getDeclaredMethod("buildPromoReq", DealCtx.class, int.class, DisplayScene.class, Boolean.class);
        method.setAccessible(true);
        return (QueryPromoDisplayRequest) method.invoke(promoProcessor, ctx, templateId, displayScene, merge);
    }

    /**
     * 测试异常场景：DisplayScene 为 null
     */
    @Test
    public void testBuildPromoReqDisplaySceneNull() throws Throwable {
        try {
            // act
            invokeBuildPromoReq(dealCtx, 239, null, true);
            fail("Expected NullPointerException to be thrown");
        } catch (InvocationTargetException e) {
            // assert
            assertTrue("Expected cause to be NullPointerException", e.getCause() instanceof NullPointerException);
        }
    }
}
