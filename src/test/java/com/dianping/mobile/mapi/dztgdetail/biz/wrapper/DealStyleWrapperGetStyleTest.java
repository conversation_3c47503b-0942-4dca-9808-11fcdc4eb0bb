package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;
import com.dianping.deal.style.dto.StyleResponse;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealStyleWrapperGetStyleTest {

    @Mock
    private Future future;

    private DealStyleWrapper dealStyleWrapper;

    @Before
    public void setUp() {
        dealStyleWrapper = new DealStyleWrapper();
    }

    /**
     * 测试getStyle方法，当styleFuture.get()能够正常返回结果时
     */
    @Test
    public void testGetStyleWhenGetMethodReturnsResult() throws Throwable {
        // arrange
        StyleResponse expectedResponse = new StyleResponse();
        when(future.get()).thenReturn(expectedResponse);
        // act
        StyleResponse actualResponse = dealStyleWrapper.getStyle(future);
        // assert
        assertEquals(expectedResponse, actualResponse);
    }

    /**
     * 测试getStyle方法，当styleFuture.get()抛出异常时
     */
    @Test
    public void testGetStyleWhenGetMethodThrowsException() throws Throwable {
        // arrange
        when(future.get()).thenThrow(new RuntimeException());
        // act
        StyleResponse actualResponse = dealStyleWrapper.getStyle(future);
        // assert
        assertNull(actualResponse);
    }
}
