package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.dianping.mobile.mapi.dztgdetail.entity.EyeServiceProcessNode;
import com.dianping.mobile.mapi.dztgdetail.entity.EyeServiceProcessNode;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SpecificModuleHandler_1603BuildResultTest {

    @Mock
    private SpecificModuleCtx mockCtx;

    @Mock
    private DealGroupDTO mockDealGroupDTO;

    @Mock
    private DealGroupServiceProjectDTO mockServiceProject;

    @Mock
    private MustServiceProjectGroupDTO mockMustGroups;

    @Mock
    private ServiceProjectDTO mockServiceProjectDTO;

    @Mock
    private ServiceProjectAttrDTO mockServiceProjectAttrDTO;

    private SpecificModuleHandler_1603 handler = new SpecificModuleHandler_1603();

    private SpecificModuleHandler_1603 specificModuleHandler_1603;

    @Before
    public void setUp() {
        specificModuleHandler_1603 = new SpecificModuleHandler_1603();
    }

    private DealDetailDisplayUnitVO invokeBuildServiceProcess(List<ServiceProjectAttrDTO> serviceProjectAttributes) throws Exception {
        Method method = SpecificModuleHandler_1603.class.getDeclaredMethod("buildServiceProcess", List.class);
        method.setAccessible(true);
        return (DealDetailDisplayUnitVO) method.invoke(specificModuleHandler_1603, serviceProjectAttributes);
    }

    @Test
    public void testBuildResultWithEmptyAttributes() throws Throwable {
        when(mockCtx.getDealGroupDTO()).thenReturn(null);
        DealDetailSpecificModuleVO result = handler.buildResult(mockCtx);
        assertNotNull(result);
        assertTrue(result.getUnits().isEmpty());
    }

    @Test
    public void testBuildResultWithNullDealGroupDTO() throws Throwable {
        when(mockCtx.getDealGroupDTO()).thenReturn(null);
        DealDetailSpecificModuleVO result = handler.buildResult(mockCtx);
        assertNotNull(result);
        assertTrue(result.getUnits().isEmpty());
    }

    @Test
    public void testBuildResultWithNullServiceProject() throws Throwable {
        when(mockCtx.getDealGroupDTO()).thenReturn(mockDealGroupDTO);
        when(mockDealGroupDTO.getServiceProject()).thenReturn(null);
        DealDetailSpecificModuleVO result = handler.buildResult(mockCtx);
        assertNotNull(result);
        assertTrue(result.getUnits().isEmpty());
    }

    @Test
    public void testBuildResultWithNullMustGroups() throws Throwable {
        when(mockCtx.getDealGroupDTO()).thenReturn(mockDealGroupDTO);
        when(mockDealGroupDTO.getServiceProject()).thenReturn(mockServiceProject);
        when(mockServiceProject.getMustGroups()).thenReturn(null);
        DealDetailSpecificModuleVO result = handler.buildResult(mockCtx);
        assertNotNull(result);
        assertTrue(result.getUnits().isEmpty());
    }

    @Test
    public void testBuildResultWithNullGroups() throws Throwable {
        when(mockCtx.getDealGroupDTO()).thenReturn(mockDealGroupDTO);
        when(mockDealGroupDTO.getServiceProject()).thenReturn(mockServiceProject);
        when(mockServiceProject.getMustGroups()).thenReturn(Collections.singletonList(mockMustGroups));
        when(mockMustGroups.getGroups()).thenReturn(null);
        DealDetailSpecificModuleVO result = handler.buildResult(mockCtx);
        assertNotNull(result);
        assertTrue(result.getUnits().isEmpty());
    }

    @Test
    public void testBuildResultWithNullServiceProjectDTO() throws Throwable {
        when(mockCtx.getDealGroupDTO()).thenReturn(mockDealGroupDTO);
        when(mockDealGroupDTO.getServiceProject()).thenReturn(mockServiceProject);
        when(mockServiceProject.getMustGroups()).thenReturn(Collections.singletonList(mockMustGroups));
        when(mockMustGroups.getGroups()).thenReturn(Collections.singletonList(mockServiceProjectDTO));
        when(mockServiceProjectDTO.getAttrs()).thenReturn(null);
        DealDetailSpecificModuleVO result = handler.buildResult(mockCtx);
        assertNotNull(result);
        assertTrue(result.getUnits().isEmpty());
    }

    @Test
    public void testBuildResultWithNullAttrs() throws Throwable {
        when(mockCtx.getDealGroupDTO()).thenReturn(mockDealGroupDTO);
        when(mockDealGroupDTO.getServiceProject()).thenReturn(mockServiceProject);
        when(mockServiceProject.getMustGroups()).thenReturn(Collections.singletonList(mockMustGroups));
        when(mockMustGroups.getGroups()).thenReturn(Collections.singletonList(mockServiceProjectDTO));
        when(mockServiceProjectDTO.getAttrs()).thenReturn(null);
        DealDetailSpecificModuleVO result = handler.buildResult(mockCtx);
        assertNotNull(result);
        assertTrue(result.getUnits().isEmpty());
    }

    @Test
    public void testBuildServiceProcessWhenServiceProjectAttributesIsEmpty() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Collections.emptyList();
        // act
        DealDetailDisplayUnitVO result = invokeBuildServiceProcess(serviceProjectAttributes);
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildServiceProcessWhenStandardServiceProcessAttrValueIsValidJsonButNodesIsEmpty() throws Throwable {
        // arrange
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("standardServiceProcess");
        serviceProjectAttrDTO.setAttrValue("[]");
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Collections.singletonList(serviceProjectAttrDTO);
        // act
        DealDetailDisplayUnitVO result = invokeBuildServiceProcess(serviceProjectAttributes);
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildServiceProcessWhenStandardServiceProcessAttrValueIsValidJsonAndNodesIsNotEmpty() throws Throwable {
        // arrange
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("standardServiceProcess");
        serviceProjectAttrDTO.setAttrValue("[{\"processName\":\"node1\",\"processDescription\":\"desc1\"}]");
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Collections.singletonList(serviceProjectAttrDTO);
        // act
        DealDetailDisplayUnitVO result = invokeBuildServiceProcess(serviceProjectAttributes);
        // assert
        assertNotNull(result);
        assertEquals("process", result.getType());
        assertEquals("服务流程", result.getTitle());
        assertEquals(1, result.getDisplayItems().size());
        assertEquals("node1", result.getDisplayItems().get(0).getName());
        assertEquals("desc1", result.getDisplayItems().get(0).getDesc());
    }

    @Test
    public void testBuildServiceProcessWhenServiceProjectAttributesNotContainsStandardServiceProcess() throws Throwable {
        // arrange
        ServiceProjectAttrDTO serviceProjectAttrDTO = new ServiceProjectAttrDTO();
        serviceProjectAttrDTO.setAttrName("other");
        serviceProjectAttrDTO.setAttrValue("{}");
        List<ServiceProjectAttrDTO> serviceProjectAttributes = Collections.singletonList(serviceProjectAttrDTO);
        // act
        DealDetailDisplayUnitVO result = invokeBuildServiceProcess(serviceProjectAttributes);
        // assert
        assertNull(result);
    }
}
