package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class DisposeDetailAdaptorTest {

    private DisposeDetailAdaptor adaptor;

    private DealCtx ctx;

    @Before
    public void setUp() {
        adaptor = new DisposeDetailAdaptor();
        ctx = mock(DealCtx.class);
    }

    /**
     * Test case for empty list of structured details.
     */
    @Test
    public void testProcessEmptyList() throws Throwable {
        // arrange
        when(ctx.getStructedDetails()).thenReturn(new ArrayList<>());
        // act
        adaptor.process(ctx);
        // assert
        verify(ctx).getStructedDetails();
        verifyNoMoreInteractions(ctx);
    }

    /**
     * Test case for list containing a null Pair.
     */
    @Test
    public void testProcessListWithNullPair() throws Throwable {
        // arrange
        List<Pair> pairs = new ArrayList<>();
        pairs.add(null);
        when(ctx.getStructedDetails()).thenReturn(pairs);
        // act
        adaptor.process(ctx);
        // assert
        verify(ctx).getStructedDetails();
        verifyNoMoreInteractions(ctx);
    }

    /**
     * Test case for list containing a Pair with an empty name.
     */
    @Test
    public void testProcessListWithEmptyName() throws Throwable {
        // arrange
        List<Pair> pairs = new ArrayList<>();
        Pair pair = new Pair("1", "", 1);
        pairs.add(pair);
        when(ctx.getStructedDetails()).thenReturn(pairs);
        // act
        adaptor.process(ctx);
        // assert
        verify(ctx).getStructedDetails();
        verifyNoMoreInteractions(ctx);
    }

    /**
     * Test case for list containing a Pair with name containing specific strings.
     */
    @Test
    public void testProcessListWithNameContainingSpecificStrings() throws Throwable {
        // arrange
        List<Pair> pairs = new ArrayList<>();
        Pair pair = new Pair("1", "<div><br></div><div><br></div><div><br></div>", 1);
        pairs.add(pair);
        when(ctx.getStructedDetails()).thenReturn(pairs);
        // act
        adaptor.process(ctx);
        // assert
        verify(ctx).getStructedDetails();
        // No need to verify Cat interactions
    }

    /**
     * Test case for list containing a Pair with name not containing specific strings.
     */
    @Test
    public void testProcessListWithNameNotContainingSpecificStrings() throws Throwable {
        // arrange
        List<Pair> pairs = new ArrayList<>();
        Pair pair = new Pair("1", "Some random text", 1);
        pairs.add(pair);
        when(ctx.getStructedDetails()).thenReturn(pairs);
        // act
        adaptor.process(ctx);
        // assert
        verify(ctx).getStructedDetails();
        // No need to verify Cat interactions
    }

    /**
     * Test case for list containing a Pair with name length greater than 2000.
     */
    @Test
    public void testProcessListWithNameLengthGreaterThan2000() throws Throwable {
        // arrange
        List<Pair> pairs = new ArrayList<>();
        StringBuilder longName = new StringBuilder();
        for (int i = 0; i < 2001; i++) {
            longName.append("a");
        }
        Pair pair = new Pair("1", longName.toString(), 1);
        pairs.add(pair);
        when(ctx.getStructedDetails()).thenReturn(pairs);
        // act
        adaptor.process(ctx);
        // assert
        verify(ctx).getStructedDetails();
        // No need to verify Cat interactions
    }
}
