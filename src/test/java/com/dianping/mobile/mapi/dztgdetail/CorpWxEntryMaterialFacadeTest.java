package com.dianping.mobile.mapi.dztgdetail;

import com.dianping.lion.client.Lion;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiShopCategoryWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetcorpwxentrymaterialRequest;
import com.dianping.mobile.mapi.dztgdetail.facade.CorpWxEntryMaterialFacade;
import com.sankuai.dz.srcm.flow.dto.FlowEntryWxMaterialRequest;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CorpWxEntryMaterialFacadeTest{

    @InjectMocks
    private CorpWxEntryMaterialFacade corpWxEntryMaterialFacade;

    @Mock
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    @Mock
    private PoiClientWrapper poiClientWrapper;

    private static final String LIMIT_SUIT_PERSON = "只适用于初次到店非商户会员使用";

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    @Test
    public void testBuildRequest() {
        System.out.println(LIMIT_SUIT_PERSON.equals("只适用于初次到店非商户会员使用"));
        GetcorpwxentrymaterialRequest req = new GetcorpwxentrymaterialRequest();
        req.setShopIdForLong(1L);
        req.setDealgroupid("1");
        req.setUserLat(1.0d);
        req.setUserlng(1.0d);
        req.setCityid(1);
        EnvCtx ctx = new EnvCtx();
        ctx.setMtUserId(1L);
        ctx.setClientType(200502);
        DpPoiDTO dpPoiDTO =new DpPoiDTO();
        when(poiClientWrapper.getDpPoiDTO(anyLong(), anyList())).thenReturn(dpPoiDTO);
        when(poiShopCategoryWrapper.getBackSecondMainCategory(any())).thenReturn(740);
        lionMockedStatic.when(() -> Lion.getList(Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.anyList())).thenReturn(Lists.newArrayList(740));
        when(mapperWrapper.fetchDpCityByMtCity(Mockito.anyInt())).thenReturn(1);
        FlowEntryWxMaterialRequest request = corpWxEntryMaterialFacade.buildRequest(req, ctx);
        assertNotNull(request);
    }
}
