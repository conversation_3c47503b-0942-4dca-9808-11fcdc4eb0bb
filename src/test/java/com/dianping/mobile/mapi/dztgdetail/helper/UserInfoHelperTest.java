package com.dianping.mobile.mapi.dztgdetail.helper;

import org.junit.Test;
import static org.junit.Assert.*;

public class UserInfoHelperTest {

    /**
     * 测试isMt为真的情况
     */
    @Test
    public void testGetDefaultAvatarUrlIsMtTrue() {
        // arrange
        boolean isMt = true;
        String userName = "test";
        // act
        String result = UserInfoHelper.getDefaultAvatarUrl(isMt, userName);
        // assert
        assertEquals("https://p0.meituan.net/ingee/4e78589c0aa6132682faae3f0438c8a4298832.png", result);
    }

    /**
     * 测试isMt为假，userName为null的情况
     */
    @Test
    public void testGetDefaultAvatarUrlIsMtFalseAndUserNameNull() {
        // arrange
        boolean isMt = false;
        String userName = null;
        // act
        String result = UserInfoHelper.getDefaultAvatarUrl(isMt, userName);
        // assert
        assertEquals("https://p0.meituan.net/ingee/0c8c3a9425dd6e925b58864216b72a9c5130.png", result);
    }

    /**
     * 测试isMt为假，userName不为null的情况
     */
    @Test
    public void testGetDefaultAvatarUrlIsMtFalseAndUserNameNotNull() {
        // arrange
        boolean isMt = false;
        String userName = "test";
        // act
        String result = UserInfoHelper.getDefaultAvatarUrl(isMt, userName);
        // assert
        assertTrue(result.startsWith("https://p0.meituan.net/ingee/"));
    }

    /**
     * 测试计算索引时发生异常的情况
     */
    @Test
    public void testGetDefaultAvatarUrlException() {
        // arrange
        boolean isMt = false;
        String userName = "";
        // act
        String result = UserInfoHelper.getDefaultAvatarUrl(isMt, userName);
        // assert
        assertEquals("https://p0.meituan.net/ingee/0c8c3a9425dd6e925b58864216b72a9c5130.png", result);
    }
}
