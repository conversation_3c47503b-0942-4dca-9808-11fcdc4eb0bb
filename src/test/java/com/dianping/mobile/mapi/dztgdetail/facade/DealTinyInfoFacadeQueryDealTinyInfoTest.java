package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzDealThemeWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PriceRangeQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealTinyInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealTinyInfoVO;
import com.google.common.collect.Lists;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class DealTinyInfoFacadeQueryDealTinyInfoTest {

    @InjectMocks
    private DealTinyInfoFacade dealTinyInfoFacade;

    @Mock
    private SkuWrapper skuWrapper;

    @Mock
    private DzDealThemeWrapper dzDealThemeWrapper;

    @Mock
    private PriceRangeQueryWrapper priceRangeQueryWrapper;

    @Mock
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private HaimaWrapper haimaWrapper;

    @Mock
    private DouHuService douHuService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private void invokeCheckRequest(GetDealTinyInfoRequest request) throws Exception {
        Method method = DealTinyInfoFacade.class.getDeclaredMethod("checkRequest", GetDealTinyInfoRequest.class);
        method.setAccessible(true);
        method.invoke(dealTinyInfoFacade, request);
    }

    private String invokeGetDealProductAttrValue(List<DealProductAttrDTO> attrs, String name) throws Throwable {
        try {
            Method method = DealTinyInfoFacade.class.getDeclaredMethod("getDealProductAttrValue", List.class, String.class);
            method.setAccessible(true);
            return (String) method.invoke(dealTinyInfoFacade, attrs, name);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            throw e.getCause();
        }
    }

    /**
     * 测试正常流程：所有步骤都成功执行，返回有效的 DealTinyInfoVO
     */
    @Test
    public void testQueryDealTinyInfoNormalFlow() throws Throwable {
        // arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("123");
        request.setShopId("456");
        EnvCtx envCtx = new EnvCtx();
        DealSkuSummaryDTO skuSummary = new DealSkuSummaryDTO();
        skuSummary.setDefaultSkuId(789L);
        when(skuWrapper.getSkuSummaryByDealId(123, IdTypeEnum.MT)).thenReturn(skuSummary);
        Future<DealProductResult> future = mock(Future.class);
        when(dzDealThemeWrapper.preQueryDealProduct(any())).thenReturn(future);
        DealProductResult dealProductResult = new DealProductResult();
        DealProductDTO dealProductDTO = new DealProductDTO();
        dealProductDTO.setProductId(123);
        dealProductDTO.setName("Test Deal");
        dealProductDTO.setHeadPic("http://example.com/image.jpg");
        dealProductDTO.setOrderUrl("http://example.com/order");
        // Set promoPrice
        dealProductDTO.setPromoPrice(new BigDecimal("100.00"));
        // Set marketPriceTag to avoid division by zero
        dealProductDTO.setMarketPriceTag("200.00");
        dealProductResult.setDeals(Collections.singletonList(dealProductDTO));
        when(dzDealThemeWrapper.getFutureResult(future)).thenReturn(dealProductResult);
        // Use reflection to invoke private method
        Method queryDealTinyInfoMethod = DealTinyInfoFacade.class.getDeclaredMethod("queryDealTinyInfo", GetDealTinyInfoRequest.class, EnvCtx.class);
        queryDealTinyInfoMethod.setAccessible(true);
        DealTinyInfoVO result = (DealTinyInfoVO) queryDealTinyInfoMethod.invoke(dealTinyInfoFacade, request, envCtx);
        // assert
        assertNotNull(result);
        assertEquals(123, result.getDealGroupId().intValue());
        assertEquals("Test Deal", result.getTitle());
        assertEquals("http://example.com/image.jpg", result.getHeadPic());
        assertEquals("http://example.com/order", result.getDirectBuyJumpUrl());
    }

    /**
     * 测试异常流程：dealGroupId 无效，抛出 NumberFormatException
     */
    @Test(expected = NumberFormatException.class)
    public void testQueryDealTinyInfoInvalidDealGroupId() throws Throwable {
        // arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("invalid");
        request.setShopId("456");
        EnvCtx envCtx = new EnvCtx();
        // Use reflection to invoke private method
        Method queryDealTinyInfoMethod = DealTinyInfoFacade.class.getDeclaredMethod("queryDealTinyInfo", GetDealTinyInfoRequest.class, EnvCtx.class);
        queryDealTinyInfoMethod.setAccessible(true);
        try {
            queryDealTinyInfoMethod.invoke(dealTinyInfoFacade, request, envCtx);
        } catch (InvocationTargetException e) {
            throw e.getCause();
        }
    }

    /**
     * 测试异常流程：skuWrapper.getSkuSummaryByDealId 返回 null
     */
    @Test
    public void testQueryDealTinyInfoSkuSummaryNull() throws Throwable {
        // arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("123");
        request.setShopId("456");
        EnvCtx envCtx = new EnvCtx();
        when(skuWrapper.getSkuSummaryByDealId(123, IdTypeEnum.MT)).thenReturn(null);
        // Use reflection to invoke private method
        Method queryDealTinyInfoMethod = DealTinyInfoFacade.class.getDeclaredMethod("queryDealTinyInfo", GetDealTinyInfoRequest.class, EnvCtx.class);
        queryDealTinyInfoMethod.setAccessible(true);
        DealTinyInfoVO result = (DealTinyInfoVO) queryDealTinyInfoMethod.invoke(dealTinyInfoFacade, request, envCtx);
        // assert
        assertNull(result);
    }

    /**
     * 测试异常流程：dzDealThemeWrapper.preQueryDealProduct 返回 null
     */
    @Test
    public void testQueryDealTinyInfoPreQueryDealProductNull() throws Throwable {
        // arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("123");
        request.setShopId("456");
        EnvCtx envCtx = new EnvCtx();
        DealSkuSummaryDTO skuSummary = new DealSkuSummaryDTO();
        skuSummary.setDefaultSkuId(789L);
        when(skuWrapper.getSkuSummaryByDealId(123, IdTypeEnum.MT)).thenReturn(skuSummary);
        when(dzDealThemeWrapper.preQueryDealProduct(any())).thenReturn(null);
        // Use reflection to invoke private method
        Method queryDealTinyInfoMethod = DealTinyInfoFacade.class.getDeclaredMethod("queryDealTinyInfo", GetDealTinyInfoRequest.class, EnvCtx.class);
        queryDealTinyInfoMethod.setAccessible(true);
        DealTinyInfoVO result = (DealTinyInfoVO) queryDealTinyInfoMethod.invoke(dealTinyInfoFacade, request, envCtx);
        // assert
        assertNull(result);
    }

    /**
     * 测试异常流程：dzDealThemeWrapper.getFutureResult 返回 null
     */
    @Test
    public void testQueryDealTinyInfoGetFutureResultNull() throws Throwable {
        // arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("123");
        request.setShopId("456");
        EnvCtx envCtx = new EnvCtx();
        DealSkuSummaryDTO skuSummary = new DealSkuSummaryDTO();
        skuSummary.setDefaultSkuId(789L);
        when(skuWrapper.getSkuSummaryByDealId(123, IdTypeEnum.MT)).thenReturn(skuSummary);
        Future<DealProductResult> future = mock(Future.class);
        when(dzDealThemeWrapper.preQueryDealProduct(any())).thenReturn(future);
        when(dzDealThemeWrapper.getFutureResult(future)).thenReturn(null);
        // Use reflection to invoke private method
        Method queryDealTinyInfoMethod = DealTinyInfoFacade.class.getDeclaredMethod("queryDealTinyInfo", GetDealTinyInfoRequest.class, EnvCtx.class);
        queryDealTinyInfoMethod.setAccessible(true);
        DealTinyInfoVO result = (DealTinyInfoVO) queryDealTinyInfoMethod.invoke(dealTinyInfoFacade, request, envCtx);
        // assert
        assertNull(result);
    }

    /**
     * 测试异常流程：getDealResult 返回 null
     */
    @Test
    public void testQueryDealTinyInfoGetDealResultNull() throws Throwable {
        // arrange
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("123");
        request.setShopId("456");
        EnvCtx envCtx = new EnvCtx();
        DealSkuSummaryDTO skuSummary = new DealSkuSummaryDTO();
        skuSummary.setDefaultSkuId(789L);
        when(skuWrapper.getSkuSummaryByDealId(123, IdTypeEnum.MT)).thenReturn(skuSummary);
        Future<DealProductResult> future = mock(Future.class);
        when(dzDealThemeWrapper.preQueryDealProduct(any())).thenReturn(future);
        DealProductResult dealProductResult = new DealProductResult();
        when(dzDealThemeWrapper.getFutureResult(future)).thenReturn(dealProductResult);
        // Use reflection to invoke private method
        Method queryDealTinyInfoMethod = DealTinyInfoFacade.class.getDeclaredMethod("queryDealTinyInfo", GetDealTinyInfoRequest.class, EnvCtx.class);
        queryDealTinyInfoMethod.setAccessible(true);
        DealTinyInfoVO result = (DealTinyInfoVO) queryDealTinyInfoMethod.invoke(dealTinyInfoFacade, request, envCtx);
        // assert
        assertNull(result);
    }

    @Test
    public void testCheckRequestDealGroupIdIsNull() throws Throwable {
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId(null);
        request.setPageSource("validPageSource");
        try {
            invokeCheckRequest(request);
            fail("Expected IllegalArgumentException to be thrown");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof IllegalArgumentException);
        }
    }

    @Test
    public void testCheckRequestDealGroupIdIsNotPositive() throws Throwable {
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("0");
        request.setPageSource("validPageSource");
        try {
            invokeCheckRequest(request);
            fail("Expected IllegalArgumentException to be thrown");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof IllegalArgumentException);
        }
    }

    @Test
    public void testCheckRequestPageSourceIsNull() throws Throwable {
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("1");
        request.setPageSource(null);
        try {
            invokeCheckRequest(request);
            fail("Expected IllegalArgumentException to be thrown");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof IllegalArgumentException);
        }
    }

    @Test
    public void testCheckRequestAllParamsAreValid() throws Throwable {
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setDealGroupId("1");
        request.setPageSource("validPageSource");
        try {
            invokeCheckRequest(request);
        } catch (IllegalArgumentException e) {
            fail("Should not throw exception when all parameters are valid");
        }
    }

    @Test
    public void testGetDealProductAttrValue_EmptyAttrs() throws Throwable {
        List<DealProductAttrDTO> attrs = Collections.emptyList();
        String name = "testAttr";
        String result = invokeGetDealProductAttrValue(attrs, name);
        assertNotNull(result);
        assertEquals("", result);
    }

    @Test
    public void testGetDealProductAttrValue_NoMatchingAttr() throws Throwable {
        DealProductAttrDTO attr1 = new DealProductAttrDTO();
        attr1.setName("attr1");
        attr1.setValue("value1");
        DealProductAttrDTO attr2 = new DealProductAttrDTO();
        attr2.setName("attr2");
        attr2.setValue("value2");
        List<DealProductAttrDTO> attrs = Lists.newArrayList(attr1, attr2);
        String name = "testAttr";
        String result = invokeGetDealProductAttrValue(attrs, name);
        assertNotNull(result);
        assertEquals("", result);
    }

    @Test
    public void testGetDealProductAttrValue_MatchingAttr() throws Throwable {
        DealProductAttrDTO attr1 = new DealProductAttrDTO();
        attr1.setName("attr1");
        attr1.setValue("value1");
        DealProductAttrDTO attr2 = new DealProductAttrDTO();
        attr2.setName("attr2");
        attr2.setValue("value2");
        List<DealProductAttrDTO> attrs = Lists.newArrayList(attr1, attr2);
        String name = "attr2";
        String result = invokeGetDealProductAttrValue(attrs, name);
        assertNotNull(result);
        assertEquals("value2", result);
    }
}
