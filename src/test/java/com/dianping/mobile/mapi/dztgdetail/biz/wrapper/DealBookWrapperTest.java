package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.book.api.DealBookQueryService;
import com.dianping.deal.book.dto.Resp;
import com.dianping.deal.book.req.BookQueryRequest;
import com.dianping.deal.book.req.ShopBookDto;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.clr.content.process.gateway.thrift.api.LeadsQueryGatewayService;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.clr.content.process.thrift.api.ShopBookInfoProcessService;
import com.sankuai.clr.content.process.thrift.dto.ShopBookInfoProcessDTO;
import com.sankuai.clr.content.process.thrift.dto.ShopBookInfoQueryByShopIdProcessRespDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealBookWrapperTest {

    @InjectMocks
    private DealBookWrapper dealBookWrapper;

    @Mock
    private DealBookQueryService dealBookQueryServiceFuture;

    @Mock
    private LeadsQueryGatewayService leadsQueryGatewayServiceFuture;

    @Mock
    private ShopBookInfoProcessService shopBookInfoProcessService;

    @Mock
    private Future future;

    private MockedStatic<FutureFactory> futureFactoryMockedStatic;

    public DealBookWrapperTest() {
        MockitoAnnotations.initMocks(this);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @After
    public void tearDown() {
        if (futureFactoryMockedStatic != null) {
            futureFactoryMockedStatic.close();
        }
    }

    @Test
    public void testQueryLongShopDealBooKInfoRespIsNotSuccess() throws Throwable {
        Resp<Map<Long, ShopBookDto>> resp = new Resp<>(false, "error", null);
        CompletableFuture<Resp<Map<Long, ShopBookDto>>> future = CompletableFuture.completedFuture(resp);
        when(dealBookQueryServiceFuture.batchQueryShopDealBookInfoV2(any(BookQueryRequest.class))).thenAnswer(invocation -> future);
        Map<Long, ShopBookDto> result = dealBookWrapper.queryLongShopDealBooKInfo(new BookQueryRequest());
        assertNull(result);
    }

    @Test
    public void testQueryLongShopDealBooKInfoFutureIsNull() throws Throwable {
        when(dealBookWrapper.prepareLongShopDealBooKInfo(any(BookQueryRequest.class))).thenReturn(null);
        Map<Long, ShopBookDto> result = dealBookWrapper.queryLongShopDealBooKInfo(new BookQueryRequest());
        assertNull(result);
    }

    /**
     * 测试 prepareShopDealBooKInfo 方法，正常情况
     */
    @Test
    @Ignore
    public void testPrepareShopDealBooKInfoNormal() throws Throwable {
        // arrange
        BookQueryRequest bookQueryRequest = new BookQueryRequest();
        Future future = FutureFactory.getFuture();
        when(dealBookQueryServiceFuture.batchQueryShopDealBookInfoV2(bookQueryRequest)).thenReturn(null);
        // act
        Future result = dealBookWrapper.prepareShopDealBooKInfo(bookQueryRequest);
        // assert
        verify(dealBookQueryServiceFuture, times(1)).batchQueryShopDealBookInfoV2(bookQueryRequest);
        assertEquals(future, result);
    }

    /**
     * 测试 prepareShopDealBooKInfo 方法，异常情况
     */
    @Test
    public void testPrepareShopDealBooKInfoException() throws Throwable {
        // arrange
        BookQueryRequest bookQueryRequest = new BookQueryRequest();
        when(dealBookQueryServiceFuture.batchQueryShopDealBookInfoV2(bookQueryRequest)).thenThrow(new RuntimeException());
        // act
        Future result = dealBookWrapper.prepareShopDealBooKInfo(bookQueryRequest);
        // assert
        verify(dealBookQueryServiceFuture, times(1)).batchQueryShopDealBookInfoV2(bookQueryRequest);
        assertNull(result);
    }

    @Test
    public void testQueryShopDealBooKInfoFutureIsNull() throws Throwable {
        long shopId = 1L;
        ShopBookDto result = dealBookWrapper.queryShopDealBooKInfo(shopId, null);
        assertNull(result);
    }

    @Test
    public void testQueryShopDealBooKInfoFutureIsNotNullButThrowException() throws Throwable {
        long shopId = 1L;
        when(future.get()).thenThrow(new InterruptedException());
        ShopBookDto result = dealBookWrapper.queryShopDealBooKInfo(shopId, future);
        assertNull(result);
    }

    @Test
    public void testQueryShopDealBooKInfoFutureIsNotNullAndGetResultSuccessButResultIsNull() throws Throwable {
        long shopId = 1L;
        when(future.get()).thenReturn(null);
        ShopBookDto result = dealBookWrapper.queryShopDealBooKInfo(shopId, future);
        assertNull(result);
    }

    @Test
    public void testQueryShopDealBooKInfoFutureIsNotNullAndGetResultSuccessAndResultIsNotNullButContentIsEmpty() throws Throwable {
        long shopId = 1L;
        Resp<Map<Long, ShopBookDto>> resp = new Resp<>(true, "Success", new HashMap<>());
        when(future.get()).thenReturn(resp);
        ShopBookDto result = dealBookWrapper.queryShopDealBooKInfo(shopId, future);
        assertNull(result);
    }

    @Test
    public void testQueryShopDealBooKInfoFutureIsNotNullAndGetResultSuccessAndResultIsNotNullAndContentIsNotEmptyButNoShopId() throws Throwable {
        long shopId = 1L;
        Map<Long, ShopBookDto> content = new HashMap<>();
        content.put(2L, new ShopBookDto());
        Resp<Map<Long, ShopBookDto>> resp = new Resp<>(true, "Success", content);
        when(future.get()).thenReturn(resp);
        ShopBookDto result = dealBookWrapper.queryShopDealBooKInfo(shopId, future);
        assertNull(result);
    }

    @Test
    public void testQueryShopDealBooKInfoFutureIsNotNullAndGetResultSuccessAndResultIsNotNullAndContentIsNotEmptyAndHasShopId() throws Throwable {
        long shopId = 1L;
        Map<Long, ShopBookDto> content = new HashMap<>();
        ShopBookDto shopBookDto = new ShopBookDto();
        content.put(shopId, shopBookDto);
        Resp<Map<Long, ShopBookDto>> resp = new Resp<>(true, "Success", content);
        when(future.get()).thenReturn(resp);
        ShopBookDto result = dealBookWrapper.queryShopDealBooKInfo(shopId, future);
        assertSame(shopBookDto, result);
    }

    /**
     * 测试 prepareLongShopDealBooKInfo 方法，正常情况
     */
    @Test
    public void testPrepareLongShopDealBooKInfoNormal() throws Throwable {
        // arrange
        BookQueryRequest bookQueryRequest = new BookQueryRequest();
        Resp mockResp = new Resp(true, "Success");
        when(dealBookQueryServiceFuture.batchQueryShopDealBookInfoV2(bookQueryRequest)).thenReturn(mockResp);
        futureFactoryMockedStatic = mockStatic(FutureFactory.class);
        futureFactoryMockedStatic.when(FutureFactory::getFuture).thenReturn(future);
        // act
        Future result = dealBookWrapper.prepareLongShopDealBooKInfo(bookQueryRequest);
        // assert
        verify(dealBookQueryServiceFuture, times(1)).batchQueryShopDealBookInfoV2(bookQueryRequest);
        assertNotNull(result);
    }

    /**
     * 测试 prepareLongShopDealBooKInfo 方法，异常情况
     */
    @Test
    public void testPrepareLongShopDealBooKInfoException() throws Throwable {
        // arrange
        BookQueryRequest bookQueryRequest = new BookQueryRequest();
        when(dealBookQueryServiceFuture.batchQueryShopDealBookInfoV2(bookQueryRequest)).thenThrow(new RuntimeException());
        // act
        Future result = dealBookWrapper.prepareLongShopDealBooKInfo(bookQueryRequest);
        // assert
        verify(dealBookQueryServiceFuture, times(1)).batchQueryShopDealBookInfoV2(bookQueryRequest);
        assertNull(result);
    }

    /**
     * 测试 prepareLoadLeadsPopupInfo 方法，正常情况
     */
    @Test
    public void testPrepareLoadLeadsInfo() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        when(leadsQueryGatewayServiceFuture.loadLeadsInfo(any())).thenReturn(null);
        Future result = dealBookWrapper.prepareLoadLeadsInfo(ctx);
        verify(leadsQueryGatewayServiceFuture, times(1)).loadLeadsInfo(any());
    }

    @Test
    public void testQueryLoadLeadsInfo() throws Throwable {
        LoadLeadsInfoRespDTO resp = (LoadLeadsInfoRespDTO) new LoadLeadsInfoRespDTO().success();
        CompletableFuture<LoadLeadsInfoRespDTO> future = CompletableFuture.completedFuture(resp);
        LoadLeadsInfoRespDTO result = dealBookWrapper.queryLoadLeadsInfo(future);
        assertNotNull(result);
    }


    @Test
    public void testPreQueryShopBookInfo() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        when(shopBookInfoProcessService.queryByShopId(any())).thenReturn(new ShopBookInfoQueryByShopIdProcessRespDTO());
        Future result = dealBookWrapper.preQueryShopBookInfo(ctx);
        assertNull(result);
    }

    @Test
    public void testQueryShopBookInfo() throws Throwable {
        ShopBookInfoQueryByShopIdProcessRespDTO resp = new ShopBookInfoQueryByShopIdProcessRespDTO();
        CompletableFuture<ShopBookInfoQueryByShopIdProcessRespDTO> future = CompletableFuture.completedFuture(resp);
        ShopBookInfoProcessDTO result = dealBookWrapper.queryShopBookInfo(future);
        assertNull(result);
    }
}
