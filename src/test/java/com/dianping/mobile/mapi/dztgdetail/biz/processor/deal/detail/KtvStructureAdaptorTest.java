package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import java.util.Arrays;
import java.util.Collections;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class KtvStructureAdaptorTest {

    @InjectMocks
    private KtvStructureAdaptor ktvStructureAdaptor;

    @Mock
    private DealCtx ctx;

    /**
     * 测试场景：dpCates 为空，返回 false
     */
    @Test
    public void testIsKtvDealWithNewDetail_EmptyDpCates() throws Throwable {
        // arrange
        when(ctx.getAttrs()).thenReturn(Collections.emptyList());
        // act
        boolean result = ktvStructureAdaptor.isKtvDealWithNewDetail(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：dpCates 包含 3002，返回 true
     */
    @Test
    public void testIsKtvDealWithNewDetail_Contains3002() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        attr.setName("category");
        attr.setValue(Arrays.asList("3002"));
        when(ctx.getAttrs()).thenReturn(Arrays.asList(attr));
        // act
        boolean result = ktvStructureAdaptor.isKtvDealWithNewDetail(ctx);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：dpCates 不包含 3002，返回 false
     */
    @Test
    public void testIsKtvDealWithNewDetail_NotContains3002() throws Throwable {
        // arrange
        AttributeDTO attr = new AttributeDTO();
        attr.setName("category");
        attr.setValue(Arrays.asList("3001", "3003"));
        when(ctx.getAttrs()).thenReturn(Arrays.asList(attr));
        // act
        boolean result = ktvStructureAdaptor.isKtvDealWithNewDetail(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：ctx 为 null，抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testIsKtvDealWithNewDetail_NullCtx() throws Throwable {
        // arrange
        DealCtx nullCtx = null;
        // act
        ktvStructureAdaptor.isKtvDealWithNewDetail(nullCtx);
        // assert
        // 期望抛出 NullPointerException
    }
}
