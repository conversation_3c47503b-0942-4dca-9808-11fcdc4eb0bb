package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.rule.api.dto.Response;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoWrapper_QueryPromoDisplayDTOTest {

    @InjectMocks
    private PromoWrapper promoWrapper;

    @Mock
    private Future future;

    @Mock
    private Response<List<PromoDisplayDTO>> response;

    /**
     * Tests the scenario where the Future result is null.
     */
    @Test
    public void testQueryPromoDisplayDTOWhenFutureResultIsNull() throws Throwable {
        when(future.get()).thenReturn(null);
        List<PromoDisplayDTO> result = promoWrapper.queryPromoDisplayDTO(future);
        assertNull(result);
    }
}
