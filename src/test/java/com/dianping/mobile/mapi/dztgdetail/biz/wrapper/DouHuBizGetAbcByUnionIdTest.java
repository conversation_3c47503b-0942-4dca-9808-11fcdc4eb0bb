package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.lion.facade.LionFacade;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.client.DouHuClient;
import com.sankuai.douhu.absdk.enums.ErrorCode;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DouHuBizGetAbcByUnionIdTest {

    @InjectMocks
    private DouHuBiz douHuBiz;

    @Mock
    private DouHuClient douHuClient;

    @Before
    public void setUp() throws Exception {
        // Use reflection to set private field
        Field douHuClientField = DouHuBiz.class.getDeclaredField("douHuClient");
        douHuClientField.setAccessible(true);
        douHuClientField.set(douHuBiz, douHuClient);
    }

    /**
     * Test when module is empty
     */
    @Test
    public void testGetAbcByUnionIdWhenModuleIsEmpty() throws Throwable {
        // arrange
        String unionId = "unionId";
        String module = "";
        boolean isMt = true;
        // act
        ModuleAbConfig result = douHuBiz.getAbcByUnionId(unionId, module, isMt);
        // assert
        assertNull(result);
    }

    /**
     * Test when expId is empty from Lion config
     */
    @Test
    public void testGetAbcByUnionIdWhenExpIdIsEmpty() throws Throwable {
        // arrange
        String unionId = "unionId";
        String module = "emptyModule";
        boolean isMt = true;
        // act
        ModuleAbConfig result = douHuBiz.getAbcByUnionId(unionId, module, isMt);
        // assert
        assertNull(result);
    }

    /**
     * Test when DouHuResponse is null
     */
    @Test
    public void testGetAbcByUnionIdWhenResponseIsNull() throws Throwable {
        // arrange
        String unionId = "unionId";
        String module = "module";
        boolean isMt = true;
        when(douHuClient.tryAb(any())).thenReturn(null);
        // act
        ModuleAbConfig result = douHuBiz.getAbcByUnionId(unionId, module, isMt);
        // assert
        assertNull(result);
    }

    /**
     * Test when sk is empty in response
     */
    @Test
    public void testGetAbcByUnionIdWhenSkIsEmpty() throws Throwable {
        // arrange
        String unionId = "unionId";
        String module = "module";
        boolean isMt = true;
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("");
        when(douHuClient.tryAb(any())).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbcByUnionId(unionId, module, isMt);
        // assert
        assertNull(result);
    }

    /**
     * Test when sk contains EXP_RES_SUFFIX_B
     */
    @Test
    public void testGetAbcByUnionIdWhenSkContainsExpResSuffixB() throws Throwable {
        // arrange
        String unionId = "unionId";
        String module = "module";
        boolean isMt = true;
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("test_b");
        when(douHuClient.tryAb(any())).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbcByUnionId(unionId, module, isMt);
        // assert
        assertNotNull(result);
        assertEquals("b", result.getConfigs().get(0).getExpResult());
    }

    /**
     * Test when sk contains EXP_RES_SUFFIX_C
     */
    @Test
    public void testGetAbcByUnionIdWhenSkContainsExpResSuffixC() throws Throwable {
        // arrange
        String unionId = "unionId";
        String module = "module";
        boolean isMt = true;
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("test_c");
        when(douHuClient.tryAb(any())).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbcByUnionId(unionId, module, isMt);
        // assert
        assertNotNull(result);
        assertEquals("c", result.getConfigs().get(0).getExpResult());
    }

    /**
     * Test when sk does not contain EXP_RES_SUFFIX_B or EXP_RES_SUFFIX_C
     */
    @Test
    public void testGetAbcByUnionIdWhenSkNotContainsExpResSuffixBAndC() throws Throwable {
        // arrange
        String unionId = "unionId";
        String module = "module";
        boolean isMt = true;
        DouHuResponse response = new DouHuResponse();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setSk("test");
        when(douHuClient.tryAb(any())).thenReturn(response);
        // act
        ModuleAbConfig result = douHuBiz.getAbcByUnionId(unionId, module, isMt);
        // assert
        assertNotNull(result);
        assertEquals("a", result.getConfigs().get(0).getExpResult());
    }
}
