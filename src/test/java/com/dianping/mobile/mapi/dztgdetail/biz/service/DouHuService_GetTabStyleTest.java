package com.dianping.mobile.mapi.dztgdetail.biz.service;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.entity.ExpResultConfig;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DouHuService_GetTabStyleTest {

    @InjectMocks
    private DouHuService douHuService;

    @Mock
    private DouHuBiz douHuBiz;

    public DouHuService_GetTabStyleTest() {
        MockitoAnnotations.initMocks(this);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetTabStyleExpResultConfigIsNull() throws Throwable {
        assertNull(douHuService.getTabStyle(null, 1, new ModuleAbConfig()));
    }

    @Test
    public void testGetTabStyleEnableStyleIsFalse() throws Throwable {
        ExpResultConfig expResultConfig = new ExpResultConfig();
        expResultConfig.setEnableStyle(false);
        assertNull(douHuService.getTabStyle(expResultConfig, 1, new ModuleAbConfig()));
    }

    @Test
    public void testGetTabStyleAllPassStyleIsNotBlank() throws Throwable {
        ExpResultConfig expResultConfig = new ExpResultConfig();
        expResultConfig.setEnableStyle(true);
        expResultConfig.setAllPassStyle("testStyle");
        assertEquals("testStyle", douHuService.getTabStyle(expResultConfig, 1, new ModuleAbConfig()));
    }

    @Test
    public void testGetTabStyleAllPassStyleIsBlankAndKey2StyleIsEmpty() throws Throwable {
        ExpResultConfig expResultConfig = new ExpResultConfig();
        expResultConfig.setEnableStyle(true);
        assertNull(douHuService.getTabStyle(expResultConfig, 1, new ModuleAbConfig()));
    }

    @Test
    public void testGetTabStyleAllPassStyleIsBlankAndKey2StyleIsNotEmptyButNoMatch() throws Throwable {
        ExpResultConfig expResultConfig = new ExpResultConfig();
        expResultConfig.setEnableStyle(true);
        expResultConfig.setKey2Style(Collections.singletonMap("otherKey", "otherStyle"));
        assertNull(douHuService.getTabStyle(expResultConfig, 1, new ModuleAbConfig()));
    }

    @Test
    public void testGetTabStyleAllPassStyleIsBlankAndKey2StyleIsNotEmptyAndMatch() throws Throwable {
        ExpResultConfig expResultConfig = new ExpResultConfig();
        expResultConfig.setEnableStyle(true);
        // Adjusted the key to match the format "expId_expResult"
        expResultConfig.setKey2Style(Collections.singletonMap("1_testStyle", "testStyle"));
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpId("1");
        // This is crucial for matching
        abConfig.setExpResult("testStyle");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        assertEquals("testStyle", douHuService.getTabStyle(expResultConfig, 1, moduleAbConfig));
    }

    @Test(expected = NullPointerException.class)
    public void testGetNativeDealDetailAbTestResultWhenCtxIsNull() throws Throwable {
        // Expecting NullPointerException due to the null context
        douHuService.getNativeDealDetailAbTestResult(null);
    }

    @Test
    public void testGetNativeDealDetailAbTestResultWhenUnionIdIsNull() throws Throwable {
        EnvCtx ctx = new EnvCtx();
        ModuleAbConfig result = douHuService.getNativeDealDetailAbTestResult(ctx);
        assertNull(result);
    }

    @Test
    public void testGetNativeDealDetailAbTestResultWhenIsMtIsTrue() throws Throwable {
        EnvCtx ctx = new EnvCtx();
        ctx.setUnionId("unionId");
        ctx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP);
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(null);
        ModuleAbConfig result = douHuService.getNativeDealDetailAbTestResult(ctx);
        assertNull(result);
    }

    @Test
    public void testGetNativeDealDetailAbTestResultWhenIsMtIsFalse() throws Throwable {
        EnvCtx ctx = new EnvCtx();
        ctx.setUnionId("unionId");
        ctx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.DIANPING_APP);
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(null);
        ModuleAbConfig result = douHuService.getNativeDealDetailAbTestResult(ctx);
        assertNull(result);
    }

    @Test
    public void testGetNativeDealDetailAbTestResultWhenGetAbByUnionIdReturnsNull() throws Throwable {
        EnvCtx ctx = new EnvCtx();
        ctx.setUnionId("unionId");
        ctx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP);
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(null);
        ModuleAbConfig result = douHuService.getNativeDealDetailAbTestResult(ctx);
        assertNull(result);
    }

    @Test
    public void testGetNativeDealDetailAbTestResultWhenGetAbByUnionIdReturnsNonNull() throws Throwable {
        EnvCtx ctx = new EnvCtx();
        ctx.setUnionId("unionId");
        ctx.setDztgClientTypeEnum(com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum.MEITUAN_APP);
        ModuleAbConfig expected = new ModuleAbConfig();
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(expected);
        ModuleAbConfig result = douHuService.getNativeDealDetailAbTestResult(ctx);
        assertEquals(expected, result);
    }

    /**
     * Tests getMiniVisionStyleExpResult method when getMiniVisionStyleAbTestSwitch returns non-null and expResult is "b", should return true.
     */
    @Test
    public void testGetMiniVisionStyleExpResultExpResultB() throws Throwable {
        // Arrange
        String unionId = "testUnionId";
        // Ensure this matches the condition in getMiniVisionStyleAbTestSwitch
        String pageSource = "mini_vision";
        boolean isMt = true;
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("b");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionIdAndExpId(eq(unionId), anyString(), anyString(), eq(isMt))).thenReturn(moduleAbConfig);
        // Act
        boolean result = douHuService.getMiniVisionStyleExpResult(unionId, pageSource, isMt);
        // Assert
        assertTrue(result);
    }

    /**
     * Tests getMiniVisionStyleExpResult method when getMiniVisionStyleAbTestSwitch returns null, should return false.
     */
    @Test
    public void testGetMiniVisionStyleExpResultReturnNull() throws Throwable {
        // Arrange
        String unionId = "testUnionId";
        String pageSource = "testPageSource";
        boolean isMt = true;
        // Act
        boolean result = douHuService.getMiniVisionStyleExpResult(unionId, pageSource, isMt);
        // Assert
        assertFalse(result);
    }

    /**
     * Tests getMiniVisionStyleExpResult method when getMiniVisionStyleAbTestSwitch returns non-null but expResult is not "b", should return false.
     */
    @Test
    public void testGetMiniVisionStyleExpResultExpResultNotB() throws Throwable {
        // Arrange
        String unionId = "testUnionId";
        // Ensure this matches the condition in getMiniVisionStyleAbTestSwitch
        String pageSource = "mini_vision";
        boolean isMt = true;
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("notB");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionIdAndExpId(eq(unionId), anyString(), anyString(), eq(isMt))).thenReturn(moduleAbConfig);
        // Act
        boolean result = douHuService.getMiniVisionStyleExpResult(unionId, pageSource, isMt);
        // Assert
        assertFalse(result);
    }

    @Test
    public void testGetCompareSameShopPriceStyleAbConfig_ExpResultConfigIsNull() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        Integer categoryId = 1;
        boolean isMt = true;
        // Use reflection to invoke the private method
        Method method = DouHuService.class.getDeclaredMethod("getCompareSameShopPriceStyleAbConfig", String.class, Integer.class, boolean.class);
        method.setAccessible(true);
        ModuleAbConfig result = (ModuleAbConfig) method.invoke(douHuService, unionId, categoryId, isMt);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetCompareSameShopPriceStyleAbConfig_CategoryId2ModuleIsEmpty() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        Integer categoryId = 1;
        boolean isMt = true;
        ExpResultConfig expResultConfig = new ExpResultConfig();
        expResultConfig.setCategoryId2Module(new HashMap<>());
        // Use reflection to the method
        Method method = DouHuService.class.getDeclaredMethod("getCompareSameShopPriceStyleAbConfig", String.class, Integer.class, boolean.class);
        method.setAccessible(true);
        ModuleAbConfig result = (ModuleAbConfig) method.invoke(douHuService, unionId, categoryId, isMt);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetCompareSameShopPriceStyleAbConfig_ModuleKeyNotInCategoryId2Module() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        Integer categoryId = 1;
        boolean isMt = true;
        ExpResultConfig expResultConfig = new ExpResultConfig();
        Map<String, String> categoryId2Module = new HashMap<>();
        categoryId2Module.put("otherKey", "module");
        expResultConfig.setCategoryId2Module(categoryId2Module);
        // Use reflection to invoke the private method
        Method method = DouHuService.class.getDeclaredMethod("getCompareSameShopPriceStyleAbConfig", String.class, Integer.class, boolean.class);
        method.setAccessible(true);
        ModuleAbConfig result = (ModuleAbConfig) method.invoke(douHuService, unionId, categoryId, isMt);
        // assert
        assertNull(result);
    }

    /**
     * 测试当moduleAbConfig为null时返回null
     */
    @Test
    public void testGetExpResultWhenModuleAbConfigIsNull() {
        // arrange
        ModuleAbConfig moduleAbConfig = null;
        // act
        String result = douHuService.getExpResult(moduleAbConfig);
        // assert
        assertNull(result);
    }

    /**
     * 测试当moduleAbConfig.configs为null时返回null
     */
    @Test
    public void testGetExpResultWhenConfigsIsNull() {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(null);
        // act
        String result = douHuService.getExpResult(moduleAbConfig);
        // assert
        assertNull(result);
    }

    /**
     * 测试当moduleAbConfig.configs为空列表时返回null
     */
    @Test
    public void testGetExpResultWhenConfigsIsEmpty() {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Collections.emptyList());
        // act
        String result = douHuService.getExpResult(moduleAbConfig);
        // assert
        assertNull(result);
    }

    /**
     * 测试当moduleAbConfig.configs第一个元素为null时返回null
     */
    @Test
    public void testGetExpResultWhenFirstConfigIsNull() {
        // arrange
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Arrays.asList(null, new AbConfig()));
        // act
        String result = douHuService.getExpResult(moduleAbConfig);
        // assert
        assertNull(result);
    }

    /**
     * 测试当moduleAbConfig.configs第一个元素expResult为null时返回null
     */
    @Test
    public void testGetExpResultWhenFirstConfigExpResultIsNull() {
        // arrange
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult(null);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        // act
        String result = douHuService.getExpResult(moduleAbConfig);
        // assert
        assertNull(result);
    }

    /**
     * 测试当moduleAbConfig.configs第一个元素expResult有值时返回正确结果
     */
    @Test
    public void testGetExpResultWhenFirstConfigExpResultHasValue() {
        // arrange
        String expectedResult = "test_result";
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult(expectedResult);
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        // act
        String result = douHuService.getExpResult(moduleAbConfig);
        // assert
        assertEquals(expectedResult, result);
    }

    /**
     * 测试当moduleAbConfig.configs有多个元素时返回第一个元素的expResult
     */
    @Test
    public void testGetExpResultWhenMultipleConfigsReturnFirstOne() {
        // arrange
        String expectedResult = "first_result";
        AbConfig abConfig1 = new AbConfig();
        abConfig1.setExpResult(expectedResult);
        AbConfig abConfig2 = new AbConfig();
        abConfig2.setExpResult("second_result");
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        moduleAbConfig.setConfigs(Arrays.asList(abConfig1, abConfig2));
        // act
        String result = douHuService.getExpResult(moduleAbConfig);
        // assert
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetNavbarSearchAbTestResult_NonMtClient() throws Throwable {
        // Arrange
        EnvCtx ctx = new EnvCtx();
        ctx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        // Act
        ModuleAbConfig result = douHuService.getNavbarSearchAbTestResult(ctx);
        // Assert
        assertNull(result);
        verify(douHuBiz, never()).getAbByUuId(any(), any(), anyBoolean());
    }

    @Test
    public void testGetNavbarSearchAbTestResult_MtClientWithNullResult() throws Throwable {
        // Arrange
        EnvCtx ctx = new EnvCtx();
        ctx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.setUuid("test-uuid-1");
        when(douHuBiz.getAbByUuId("test-uuid-1", "MtNavbarSearchModule", true)).thenReturn(null);
        // Act
        ModuleAbConfig result = douHuService.getNavbarSearchAbTestResult(ctx);
        // Assert
        assertNull(result);
        verify(douHuBiz).getAbByUuId("test-uuid-1", "MtNavbarSearchModule", true);
    }

    @Test
    public void testGetNavbarSearchAbTestResult_MtClientWithValidResult() throws Throwable {
        // Arrange
        EnvCtx ctx = new EnvCtx();
        ctx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.setUuid("test-uuid-2");
        ModuleAbConfig expected = new ModuleAbConfig();
        expected.setKey("test-config");
        when(douHuBiz.getAbByUuId("test-uuid-2", "MtNavbarSearchModule", true)).thenReturn(expected);
        // Act
        ModuleAbConfig result = douHuService.getNavbarSearchAbTestResult(ctx);
        // Assert
        assertNotNull(result);
        assertEquals(expected, result);
        verify(douHuBiz).getAbByUuId("test-uuid-2", "MtNavbarSearchModule", true);
    }

    @Test(expected = NullPointerException.class)
    public void testGetNavbarSearchAbTestResult_NullContext() throws Throwable {
        // Act & Assert
        douHuService.getNavbarSearchAbTestResult(null);
    }

    @Test
    public void testGetNavbarSearchAbTestResult_MtClientWithNullUuid() throws Throwable {
        // Arrange
        EnvCtx ctx = new EnvCtx();
        ctx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.setUuid(null);
        when(douHuBiz.getAbByUuId(null, "MtNavbarSearchModule", true)).thenReturn(null);
        // Act
        ModuleAbConfig result = douHuService.getNavbarSearchAbTestResult(ctx);
        // Assert
        assertNull(result);
        verify(douHuBiz).getAbByUuId(null, "MtNavbarSearchModule", true);
    }

    @Test(expected = RuntimeException.class)
    public void testGetNavbarSearchAbTestResult_WhenBizThrowsException() throws Throwable {
        // Arrange
        EnvCtx ctx = new EnvCtx();
        ctx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        ctx.setUuid("test-uuid-3");
        when(douHuBiz.getAbByUuId("test-uuid-3", "MtNavbarSearchModule", true)).thenThrow(new RuntimeException("Simulated exception"));
        // Act & Assert
        douHuService.getNavbarSearchAbTestResult(ctx);
    }
}
