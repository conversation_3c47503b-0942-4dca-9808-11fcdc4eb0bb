package com.dianping.mobile.mapi.dztgdetail.button.coupon;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.constants.PlusIcons;
import com.dianping.mobile.mapi.dztgdetail.common.enums.BannerTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBanner;
import com.dianping.mobile.mapi.dztgdetail.entity.CouponBannerInfo;
import com.dianping.mobile.mapi.dztgdetail.util.JsonLabelUtil;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Test;

@RunWith(MockitoJUnitRunner.class)
public class CouponBannerBuilderTest {

    @Mock
    private DealCtx context;

    private CouponBannerBuilder couponBannerBuilder;

    private Method buildQcpxCouponBannerMethod;

    @Before
    public void setUp() throws Exception {
        couponBannerBuilder = new CouponBannerBuilder();
        buildQcpxCouponBannerMethod = CouponBannerBuilder.class.getDeclaredMethod("buildQcpxCouponBanner", CouponBannerInfo.class);
        buildQcpxCouponBannerMethod.setAccessible(true);
    }

    /**
     * 测试当优惠券已使用且结束时间在24小时内的情况
     */
    @Test
    public void testBuildQcpxCouponBanner_UsedAndEndTimeWithin24Hours() throws Throwable {
        // arrange
        CouponBannerInfo couponBannerInfo = new CouponBannerInfo();
        couponBannerInfo.setAmount(new BigDecimal("100"));
        couponBannerInfo.setUsed(true);
        // 设置结束时间为当前时间后12小时
        couponBannerInfo.setEndTime(new Date(System.currentTimeMillis() + 12 * 60 * 60 * 1000));
        // act
        DealBuyBanner result = (DealBuyBanner) buildQcpxCouponBannerMethod.invoke(couponBannerBuilder, couponBannerInfo);
        // assert
        assertTrue(result.isShow());
        assertEquals(BannerTypeEnum.COUPON_INFO.getType(), result.getBannerType());
        // 验证content不为null
        assertNotNull(result.getContent());
        assertEquals(PlusIcons.COUPON_INFO, result.getIconUrl());
        //assertNotNull(result.getCountDownTs());
        //assertEquals(couponBannerInfo.getEndTime().getTime(), result.getCountDownTs().longValue());
    }

    /**
     * 测试当优惠券已使用且结束时间在1-7天内的情况
     */
    @Test
    public void testBuildQcpxCouponBanner_UsedAndEndTimeWithin7Days() throws Throwable {
        // arrange
        CouponBannerInfo couponBannerInfo = new CouponBannerInfo();
        couponBannerInfo.setAmount(new BigDecimal("50"));
        couponBannerInfo.setUsed(true);
        // 设置结束时间为当前时间后3天
        couponBannerInfo.setEndTime(new Date(System.currentTimeMillis() + 3 * 24 * 60 * 60 * 1000));
        // act
        DealBuyBanner result = (DealBuyBanner) buildQcpxCouponBannerMethod.invoke(couponBannerBuilder, couponBannerInfo);
        // assert
        assertTrue(result.isShow());
        assertEquals(BannerTypeEnum.COUPON_INFO.getType(), result.getBannerType());
        // 验证content不为null
        assertNotNull(result.getContent());
        assertEquals(PlusIcons.COUPON_INFO, result.getIconUrl());
        assertNull(result.getCountDownTs());
    }

    /**
     * 测试当优惠券已使用且结束时间超过7天的情况
     */
    @Test
    public void testBuildQcpxCouponBanner_UsedAndEndTimeAfter7Days() throws Throwable {
        // arrange
        CouponBannerInfo couponBannerInfo = new CouponBannerInfo();
        couponBannerInfo.setAmount(new BigDecimal("200"));
        couponBannerInfo.setUsed(true);
        // 设置结束时间为当前时间后10天
        couponBannerInfo.setEndTime(new Date(System.currentTimeMillis() + 10 * 24 * 60 * 60 * 1000));
        // act
        DealBuyBanner result = (DealBuyBanner) buildQcpxCouponBannerMethod.invoke(couponBannerBuilder, couponBannerInfo);
        // assert
        assertTrue(result.isShow());
        assertEquals(BannerTypeEnum.COUPON_INFO.getType(), result.getBannerType());
        // 验证content不为null
        assertNotNull(result.getContent());
        assertEquals(PlusIcons.COUPON_INFO, result.getIconUrl());
        assertNull(result.getCountDownTs());
    }

    /**
     * 测试当优惠券未使用的情况
     */
    @Test
    public void testBuildQcpxCouponBanner_NotUsed() throws Throwable {
        // arrange
        CouponBannerInfo couponBannerInfo = new CouponBannerInfo();
        couponBannerInfo.setAmount(new BigDecimal("100"));
        couponBannerInfo.setUsed(false);
        couponBannerInfo.setEndTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000));
        // act
        DealBuyBanner result = (DealBuyBanner) buildQcpxCouponBannerMethod.invoke(couponBannerBuilder, couponBannerInfo);
        // assert
        assertTrue(result.isShow());
        assertEquals(BannerTypeEnum.COUPON_INFO.getType(), result.getBannerType());
        // 验证content不为null
        assertNotNull(result.getContent());
        assertEquals(PlusIcons.COUPON_INFO, result.getIconUrl());
        assertNull(result.getCountDownTs());
    }

    /**
     * 测试当优惠券已使用但结束时间已过的情况
     */
    @Test
    public void testBuildQcpxCouponBanner_UsedButExpired() throws Throwable {
        // arrange
        CouponBannerInfo couponBannerInfo = new CouponBannerInfo();
        couponBannerInfo.setAmount(new BigDecimal("75"));
        couponBannerInfo.setUsed(true);
        // 设置结束时间为当前时间前1天
        couponBannerInfo.setEndTime(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000));
        // act
        DealBuyBanner result = (DealBuyBanner) buildQcpxCouponBannerMethod.invoke(couponBannerBuilder, couponBannerInfo);
        // assert
        assertTrue(result.isShow());
        assertEquals(BannerTypeEnum.COUPON_INFO.getType(), result.getBannerType());
        // 验证content不为null
        assertNotNull(result.getContent());
        assertEquals(PlusIcons.COUPON_INFO, result.getIconUrl());
        assertNull(result.getCountDownTs());
    }

    private boolean invokeMatchQcpx(List<PromoDTO> promoDTOS) throws Exception {
        Method method = CouponBannerBuilder.class.getDeclaredMethod("matchQcpx", List.class);
        method.setAccessible(true);
        return (boolean) method.invoke(null, promoDTOS);
    }

    @Test
    public void testMatchQcpxWithNullInput() throws Throwable {
        assertFalse(invokeMatchQcpx(null));
    }

    @Test
    public void testMatchQcpxWithEmptyList() throws Throwable {
        assertFalse(invokeMatchQcpx(Collections.emptyList()));
    }

    @Test
    public void testMatchQcpxWithNullMap() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO = mock(PromoDTO.class);
        when(promoDTO.getPromotionOtherInfoMap()).thenReturn(null);
        promoDTOS.add(promoDTO);
        assertFalse(invokeMatchQcpx(promoDTOS));
    }

    @Test
    public void testMatchQcpxWithEmptyMap() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO = mock(PromoDTO.class);
        when(promoDTO.getPromotionOtherInfoMap()).thenReturn(Collections.emptyMap());
        promoDTOS.add(promoDTO);
        assertFalse(invokeMatchQcpx(promoDTOS));
    }

    @Test
    public void testMatchQcpxWithMapNotContainingKey() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO = mock(PromoDTO.class);
        Map<String, String> map = new HashMap<>();
        map.put("OTHER_KEY", "some value");
        when(promoDTO.getPromotionOtherInfoMap()).thenReturn(map);
        promoDTOS.add(promoDTO);
        assertFalse(invokeMatchQcpx(promoDTOS));
    }

    @Test
    public void testMatchQcpxWithMapContainingKeyButNullValue() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO = mock(PromoDTO.class);
        Map<String, String> map = new HashMap<>();
        // 先放非空值
        map.put(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue(), "NOT_NULL");
        when(promoDTO.getPromotionOtherInfoMap()).thenReturn(map);
        promoDTOS.add(promoDTO);
        // 修改mock行为来模拟null值情况
        when(promoDTO.getPromotionOtherInfoMap().get(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue())).thenReturn(null);
        assertFalse(invokeMatchQcpx(promoDTOS));
    }

    @Test
    public void testMatchQcpxWithMapContainingKeyButEmptyValue() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO = mock(PromoDTO.class);
        Map<String, String> map = new HashMap<>();
        map.put(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue(), "");
        when(promoDTO.getPromotionOtherInfoMap()).thenReturn(map);
        promoDTOS.add(promoDTO);
        assertFalse(invokeMatchQcpx(promoDTOS));
    }

    @Test
    public void testMatchQcpxWithMapContainingKeyButBlankValue() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO = mock(PromoDTO.class);
        Map<String, String> map = new HashMap<>();
        map.put(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue(), "   ");
        when(promoDTO.getPromotionOtherInfoMap()).thenReturn(map);
        promoDTOS.add(promoDTO);
        assertFalse(invokeMatchQcpx(promoDTOS));
    }

    @Test
    public void testMatchQcpxWithMapContainingKeyButDifferentValue() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO = mock(PromoDTO.class);
        Map<String, String> map = new HashMap<>();
        map.put(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue(), "DIFFERENT_VALUE");
        when(promoDTO.getPromotionOtherInfoMap()).thenReturn(map);
        promoDTOS.add(promoDTO);
        assertFalse(invokeMatchQcpx(promoDTOS));
    }

    @Test
    public void testMatchQcpxWithMapContainingKeyAndMatchingValue() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO = mock(PromoDTO.class);
        Map<String, String> map = new HashMap<>();
        map.put(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue(), "DZ_QCPX_PLATFORM");
        when(promoDTO.getPromotionOtherInfoMap()).thenReturn(map);
        promoDTOS.add(promoDTO);
        assertTrue(invokeMatchQcpx(promoDTOS));
    }

    @Test
    public void testMatchQcpxWithMultiplePromosOnlyOneMatching() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO1 = mock(PromoDTO.class);
        Map<String, String> map1 = new HashMap<>();
        map1.put("OTHER_KEY", "value1");
        when(promoDTO1.getPromotionOtherInfoMap()).thenReturn(map1);
        promoDTOS.add(promoDTO1);
        PromoDTO promoDTO2 = mock(PromoDTO.class);
        Map<String, String> map2 = new HashMap<>();
        map2.put(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue(), "DZ_QCPX_PLATFORM");
        when(promoDTO2.getPromotionOtherInfoMap()).thenReturn(map2);
        promoDTOS.add(promoDTO2);
        assertTrue(invokeMatchQcpx(promoDTOS));
    }

    @Test
    public void testMatchQcpxWithMultiplePromosNoneMatching() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO1 = mock(PromoDTO.class);
        Map<String, String> map1 = new HashMap<>();
        map1.put(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue(), "VALUE1");
        when(promoDTO1.getPromotionOtherInfoMap()).thenReturn(map1);
        promoDTOS.add(promoDTO1);
        PromoDTO promoDTO2 = mock(PromoDTO.class);
        Map<String, String> map2 = new HashMap<>();
        map2.put(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue(), "VALUE2");
        promoDTOS.add(promoDTO2);
        assertFalse(invokeMatchQcpx(promoDTOS));
    }

    @Test
    public void testMatchQcpxWithMixedPromos() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO1 = mock(PromoDTO.class);
        when(promoDTO1.getPromotionOtherInfoMap()).thenReturn(null);
        promoDTOS.add(promoDTO1);
        PromoDTO promoDTO2 = mock(PromoDTO.class);
        when(promoDTO2.getPromotionOtherInfoMap()).thenReturn(Collections.emptyMap());
        promoDTOS.add(promoDTO2);
        PromoDTO promoDTO3 = mock(PromoDTO.class);
        Map<String, String> map3 = new HashMap<>();
        map3.put(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue(), "DZ_QCPX_PLATFORM");
        when(promoDTO3.getPromotionOtherInfoMap()).thenReturn(map3);
        promoDTOS.add(promoDTO3);
        assertTrue(invokeMatchQcpx(promoDTOS));
    }

    @Test
    public void testMatchQcpxWithMultiplePromosFirstMatching() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO1 = mock(PromoDTO.class);
        Map<String, String> map1 = new HashMap<>();
        map1.put(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue(), "DZ_QCPX_PLATFORM");
        when(promoDTO1.getPromotionOtherInfoMap()).thenReturn(map1);
        promoDTOS.add(promoDTO1);
        PromoDTO promoDTO2 = mock(PromoDTO.class);
        Map<String, String> map2 = new HashMap<>();
        map2.put("OTHER_KEY", "value2");
        promoDTOS.add(promoDTO2);
        assertTrue(invokeMatchQcpx(promoDTOS));
    }

    @Test
    public void testMatchQcpxWithMultiplePromosMiddleMatching() throws Throwable {
        List<PromoDTO> promoDTOS = new ArrayList<>();
        PromoDTO promoDTO1 = mock(PromoDTO.class);
        Map<String, String> map1 = new HashMap<>();
        map1.put("OTHER_KEY", "value1");
        when(promoDTO1.getPromotionOtherInfoMap()).thenReturn(map1);
        promoDTOS.add(promoDTO1);
        PromoDTO promoDTO2 = mock(PromoDTO.class);
        Map<String, String> map2 = new HashMap<>();
        map2.put(PromotionPropertyEnum.ISSUE_SOURCE_LIMIT.getValue(), "DZ_QCPX_PLATFORM");
        when(promoDTO2.getPromotionOtherInfoMap()).thenReturn(map2);
        promoDTOS.add(promoDTO2);
        PromoDTO promoDTO3 = mock(PromoDTO.class);
        Map<String, String> map3 = new HashMap<>();
        map3.put("OTHER_KEY", "value3");
        promoDTOS.add(promoDTO3);
        assertTrue(invokeMatchQcpx(promoDTOS));
    }
}
