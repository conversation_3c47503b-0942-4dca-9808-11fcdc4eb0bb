package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.lion.client.Lion;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.GlassProductTagRuleConfig;
import com.meituan.mdp.boot.starter.pigeon.util.MdpEnvUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.MedicalConstant.SAFE_PTOMETRY_TAGID_TEST;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * 测试 GlassDealUtils.hasAuthorizedShopTag 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class GlassDealUtilsTest {
    @InjectMocks
    private GlassDealUtils glassDealUtils;
    private DealCtx ctx;
    private Map<Long, List<DisplayTagDto>> dpShopId2TagsMap;
    private List<DisplayTagDto> displayTagDtos;
    private List<DealGroupTagDTO> tags;
    public static final Long GUNUINE_GUARANTEE_TAGID_TEST = 7406L;

    private MockedStatic<Lion> lionMockedStatic;
    private MockedStatic<MdpEnvUtils> mdpEnvUtilsMockedStatic;

    @Before
    public void setUp() {
        ctx = Mockito.mock(DealCtx.class);
        dpShopId2TagsMap = new HashMap<>();
        displayTagDtos = new ArrayList<>();
        tags = new ArrayList<>();
        lionMockedStatic = mockStatic(Lion.class);
        mdpEnvUtilsMockedStatic = mockStatic(MdpEnvUtils.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
        mdpEnvUtilsMockedStatic.close();
    }

    /**
     * 测试场景：有对应的店铺ID，标签列表不为空，有匹配的标签ID
     */
    @Test
    public void testIsSafePtometry_WithMatchingTagId() {
        DisplayTagDto tagDto = new DisplayTagDto();
        tagDto.setTagId(SAFE_PTOMETRY_TAGID_TEST);
        displayTagDtos.add(tagDto);
        dpShopId2TagsMap.put(888L, displayTagDtos);
        Mockito.when(ctx.getDpShopId2TagsMap()).thenReturn(dpShopId2TagsMap);
        Mockito.when(ctx.getDpLongShopId()).thenReturn(888L);
        mdpEnvUtilsMockedStatic.when(MdpEnvUtils::isTestEnv).thenReturn(true);
        boolean result = glassDealUtils.isSafePtometry(ctx);

        assertTrue( result);
    }

    /**
     * 测试场景：正常情况，有GUNUINE_GUARANTEE_TAGID标签，且有授权店铺标签
     */
    @Test
    public void testIsGenuineGuaranteeNormalCase() {
        DisplayTagDto displayTagDto = new DisplayTagDto();
        displayTagDto.setTagId(GUNUINE_GUARANTEE_TAGID_TEST);
        displayTagDtos.add(displayTagDto);
        dpShopId2TagsMap.put(1L, displayTagDtos);

        DealGroupTagDTO tag = new DealGroupTagDTO();
        tag.setId(1L);
        tags.add(tag);

        GlassProductTagRuleConfig glassProductTagRuleConfig = new GlassProductTagRuleConfig();
        glassProductTagRuleConfig.setShopTag(7406L);
        glassProductTagRuleConfig.setMissProductTag(-1);
        glassProductTagRuleConfig.setHitProductTag(1);
        LinkedList<Object> glassProductTagRuleConfigs = Lists.newLinkedList();
        glassProductTagRuleConfigs.add(glassProductTagRuleConfig);


        when(ctx.getDpShopId2TagsMap()).thenReturn(dpShopId2TagsMap);
        when(ctx.getDpLongShopId()).thenReturn(1L);
        when(ctx.getDealGroupDTO()).thenReturn(mock(DealGroupDTO.class));
        when(ctx.getDealGroupDTO().getTags()).thenReturn(tags);
        lionMockedStatic.when(() -> Lion.getList(Mockito.anyString(), Mockito.anyString(), Mockito.any()))
                .thenReturn(glassProductTagRuleConfigs);
        mdpEnvUtilsMockedStatic.when(MdpEnvUtils::isTestEnv).thenReturn(true);


        boolean result = glassDealUtils.isGenuineGuarantee(ctx);

        assertTrue(result);
    }
}
