package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.JoyDiscountCardProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.PoiShopCategoryProcessor;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiShopCategoryWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.google.common.collect.Sets;
import com.sankuai.athena.inf.cache2.CacheClient;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2024/7/3
 * @since mapi-dztgdetail-web
 */
@RunWith(MockitoJUnitRunner.class)
public class PoiShopCategoryProcessorTest {

    @InjectMocks
    PoiShopCategoryProcessor poiShopCategoryProcessor;

    @Mock
    private PoiShopCategoryWrapper poiShopCategoryWrapper;

    @Mock
    private JoyDiscountCardProcessor joyDiscountCardProcessor;

    @Mock
    private CacheClient cacheClient;

    @Test
    public void PoiShopCategoryProcessorTest() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDpShopId(123);
        when(poiShopCategoryWrapper.queryDpShopCategoryIds(Mockito.any())).thenReturn(Sets.newHashSet(1,2,3));
        poiShopCategoryProcessor.queryPoiBackCategoryIds(123);
        joyDiscountCardProcessor.prepare(ctx);
        poiShopCategoryProcessor.process(ctx);
        Assert.assertNotNull(ctx);
    }

}
