package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.factory.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.factory.GlassesStrategy;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.glasses.factory.impl.GlassesGetDurationStrategyImpl;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GlassesGetDurationStrategyImplTest {

    @InjectMocks
    private GlassesGetDurationStrategyImpl glassesGetDurationStrategy;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    /**
     * 测试正常情况，即所有的条件都满足，能够在DztgHighlightsModule对象的属性列表中添加一个新的CommonAttrVO对象
     */
    @Test
    public void testBuildMoudleNormal() throws Throwable {
        // arrange
        ctx = new DealCtx(new EnvCtx());
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("whether_pickup");
        attrDTO.setValue(Arrays.asList("是"));
        ctx.setDealGroupDTO(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setAttrs(new ArrayList<CommonAttrVO>());
        ctx.setHighlightsModule(dztgHighlightsModule);

        // act
        glassesGetDurationStrategy.buildMoudle(ctx);

        // assert
        // 在正常情况下，DztgHighlightsModule对象的属性列表中应该添加了一个新的CommonAttrVO对象
        assertEquals(1, ctx.getHighlightsModule().getAttrs().size());
    }
    //dealGroup为null的场景
    @Test
    public void testBuildMoudleDealGroupNull() throws Throwable {
        // arrange
        ctx = new DealCtx(new EnvCtx());
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("whether_pickup");
        attrDTO.setValue(Arrays.asList("是"));
        ctx.setDealGroupDTO(null);
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setAttrs(new ArrayList<CommonAttrVO>());
        ctx.setHighlightsModule(dztgHighlightsModule);

        // act
        glassesGetDurationStrategy.buildMoudle(ctx);

        // assert
        assertEquals(0,ctx.getHighlightsModule().getAttrs().size());
    }

    @Test
    public void testBuildMoudleAttrNull() throws Throwable {
        // arrange
        ctx = new DealCtx(new EnvCtx());
        AttrDTO attrDTO = new AttrDTO();
        ctx.setDealGroupDTO(null);
        DztgHighlightsModule dztgHighlightsModule = new DztgHighlightsModule();
        dztgHighlightsModule.setAttrs(new ArrayList<CommonAttrVO>());
        ctx.setHighlightsModule(dztgHighlightsModule);

        // act
        glassesGetDurationStrategy.buildMoudle(ctx);

        // assert
        assertEquals(0,ctx.getHighlightsModule().getAttrs().size());
    }
}