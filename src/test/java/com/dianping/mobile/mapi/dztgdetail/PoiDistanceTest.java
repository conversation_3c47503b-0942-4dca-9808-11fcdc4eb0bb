/*
package com.dianping.mobile.mapi.dztgdetail;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PoiClientWrapper;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

*/
/**
 * <AUTHOR>
 * @date 2021-06-30-10:51 下午
 *//*

public class PoiDistanceTest extends GenericTest {

    @Autowired
    private PoiClientWrapper wrapper;

    @Test
    public void testDistance(){
        boolean door2DoorPoi = wrapper.isDoor2DoorPoi(604202517);
        System.out.println(door2DoorPoi);
    }
}
*/
