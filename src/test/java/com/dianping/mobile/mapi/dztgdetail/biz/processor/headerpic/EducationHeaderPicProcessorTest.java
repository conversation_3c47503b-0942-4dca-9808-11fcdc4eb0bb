package com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic;

import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageScaleEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO;
import com.sankuai.general.product.query.center.client.dto.video.ExtendVideoDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class EducationHeaderPicProcessorTest {

    @InjectMocks
    private EducationHeaderPicProcessor educationHeaderPicProcessor;

    @Test
    public void testHeaderVideoIsNull() {

        DealGroupPBO dealGroupPBO = new DealGroupPBO();

        DealCtx ctx = new DealCtx(new EnvCtx());

        List<ContentPBO> result = new ArrayList<>();
        result.add(new ContentPBO(1, "http://test"));

        educationHeaderPicProcessor.fillPicScale(ctx,result, dealGroupPBO);
        assert result.get(0).getScale().equals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
    }

    @Test
    public void testDealGroupDTOIsNull() {

        DealGroupPBO dealGroupPBO = new DealGroupPBO();

        DealCtx ctx = new DealCtx(new EnvCtx());

        List<ContentPBO> result = new ArrayList<>();
        result.add(new ContentPBO(2, "http://test"));

        educationHeaderPicProcessor.fillPicScale(ctx,result, dealGroupPBO);
        assert result.get(0).getScale().equals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
    }

    @Test
    public void testHeaderVideoRatioExp() {

        DealGroupPBO dealGroupPBO = new DealGroupPBO();

        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDealGroupDTO(buildDealGroupDTO("1280-720"));

        List<ContentPBO> result = new ArrayList<>();
        result.add(new ContentPBO(2, "http://test"));

        educationHeaderPicProcessor.fillPicScale(ctx,result, dealGroupPBO);
        assert result.get(0).getScale().equals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
        assert result.get(0).getContent().equals("http://test");
    }

    @Test
    public void testHeaderVideoRatioSixteen_to_nine() {

        DealGroupPBO dealGroupPBO = new DealGroupPBO();

        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDealGroupDTO(buildDealGroupDTO("1280:720"));

        List<ContentPBO> result = new ArrayList<>();
        result.add(new ContentPBO(2, "http://test"));

        educationHeaderPicProcessor.fillPicScale(ctx,result, dealGroupPBO);
        assert result.get(0).getScale().equals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
        assert result.get(0).getContent().contains("1280:720");
    }

    @Test
    public void testHeaderVideoRatioThree_to_four() {

        DealGroupPBO dealGroupPBO = new DealGroupPBO();

        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setDealGroupDTO(buildDealGroupDTO("720:1280"));

        List<ContentPBO> result = new ArrayList<>();
        result.add(new ContentPBO(2, "http://test"));

        educationHeaderPicProcessor.fillPicScale(ctx,result, dealGroupPBO);
        assert result.get(0).getScale().equals(ImageScaleEnum.THREE_TO_FOUR.getScale());
        assert result.get(0).getContent().contains("720:1280");
    }



    private DealGroupDTO buildDealGroupDTO(String ratio){

        List<ExtendVideoDTO> extendVideoDTOs = new ArrayList<>();
        ExtendVideoDTO extendVideoDTO1 = new ExtendVideoDTO();
        extendVideoDTO1.setRatio("16:9");
        extendVideoDTO1.setCoverPath("http://host/16:9");
        extendVideoDTOs.add(extendVideoDTO1);

        ExtendVideoDTO extendVideoDTO2 = new ExtendVideoDTO();
        extendVideoDTO2.setRatio(ratio);
        extendVideoDTO2.setCoverPath("http://host/" + ratio);
        extendVideoDTOs.add(extendVideoDTO2);

        DealGroupImageDTO dealGroupImageDTO = new DealGroupImageDTO();
        dealGroupImageDTO.setExtendVideos(extendVideoDTOs);

        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setImage(dealGroupImageDTO);
        return dealGroupDTO;
    }


}
