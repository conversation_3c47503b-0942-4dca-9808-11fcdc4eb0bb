package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.publishcategory.DealGroupPublishCategoryQueryService;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.MoreDealsCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedMoreDealsReq;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedMoreDealsFacadeInitMoreDealsCtxTest {

    @InjectMocks
    private UnifiedMoreDealsFacade unifiedMoreDealsFacade;

    @Mock
    private DealIdMapperService dealIdMapperService;

    @Mock
    private DealGroupPublishCategoryQueryService dealGroupPublishCategoryQueryService;

    @Mock
    private IMobileContext iMobileContext;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    private UnifiedMoreDealsReq request;

    @Mock
    private EnvCtx envCtx;

    @Before
    public void setUp() {
        request = new UnifiedMoreDealsReq();
        when(iMobileContext.getHeader()).thenReturn(new MobileHeader());
    }

    /**
     * 测试走查询中心逻辑且成功执行
     */
    @Test
    public void testInitMoreDealsCtxQueryCenterSuccess() throws Throwable {
        // arrange
        request.setDealGroupId(123);
        // Fix: Set cityId to avoid NPE
        request.setCityId(789);
        MoreDealsCtx expectedCtx = new MoreDealsCtx(envCtx);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenReturn(new DealGroupDTO());
        // act
        Method initMoreDealsCtxMethod = UnifiedMoreDealsFacade.class.getDeclaredMethod("initMoreDealsCtx", UnifiedMoreDealsReq.class, EnvCtx.class, IMobileContext.class);
        initMoreDealsCtxMethod.setAccessible(true);
        MoreDealsCtx result = (MoreDealsCtx) initMoreDealsCtxMethod.invoke(unifiedMoreDealsFacade, request, envCtx, iMobileContext);
        // assert
        assertNotNull(result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * 测试走查询中心逻辑但抛出异常
     */
    @Test
    public void testInitMoreDealsCtxQueryCenterException() throws Throwable {
        // arrange
        request.setDealGroupId(123);
        // Fix: Set cityId to avoid NPE
        request.setCityId(789);
        when(queryCenterWrapper.getDealGroupDTO(any(QueryByDealGroupIdRequest.class))).thenThrow(new RuntimeException("Query center error"));
        // act
        Method initMoreDealsCtxMethod = UnifiedMoreDealsFacade.class.getDeclaredMethod("initMoreDealsCtx", UnifiedMoreDealsReq.class, EnvCtx.class, IMobileContext.class);
        initMoreDealsCtxMethod.setAccessible(true);
        MoreDealsCtx result = (MoreDealsCtx) initMoreDealsCtxMethod.invoke(unifiedMoreDealsFacade, request, envCtx, iMobileContext);
        // assert
        assertNotNull(result);
        verify(queryCenterWrapper).getDealGroupDTO(any(QueryByDealGroupIdRequest.class));
    }
}
