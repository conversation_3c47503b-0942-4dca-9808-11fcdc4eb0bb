package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.mpproduct.idservice.api.model.BizSkuIdDTO;
import com.sankuai.mpproduct.idservice.api.response.SkuIdConvertResponse;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class IdMappingWrapper_PtSkuIdToBizIdTest {

    @Mock
    private Future future;

    @Mock
    private SkuIdConvertResponse skuIdConvertResponse;

    private IdMappingWrapper idMappingWrapper = new IdMappingWrapper();

    @Test
    public void testPtSkuIdToBizIdFutureIsNull() throws Throwable {
        Map<Long, Long> result = idMappingWrapper.ptSkuIdToBizId(null);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testPtSkuIdToBizIdResponseIsNull() throws Throwable {
        when(future.get()).thenReturn(null);
        Map<Long, Long> result = idMappingWrapper.ptSkuIdToBizId(future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testPtSkuIdToBizIdResponseIsNotSuccess() throws Throwable {
        when(future.get()).thenReturn(skuIdConvertResponse);
        when(skuIdConvertResponse.isSuccess()).thenReturn(false);
        Map<Long, Long> result = idMappingWrapper.ptSkuIdToBizId(future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testPtSkuIdToBizIdSkuIdConvertResultIsNull() throws Throwable {
        when(future.get()).thenReturn(skuIdConvertResponse);
        when(skuIdConvertResponse.isSuccess()).thenReturn(true);
        when(skuIdConvertResponse.getSkuIdConvertResult()).thenReturn(null);
        Map<Long, Long> result = idMappingWrapper.ptSkuIdToBizId(future);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testPtSkuIdToBizIdNormal() throws Throwable {
        Map<Long, BizSkuIdDTO> skuIdConvertResult = new HashMap<>();
        skuIdConvertResult.put(1L, new BizSkuIdDTO(2L, 3));
        when(future.get()).thenReturn(skuIdConvertResponse);
        when(skuIdConvertResponse.isSuccess()).thenReturn(true);
        when(skuIdConvertResponse.getSkuIdConvertResult()).thenReturn(skuIdConvertResult);
        Map<Long, Long> result = idMappingWrapper.ptSkuIdToBizId(future);
        Map<Long, Long> expected = new HashMap<>();
        expected.put(1L, 2L);
        assertEquals(expected, result);
    }
}
