package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.BaseSearchOption;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.CountOption;
import com.dianping.general.unified.search.api.productshopsearch.dto.unit.CountUnit;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductBizTypeEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopCountFieldEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopCountTypeEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopGroupByFieldEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopSearchIdTypeEnum;
import com.dianping.general.unified.search.api.productshopsearch.request.GeneralProductShopCountRequest;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import org.mockito.InjectMocks;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupWrapper_GetCategoryIdTest {

    @Mock
    private Future future;

    private DealGroupWrapper dealGroupWrapper = new DealGroupWrapper();

    private static final String COUNT_KEY = "productShopQty";

    /**
     * 测试 future 为 null 的情况
     */
    @Test
    public void testGetCategoryIdFutureIsNull() throws Throwable {
        // arrange
        Future future = null;
        // act
        int result = dealGroupWrapper.getCategoryId(future);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 future 不为 null，但 getFutureResult(future) 返回 null 的情况
     */
    @Test
    public void testGetCategoryIdFutureResultIsNull() throws Throwable {
        // arrange
        when(future.get()).thenReturn(null);
        // act
        int result = dealGroupWrapper.getCategoryId(future);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 future 不为 null，getFutureResult(future) 返回的 DealGroupChannelDTO 对象的 categoryId 为 null 的情况
     */
    @Test
    public void testGetCategoryIdCategoryIdIsNull() throws Throwable {
        // arrange
        DealGroupChannelDTO dealGroupChannelDTO = new DealGroupChannelDTO();
        when(future.get()).thenReturn(dealGroupChannelDTO);
        // act
        int result = dealGroupWrapper.getCategoryId(future);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 future 不为 null，getFutureResult(future) 返回的 DealGroupChannelDTO 对象的 categoryId 不为 null 的情况
     */
    @Test
    public void testGetCategoryIdCategoryIdIsNotNull() throws Throwable {
        // arrange
        DealGroupChannelDTO dealGroupChannelDTO = new DealGroupChannelDTO();
        dealGroupChannelDTO.setCategoryId(1);
        when(future.get()).thenReturn(dealGroupChannelDTO);
        // act
        int result = dealGroupWrapper.getCategoryId(future);
        // assert
        assertEquals(1, result);
    }

    private GeneralProductShopCountRequest invokeGetProductShopQtyRequest(long dealGroupId, boolean mt) throws Exception {
        Method method = DealGroupWrapper.class.getDeclaredMethod("getProductShopQtyRequest", long.class, boolean.class);
        method.setAccessible(true);
        return (GeneralProductShopCountRequest) method.invoke(null, dealGroupId, mt);
    }

    @Test
    public void testGetProductShopQtyRequestWithDpPlatform() throws Throwable {
        // arrange
        long dealGroupId = 123456L;
        boolean mt = false;
        // act
        GeneralProductShopCountRequest request = invokeGetProductShopQtyRequest(dealGroupId, mt);
        // assert
        assertNotNull(request);
        assertEquals(ProductShopSearchIdTypeEnum.DP_BP, request.getIdPlatform());
        // Verify BaseSearchOption
        BaseSearchOption searchOption = request.getBaseSearchOption();
        assertNotNull(searchOption);
        assertEquals(1, searchOption.getProductBizTypes().size());
        assertEquals(ProductBizTypeEnum.DEALGROUP, searchOption.getProductBizTypes().get(0));
        assertEquals(1, searchOption.getProductIds().size());
        assertEquals(Long.valueOf(dealGroupId), searchOption.getProductIds().get(0));
        assertTrue(searchOption.getShopCanDisplay());
        // Verify CountOption
        CountOption countOption = request.getCountOption();
        assertNotNull(countOption);
        assertEquals(1, countOption.getCountUnits().size());
        // Verify CountUnit
        CountUnit countUnit = countOption.getCountUnits().get(0);
        assertNotNull(countUnit);
        assertEquals(COUNT_KEY, countUnit.getCountKey());
        assertEquals(ProductShopCountFieldEnum.SHOP_ID.getCode(), countUnit.getCountField());
        assertEquals(ProductShopCountTypeEnum.DISTINCT_COUNT, countUnit.getCountType());
        assertEquals(1, countUnit.getGroupByFields().size());
        assertTrue(countUnit.getGroupByFields().contains(ProductShopGroupByFieldEnum.PRODUCT_ID.getCode()));
        // Verify needEsRequest is not set (default false)
        assertFalse(request.isNeedEsRequest());
    }

    @Test
    public void testGetProductShopQtyRequestWithMtPlatform() throws Throwable {
        // arrange
        long dealGroupId = 789012L;
        boolean mt = true;
        // act
        GeneralProductShopCountRequest request = invokeGetProductShopQtyRequest(dealGroupId, mt);
        // assert
        assertNotNull(request);
        assertEquals(ProductShopSearchIdTypeEnum.MT_PP, request.getIdPlatform());
        // Verify BaseSearchOption
        BaseSearchOption searchOption = request.getBaseSearchOption();
        assertNotNull(searchOption);
        assertEquals(1, searchOption.getProductBizTypes().size());
        assertEquals(ProductBizTypeEnum.DEALGROUP, searchOption.getProductBizTypes().get(0));
        assertEquals(1, searchOption.getProductIds().size());
        assertEquals(Long.valueOf(dealGroupId), searchOption.getProductIds().get(0));
        assertTrue(searchOption.getShopCanDisplay());
        // Verify CountOption
        CountOption countOption = request.getCountOption();
        assertNotNull(countOption);
        assertEquals(1, countOption.getCountUnits().size());
        // Verify CountUnit
        CountUnit countUnit = countOption.getCountUnits().get(0);
        assertNotNull(countUnit);
        assertEquals(COUNT_KEY, countUnit.getCountKey());
        assertEquals(ProductShopCountFieldEnum.SHOP_ID.getCode(), countUnit.getCountField());
        assertEquals(ProductShopCountTypeEnum.DISTINCT_COUNT, countUnit.getCountType());
        assertEquals(1, countUnit.getGroupByFields().size());
        assertTrue(countUnit.getGroupByFields().contains(ProductShopGroupByFieldEnum.PRODUCT_ID.getCode()));
    }

    @Test
    public void testGetProductShopQtyRequestWithZeroDealGroupId() throws Throwable {
        // arrange
        long dealGroupId = 0L;
        boolean mt = false;
        // act
        GeneralProductShopCountRequest request = invokeGetProductShopQtyRequest(dealGroupId, mt);
        // assert
        assertNotNull(request);
        assertEquals(ProductShopSearchIdTypeEnum.DP_BP, request.getIdPlatform());
        // Verify BaseSearchOption
        BaseSearchOption searchOption = request.getBaseSearchOption();
        assertNotNull(searchOption);
        assertEquals(1, searchOption.getProductIds().size());
        assertEquals(Long.valueOf(0L), searchOption.getProductIds().get(0));
    }

    @Test
    public void testGetProductShopQtyRequestWithNegativeDealGroupId() throws Throwable {
        // arrange
        long dealGroupId = -123L;
        boolean mt = true;
        // act
        GeneralProductShopCountRequest request = invokeGetProductShopQtyRequest(dealGroupId, mt);
        // assert
        assertNotNull(request);
        assertEquals(ProductShopSearchIdTypeEnum.MT_PP, request.getIdPlatform());
        // Verify BaseSearchOption
        BaseSearchOption searchOption = request.getBaseSearchOption();
        assertNotNull(searchOption);
        assertEquals(1, searchOption.getProductIds().size());
        assertEquals(Long.valueOf(-123L), searchOption.getProductIds().get(0));
    }

    @Test
    public void testGetProductShopQtyRequestWithMaxLongDealGroupId() throws Throwable {
        // arrange
        long dealGroupId = Long.MAX_VALUE;
        boolean mt = false;
        // act
        GeneralProductShopCountRequest request = invokeGetProductShopQtyRequest(dealGroupId, mt);
        // assert
        assertNotNull(request);
        // Verify BaseSearchOption
        BaseSearchOption searchOption = request.getBaseSearchOption();
        assertNotNull(searchOption);
        assertEquals(1, searchOption.getProductIds().size());
        assertEquals(Long.valueOf(Long.MAX_VALUE), searchOption.getProductIds().get(0));
    }

    @Test
    public void testGetProductShopQtyRequestCountUnitStructure() throws Throwable {
        // arrange
        long dealGroupId = 123L;
        boolean mt = false;
        // act
        GeneralProductShopCountRequest request = invokeGetProductShopQtyRequest(dealGroupId, mt);
        // assert
        CountOption countOption = request.getCountOption();
        CountUnit countUnit = countOption.getCountUnits().get(0);
        // Verify CountUnit structure in detail
        assertEquals(COUNT_KEY, countUnit.getCountKey());
        assertEquals(ProductShopCountFieldEnum.SHOP_ID.getCode(), countUnit.getCountField());
        assertEquals(ProductShopCountTypeEnum.DISTINCT_COUNT, countUnit.getCountType());
        assertEquals(1, countUnit.getGroupByFields().size());
        assertTrue(countUnit.getGroupByFields().contains(ProductShopGroupByFieldEnum.PRODUCT_ID.getCode()));
    }
}
