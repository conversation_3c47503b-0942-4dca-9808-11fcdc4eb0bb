package com.dianping.mobile.mapi.dztgdetail.rcf.domian.service;

import com.alibaba.fastjson.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;

public class DzDealDetailServiceTest {

    @InjectMocks
    private DzDealBaseService dzDealBaseService = new DzDealBaseService();

    @Test
   public void testFlattenDetail() {
        JSONObject dealBase = new JSONObject();
        dealBase.put("title", "Hello世界Bonjour안녕하세요");
        dzDealBaseService.processDealTitle(dealBase);
        // 验证 module 中是否添加了 rcfSkuGroupsModule1Flatten
        Assert.assertTrue("Hello\u2006世界\u2006Bonjour안녕하세요".equals(dealBase.getString("title")));
    }
}