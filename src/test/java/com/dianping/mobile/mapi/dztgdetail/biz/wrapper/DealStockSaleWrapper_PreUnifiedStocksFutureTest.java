package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.sales.common.datatype.SalesDisplayRequest;
import com.dianping.deal.sales.display.api.service.ProductSceneSalesDisplayService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealStockSaleWrapper_PreUnifiedStocksFutureTest {

    @InjectMocks
    private DealStockSaleWrapper dealStockSaleWrapper;

    @Mock
    private ProductSceneSalesDisplayService productSceneSalesDisplayServiceFuture;

    private MockedStatic<FutureFactory> futureFactoryMockedStatic;

    @Before
    public void setUp() {
        futureFactoryMockedStatic = mockStatic(FutureFactory.class);
    }

    @After
    public void tearDown() {
        futureFactoryMockedStatic.close();
    }

    /**
     * 测试 multiRequest 为 null 的情况
     */
    @Test
    public void testPreUnifiedStocksFutureNullRequest() throws Throwable {
        // arrange
        SalesDisplayRequest multiRequest = null;
        // act
        Future result = dealStockSaleWrapper.preUnifiedStocksFuture(multiRequest);
        // assert
        assertNull(result);
    }

    /**
     * 测试 multiGetSales 方法执行正常的情况
     */
    @Test
    public void testPreUnifiedStocksFutureNormal() throws Throwable {
        // arrange
        SalesDisplayRequest multiRequest = new SalesDisplayRequest();
        when(productSceneSalesDisplayServiceFuture.multiGetSales(multiRequest)).thenReturn(null);
        futureFactoryMockedStatic.when(FutureFactory::getFuture).thenReturn(null);
        // act
        Future result = dealStockSaleWrapper.preUnifiedStocksFuture(multiRequest);
        // assert
        assertSame(result, FutureFactory.getFuture());
    }

    /**
     * 测试 multiGetSales 方法执行时抛出异常的情况
     */
    @Test
    public void testPreUnifiedStocksFutureException() throws Throwable {
        // arrange
        SalesDisplayRequest multiRequest = new SalesDisplayRequest();
        doThrow(new RuntimeException()).when(productSceneSalesDisplayServiceFuture).multiGetSales(multiRequest);
        // act
        Future result = dealStockSaleWrapper.preUnifiedStocksFuture(multiRequest);
        // assert
        assertNull(result);
    }
}
