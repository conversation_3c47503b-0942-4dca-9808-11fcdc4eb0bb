package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import static org.mockito.Mockito.when;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import java.util.HashMap;
import java.util.Map;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class ParallDealBuilderProcessor_GetPreviewInfoByRequestSourceTest {

    @InjectMocks
    private ParallDealBuilderProcessor parallDealBuilderProcessor;

    @Mock
    private LionConfigUtils lionConfigUtils;

    private Map<String, ModuleConfigDo> pagesourceMapKeyValueMap;

    @Before
    public void setUp() {
        pagesourceMapKeyValueMap = new HashMap<>();
        ModuleConfigDo moduleConfigDo = new ModuleConfigDo();
        moduleConfigDo.setValue("test_value");
        pagesourceMapKeyValueMap.put("test_source", moduleConfigDo);
    }

    /**
     * 测试 getPreviewInfoByRequestSource 方法，当 source 对应的 ModuleConfigDo 对象不存在时
     */
    @Test
    public void testGetPreviewInfoByRequestSource_ModuleConfigDoNotExists() {
        // arrange
        String source = "non_existent_source";
        // act
        String result = parallDealBuilderProcessor.getPreviewInfoByRequestSource(source);
        // assert
        assertEquals("create_order_preview", result);
    }
}
