package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReviewWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GoodReviewReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.AbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.GoodReviewPBO;
import com.dianping.reviewremote.remote.dto.ReviewStarDistributionDTO;
import com.dianping.ugc.review.remote.dto.ReviewCount;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class GoodReviewFacadeTest {

    @InjectMocks
    private GoodReviewFacade goodReviewFacade;

    @Mock
    private ReviewWrapper reviewWrapper;

    @Mock
    private DouHuBiz douHuBiz;

    private GoodReviewReq req;

    private EnvCtx envCtx;

    private int categoryId;

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Before
    public void setUp() {
        req = new GoodReviewReq();
        envCtx = new EnvCtx();
        envCtx.setUnionId("testUnionId");
        categoryId = 1;
    }

    private void mockDouHuBiz() {
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        List<AbConfig> configs = new ArrayList<>();
        AbConfig abConfig = new AbConfig();
        abConfig.setExpResult("testa");
        configs.add(abConfig);
        moduleAbConfig.setConfigs(configs);
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);
    }

    private void setUpCommon() {
        req = new GoodReviewReq();
        req.setDealgroupid(1);
        envCtx = new EnvCtx();
        envCtx.setUnionId("test");
        // Mocking DouHuBiz
        ModuleAbConfig moduleAbConfig = new ModuleAbConfig();
        AbConfig abConfig = new AbConfig();
        // Assuming "a" is a valid result that indicates a match
        abConfig.setExpResult("a");
        moduleAbConfig.setConfigs(Collections.singletonList(abConfig));
        when(douHuBiz.getAbByUnionId(anyString(), anyString(), anyBoolean())).thenReturn(moduleAbConfig);
    }

    @Test
    public void testQueryMtGoodReviewWhenMtReviewCountIsNull() throws Throwable {
        // arrange
        when(reviewWrapper.getReviewCount(req.getDealgroupid(), null)).thenReturn(null);
        // act
        GoodReviewPBO result = goodReviewFacade.queryMtGoodReview(req, envCtx, categoryId);
        // assert
        GoodReviewPBO expected = new GoodReviewPBO();
        assertEquals(expected.getGoodReviewRatio(), result.getGoodReviewRatio());
        assertEquals(expected.getTotalReviewDesc(), result.getTotalReviewDesc());
        assertEquals(expected.getReviewScore(), result.getReviewScore(), 0.01);
        assertEquals(expected.getUserIcons(), result.getUserIcons());
        assertEquals(expected.getReviewPhrase(), result.getReviewPhrase());
        assertEquals(expected.getRedirectUrl(), result.getRedirectUrl());
    }

    @Test
    public void testQueryMtGoodReviewWhenGoodReviewCountIsZero() throws Throwable {
        // arrange
        ReviewCount mtReviewCount = new ReviewCount();
        mtReviewCount.setAll(100);
        HashMap<Integer, Integer> stars = new HashMap<>();
        stars.put(50, 0);
        stars.put(40, 0);
        stars.put(45, 0);
        mtReviewCount.setStars(stars);
        when(reviewWrapper.getReviewCount(req.getDealgroupid(), null)).thenReturn(mtReviewCount);
        mockDouHuBiz();
        // act
        GoodReviewPBO result = goodReviewFacade.queryMtGoodReview(req, envCtx, categoryId);
        // assert
        assertEquals("0%", result.getGoodReviewRatio());
    }

    @Test
    public void testQueryMtGoodReviewWhenAvgRateIsZero() throws Throwable {
        // arrange
        ReviewCount mtReviewCount = new ReviewCount();
        mtReviewCount.setAll(100);
        HashMap<Integer, Integer> stars = new HashMap<>();
        stars.put(50, 50);
        stars.put(40, 0);
        stars.put(45, 0);
        mtReviewCount.setStars(stars);
        mtReviewCount.setAvgRate(0);
        when(reviewWrapper.getReviewCount(req.getDealgroupid(), null)).thenReturn(mtReviewCount);
        mockDouHuBiz();
        // act
        GoodReviewPBO result = goodReviewFacade.queryMtGoodReview(req, envCtx, categoryId);
        // assert
        assertEquals(0.0, result.getReviewScore(), 0.01);
    }

    @Test
    public void testQueryMtGoodReviewWhenAvgRateIsGreaterThanZero() throws Throwable {
        // arrange
        ReviewCount mtReviewCount = new ReviewCount();
        mtReviewCount.setAll(100);
        HashMap<Integer, Integer> stars = new HashMap<>();
        stars.put(50, 50);
        stars.put(40, 0);
        stars.put(45, 0);
        mtReviewCount.setStars(stars);
        mtReviewCount.setAvgRate(50);
        when(reviewWrapper.getReviewCount(req.getDealgroupid(), null)).thenReturn(mtReviewCount);
        mockDouHuBiz();
        // act
        GoodReviewPBO result = goodReviewFacade.queryMtGoodReview(req, envCtx, categoryId);
        // assert
        assertEquals(5.0, result.getReviewScore(), 0.01);
    }

    @Test
    public void testQueryMtGoodReviewCountIsNotZero() throws Throwable {
        // arrange
        ReviewCount mtReviewCount = new ReviewCount();
        mtReviewCount.setAll(100);
        HashMap<Integer, Integer> stars = new HashMap<>();
        stars.put(50, 50);
        stars.put(40, 0);
        stars.put(45, 0);
        mtReviewCount.setStars(stars);
        mtReviewCount.setAvgRate(50);
        when(reviewWrapper.getReviewCount(req.getDealgroupid(), null)).thenReturn(mtReviewCount);
        mockDouHuBiz();

        Future categoryFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupPublishCategoryById(anyInt())).thenReturn(categoryFuture);
        // Assuming categoryId is 1
        when(dealGroupWrapper.getFutureResult(categoryFuture)).thenReturn(1);
        // int categoryId = dealGroupWrapper.getFutureResult(categoryFuture);
        GoodReviewPBO result = goodReviewFacade.queryMtGoodReview(req, envCtx);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 reviewStar 为 null 的情况
     */
    @Test
    public void testQueryDpGoodReviewWhenReviewStarIsNull() throws Throwable {
        setUpCommon();
        // Mocking Future and DealGroupWrapper
        Future categoryFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupPublishCategoryById(anyInt())).thenReturn(categoryFuture);
        // Assuming categoryId is 1
        when(dealGroupWrapper.getFutureResult(categoryFuture)).thenReturn(1);
        Future reviewFuture = mock(Future.class);
        when(reviewWrapper.preReviewStarByReferIds(anyInt())).thenReturn(reviewFuture);
        when(reviewWrapper.getReviewStar(reviewFuture)).thenReturn(null);
        // act
        GoodReviewPBO result = goodReviewFacade.queryDpGoodReview(req, envCtx);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 reviewStar 不为 null 的情况
     */
    @Test
    public void testQueryDpGoodReviewWhenReviewStarIsNotNull() throws Throwable {
        setUpCommon();
        // Mocking Future and DealGroupWrapper
        Future categoryFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupPublishCategoryById(anyInt())).thenReturn(categoryFuture);
        // Assuming categoryId is 1
        when(dealGroupWrapper.getFutureResult(categoryFuture)).thenReturn(1);
        Future reviewFuture = mock(Future.class);
        when(reviewWrapper.preReviewStarByReferIds(anyInt())).thenReturn(reviewFuture);
        ReviewStarDistributionDTO reviewStar = new ReviewStarDistributionDTO(1L);
        reviewStar.setReviewCount(1);
        when(reviewWrapper.getReviewStar(reviewFuture)).thenReturn(reviewStar);
        // act
        GoodReviewPBO result = goodReviewFacade.queryDpGoodReview(req, envCtx);
        GoodReviewPBO result1 = goodReviewFacade.queryDpGoodReview(req, envCtx,1);
        // assert
        assertNotNull(result);
        assertNotNull(result1);
    }
}
