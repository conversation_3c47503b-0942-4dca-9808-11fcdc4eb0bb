package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.service.MtPoiService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.ArgumentMatchers.anyList;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class PoiClientWrapper_GetMtPoiDTOTest {

    @InjectMocks
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private MtPoiService sinaiMtPoiService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetMtPoiDTO_ExistMtPoiDTO() throws Throwable {
        long mtShopId = 1L;
        MtPoiDTO mtPoiDTO = new MtPoiDTO();
        Map<Long, MtPoiDTO> mtPoiDTOMap = new HashMap<>();
        mtPoiDTOMap.put(mtShopId, mtPoiDTO);
        try {
            when(sinaiMtPoiService.findPoisById(anyList(), anyList())).thenReturn(mtPoiDTOMap);
        } catch (Exception e) {
            // Handle the exception for the test scenario, typically by failing the test if this exception is unexpected
        }
        MtPoiDTO result = poiClientWrapper.getMtPoiDTO(mtShopId, Arrays.asList("field1", "field2"));
        assertEquals(mtPoiDTO, result);
    }

    @Test
    public void testGetMtPoiDTO_NotExistMtPoiDTO() throws Throwable {
        long mtShopId = 1L;
        Map<Long, MtPoiDTO> mtPoiDTOMap = new HashMap<>();
        try {
            when(sinaiMtPoiService.findPoisById(anyList(), anyList())).thenReturn(mtPoiDTOMap);
        } catch (Exception e) {
            // Handle the exception for the test scenario
        }
        MtPoiDTO result = poiClientWrapper.getMtPoiDTO(mtShopId, Arrays.asList("field1", "field2"));
        assertNull(result);
    }

    @Test
    public void testGetMtPoiDTO_EmptyMtPoiDTOMap() throws Throwable {
        long mtShopId = 1L;
        try {
            when(sinaiMtPoiService.findPoisById(anyList(), anyList())).thenReturn(new HashMap<>());
        } catch (Exception e) {
            // Handle the exception for the test scenario
        }
        MtPoiDTO result = poiClientWrapper.getMtPoiDTO(mtShopId, Arrays.asList("field1", "field2"));
        assertNull(result);
    }

    @Test
    public void testGetMtPoiDTO_Exception() throws Throwable {
        long mtShopId = 1L;
        try {
            when(sinaiMtPoiService.findPoisById(anyList(), anyList())).thenThrow(new RuntimeException());
        } catch (Exception e) {
            // Handle the exception for the test scenario
        }
        MtPoiDTO result = poiClientWrapper.getMtPoiDTO(mtShopId, Arrays.asList("field1", "field2"));
        assertNull(result);
    }
}
