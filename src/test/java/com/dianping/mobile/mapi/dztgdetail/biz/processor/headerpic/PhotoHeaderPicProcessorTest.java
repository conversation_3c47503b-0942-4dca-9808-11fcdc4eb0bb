package com.dianping.mobile.mapi.dztgdetail.biz.processor.headerpic;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageScaleEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.ExhibitContentDTO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class PhotoHeaderPicProcessorTest {

    private PhotoHeaderPicProcessor photoHeaderPicProcessor;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupPBO dealGroupPBO;

    @Mock
    private LionConfigUtils lionConfigUtils;

    @Mock
    private ExhibitContentDTO exhibitContentDTO;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        photoHeaderPicProcessor = new PhotoHeaderPicProcessor();
    }

    /**
     * Test case for empty result list.
     */
    @Test
    public void testFillPicScaleEmptyResultList() throws Throwable {
        // arrange
        List<ContentPBO> result = new ArrayList<>();
        // act
        photoHeaderPicProcessor.fillPicScale(ctx, result, dealGroupPBO);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case when ExhibitContentDTO is null.
     */
    @Test
    public void testMatchShowExhibit_ExhibitContentDTONull() throws Throwable {
        // arrange
        when(ctx.getExhibitContentDTO()).thenReturn(null);
        // act
        boolean result = photoHeaderPicProcessor.matchShowExhibit(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when ExhibitContentDTO is not null and recordCount is 0.
     */
    @Test
    public void testMatchShowExhibit_ExhibitContentDTONotNullRecordCountZero() throws Throwable {
        // arrange
        when(ctx.getExhibitContentDTO()).thenReturn(exhibitContentDTO);
        when(exhibitContentDTO.getRecordCount()).thenReturn(0);
        // act
        boolean result = photoHeaderPicProcessor.matchShowExhibit(ctx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when ExhibitContentDTO is not null and recordCount is greater than 0.
     */
    @Test
    public void testMatchShowExhibit_ExhibitContentDTONotNullRecordCountGreaterThanZero() throws Throwable {
        // arrange
        when(ctx.getExhibitContentDTO()).thenReturn(exhibitContentDTO);
        when(exhibitContentDTO.getRecordCount()).thenReturn(1);
        // act
        boolean result = photoHeaderPicProcessor.matchShowExhibit(ctx);
        // assert
        assertTrue(result);
    }
}
