package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.stock.DealStockQueryService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.concurrent.Future;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class DealStockWrapper_PreStock_1_Test {

    @InjectMocks
    private DealStockWrapper dealStockWrapper;

    @Mock
    private DealStockQueryService dealStockQueryServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试dpDealGroupId小于等于0的情况
     */
    @Test
    public void testPreStockDpDealGroupIdLessThanOrEqualToZero() {
        Future result = dealStockWrapper.preStock(0);
        assertNull(result);
    }

    /**
     * 测试dpDealGroupId大于0但getProductGroupStock方法抛出异常的情况
     */
    @Test
    public void testPreStockDpDealGroupIdGreaterThanZeroAndGetProductGroupStockThrowsException() throws Exception {
        when(dealStockQueryServiceFuture.getProductGroupStock(anyInt())).thenThrow(new RuntimeException());
        Future result = dealStockWrapper.preStock(1);
        verify(dealStockQueryServiceFuture, times(1)).getProductGroupStock(anyInt());
        assertNull(result);
    }
}
