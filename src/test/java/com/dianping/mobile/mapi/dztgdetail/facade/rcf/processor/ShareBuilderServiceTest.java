package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DztgShareModule;
import com.dianping.mobile.mapi.dztgdetail.entity.CustomShareAbleConfig;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.ShareBuilderService;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.APP_KEY;
import static org.mockito.Mockito.mockStatic;

/**
 * @Author: <EMAIL>
 * @Date: 2024/8/25
 */
@RunWith(MockitoJUnitRunner.class)
public class ShareBuilderServiceTest {
    @InjectMocks
    private ShareBuilderService shareBuilderService;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setup() {
        lionMockedStatic = mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
    }

    /**
     * 来源渠道匹配，且指定可分享渠道配置
     */
    @Test
    public void testPutShareAble_CostEffectiveShare() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        PriceContext priceContext = new PriceContext();
        priceContext.setHasExclusiveDeduction(true);
        DztgShareModule dztgShareModule = new DztgShareModule();
        ctx.setDztgShareModule(dztgShareModule);
        ctx.setPriceContext(priceContext);
        ctx.setRequestSource("cost_effective");

        CustomShareAbleConfig customConfig = new CustomShareAbleConfig();
        customConfig.setEnable(true);
        lionMockedStatic.when(() -> Lion.getBean(APP_KEY, LionConstants.CUSTOM_PAGESOURCE_SHARE_CONFIG, CustomShareAbleConfig.class)).thenReturn(customConfig);
        DealGroupPBO result = new DealGroupPBO();
        result.setShareAble(true);
        shareBuilderService.putShareAble(ctx, result);
        Assert.assertTrue(result.getShareAble());
    }

    /**
     * 有可分享渠道配置，但不匹配
     * 预期结果：不能分享
     */
    @Test
    public void testPutShareAble_NoMatchPagesource() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        PriceContext priceContext = new PriceContext();
        priceContext.setHasExclusiveDeduction(true);
        DztgShareModule dztgShareModule = new DztgShareModule();
        ctx.setDztgShareModule(dztgShareModule);
        ctx.setPriceContext(priceContext);
        ctx.setRequestSource("cost_effective");

        CustomShareAbleConfig customConfig = new CustomShareAbleConfig();
        customConfig.setEnable(true);
        lionMockedStatic.when(() -> Lion.getBean(APP_KEY, LionConstants.CUSTOM_PAGESOURCE_SHARE_CONFIG, CustomShareAbleConfig.class)).thenReturn(customConfig);
        DealGroupPBO result = new DealGroupPBO();
        result.setShareAble(true);
        shareBuilderService.putShareAble(ctx, result);
        Assert.assertTrue(result.getShareAble());
    }

    /**
     * 总开关打开，无可分享渠道配置
     * 预期结果：可以分享
     */
    @Test
    public void testPutShareAble_SwitchOpen() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        PriceContext priceContext = new PriceContext();
        priceContext.setHasExclusiveDeduction(true);
        DztgShareModule dztgShareModule = new DztgShareModule();
        ctx.setDztgShareModule(dztgShareModule);
        ctx.setPriceContext(priceContext);
        ctx.setRequestSource("cost_effective");

        CustomShareAbleConfig customConfig = new CustomShareAbleConfig();
        customConfig.setEnable(true);
        lionMockedStatic.when(() -> Lion.getBean(APP_KEY, LionConstants.CUSTOM_PAGESOURCE_SHARE_CONFIG, CustomShareAbleConfig.class)).thenReturn(customConfig);
        DealGroupPBO result = new DealGroupPBO();
        result.setShareAble(true);
        shareBuilderService.putShareAble(ctx, result);
        Assert.assertTrue(result.getShareAble());
    }

    /**
     * 总开关关闭
     * 预期结果：不可以分享
     */
    @Test
    public void testPutShareAble_SwitchClose() {
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        PriceContext priceContext = new PriceContext();
        priceContext.setHasExclusiveDeduction(true);
        DztgShareModule dztgShareModule = new DztgShareModule();
        ctx.setDztgShareModule(dztgShareModule);
        ctx.setPriceContext(priceContext);
        ctx.setRequestSource("cost_effective");

        CustomShareAbleConfig customConfig = new CustomShareAbleConfig();
        customConfig.setEnable(false);
        lionMockedStatic.when(() -> Lion.getBean(APP_KEY, LionConstants.CUSTOM_PAGESOURCE_SHARE_CONFIG, CustomShareAbleConfig.class)).thenReturn(customConfig);
        DealGroupPBO result = new DealGroupPBO();
        result.setShareAble(true);
        shareBuilderService.putShareAble(ctx, result);
        Assert.assertTrue(!result.getShareAble());
    }
}
