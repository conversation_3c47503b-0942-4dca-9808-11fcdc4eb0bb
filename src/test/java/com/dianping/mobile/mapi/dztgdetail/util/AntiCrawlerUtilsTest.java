package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.sankuai.hotel.login.authenticate.api.model.HttpResponse;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@Ignore
public class AntiCrawlerUtilsTest {

    @Mock
    private IMobileContext ctx;

    @Mock
    private HttpServletResponse response;

    @Mock
    private UserStatusResult userStatusResult;

    @Mock
    private ServletOutputStream servletOutputStream;

    @Mock
    private UserStatusResult userStatus;

    /**
     * 测试未授权登录的情况
     */
    @Test
    @Ignore
    public void testAntiUnauthenticLoginUnAuthorized() throws IOException {
        // arrange
        when(ctx.getResponse()).thenReturn(response);
        when(response.getOutputStream()).thenReturn(servletOutputStream);
        when(ctx.getUserStatus()).thenReturn(userStatusResult);
        when(userStatusResult.getMtUserId()).thenReturn(0L);
        when(userStatusResult.getUserId()).thenReturn(0L);
        when(ctx.getUserId()).thenReturn(0L);
        // act
        AntiCrawlerUtils.antiUnauthenticLogin(ctx);
        // assert
        verify(response).setStatus(HttpStatus.UNAUTHORIZED.value());
        verify(response).addHeader("M-LOGIN-FLAG", "1");
    }

    /**
     * 测试已授权登录的情况
     */
    @Test
    public void testAntiUnauthenticLoginAuthorized() throws IOException {
        // arrange
        when(ctx.getUserStatus()).thenReturn(userStatusResult);
        when(userStatusResult.getMtUserId()).thenReturn(1L);
        // act
        AntiCrawlerUtils.antiUnauthenticLogin(ctx);
        // assert
        verify(response, never()).setStatus(HttpStatus.UNAUTHORIZED.value());
        verify(response, never()).addHeader("M-LOGIN-FLAG", "1");
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testValidUnAuthorizationWhenLionGetBooleanReturnsFalse() {
        Mockito.when(ctx.getUserStatus()).thenReturn(userStatus);
        Mockito.when(userStatus.getMtUserId()).thenReturn(1L);
        boolean result = AntiCrawlerUtils.validUnAuthorization(ctx);
        assertFalse(result);
    }

    @Test
    public void testValidUnAuthorizationWhenLionGetBooleanReturnsTrueAndAnyUserIdGreaterThanZero() {
        Mockito.when(ctx.getUserStatus()).thenReturn(userStatus);
        Mockito.when(userStatus.getMtUserId()).thenReturn(1L);
        boolean result = AntiCrawlerUtils.validUnAuthorization(ctx);
        assertFalse(result);
    }

    // 其他测试用例代码...
    @Test
    @Ignore
    public void testValidUnAuthorizationWhenLionGetBooleanReturnsTrueAndAllUserIdsLessThanOrEqualToZero() {
        Mockito.when(ctx.getUserStatus()).thenReturn(userStatus);
        Mockito.when(userStatus.getMtUserId()).thenReturn(0L);
        boolean result = AntiCrawlerUtils.validUnAuthorization(ctx);
        assertTrue(result);
    }

    /**
     * 测试buildUnAuthorizedResponse方法，检查返回的HttpResponse对象的code和message属性是否正确
     */
    @Test
    public void testBuildUnAuthorizedResponse() {
        // arrange
        int expectedCode = HttpStatus.UNAUTHORIZED.value();
        String expectedMessage = HttpStatus.UNAUTHORIZED.getReasonPhrase();
        // act
        HttpResponse httpResponse = AntiCrawlerUtils.buildUnAuthorizedResponse();
        // assert
        assertEquals(expectedCode, httpResponse.getCode());
        assertEquals(expectedMessage, httpResponse.getMessage());
    }
}
