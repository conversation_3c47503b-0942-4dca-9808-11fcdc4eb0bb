package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.mobile.mapi.dztgdetail.entity.MtCommonParam;
import com.dianping.mobile.mapi.dztgdetail.entity.SortCxt;
import com.meituan.service.mobile.sinai.client.model.PoiModelL;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PoisRankHelper_SortPoiModelsTest {

    @Mock
    private IMobileContext mockMobileContext;

    @Mock
    private UserStatusResult mockUserStatusResult;

    private MtCommonParam createMockMtCommonParam() {
        // Mocking necessary methods to prevent NullPointerException
        when(mockMobileContext.getUserStatus()).thenReturn(mockUserStatusResult);
        // Assuming "12345" is a valid user ID for the test context
        when(mockUserStatusResult.getMtUserId()).thenReturn(12345L);
        // Assuming "1" is a valid city ID for the test context
        return new MtCommonParam(mockMobileContext);
    }

    @Test
    @Ignore
    public void testSortPoiModelsWithDistanceSortStrategyAndLatLng() throws Throwable {
        List<PoiModelL> poiModels = Arrays.asList(new PoiModelL(), new PoiModelL());
        SortCxt sortCxt = new SortCxt(createMockMtCommonParam());
        sortCxt.setSortStrategy(PoisRankHelper.SORT_TYPE_DISTANCE);
        sortCxt.setLat(30.0);
        sortCxt.setLng(120.0);
        PoisRankHelper.sortPoiModels(poiModels, sortCxt);
    }

    @Test
    @Ignore
    public void testSortPoiModelsWithNonDistanceSortStrategyOrNoLatLng() throws Throwable {
        List<PoiModelL> poiModels = Arrays.asList(new PoiModelL(), new PoiModelL());
        SortCxt sortCxt = new SortCxt(createMockMtCommonParam());
        sortCxt.setSortStrategy("other");
        PoisRankHelper.sortPoiModels(poiModels, sortCxt);
    }

    @Test
    public void testSortPoiModelsWithEmptyPoiModels() throws Throwable {
        List<PoiModelL> poiModels = Arrays.asList();
        SortCxt sortCxt = new SortCxt(createMockMtCommonParam());
        PoisRankHelper.sortPoiModels(poiModels, sortCxt);
        assertEquals(0, poiModels.size());
    }

    @Test
    public void testSortPoiModelsWithSingleElementPoiModels() throws Throwable {
        List<PoiModelL> poiModels = Arrays.asList(new PoiModelL());
        SortCxt sortCxt = new SortCxt(createMockMtCommonParam());
        PoisRankHelper.sortPoiModels(poiModels, sortCxt);
        assertEquals(1, poiModels.size());
    }
}
