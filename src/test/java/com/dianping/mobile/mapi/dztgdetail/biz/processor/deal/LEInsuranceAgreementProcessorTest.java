package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ShopTagWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class LEInsuranceAgreementProcessorTest {

    @Mock
    private ShopTagWrapper shopTagWrapper;

    @Mock
    private Future future;

    @InjectMocks
    private LEInsuranceAgreementProcessor processor;

    private DealCtx ctx;

    Map<Long, List<DisplayTagDto>> data = new HashMap<>();

    @Before
    public void setUp() {
        ctx = new DealCtx(new EnvCtx());
        ctx.setDpLongShopId(123L);
        FutureCtx futureCtx = new FutureCtx();
        futureCtx.setLeShopTagFuture(future);
        ctx.setFutureCtx(futureCtx);

        DisplayTagDto tagDto = new DisplayTagDto();
        tagDto.setTagId(21267L);

        data.put(123L, Collections.singletonList(tagDto));

    }
    /**
     * 测试场景：DealCtx为null
     */
    @Test
    public void testIsEnable_DealCtxIsNull() {

        // act
        boolean result = processor.isEnable(ctx);

        // assert
        assertFalse("DealCtx为null时应返回false", result);
    }

    /**
     * 测试场景：ServiceTypeId在目标列表中
     */
    @Test
    public void testIsEnable_ServiceTypeIdInTargetList() {
        // arrange
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setServiceTypeId(650L); // 在目标列表中的ID
        dealGroupDTO.setCategory(categoryDTO);
        ctx.setDealGroupDTO(dealGroupDTO);

//        when(Lion.getList(Environment.getAppName(), LionConstants.LE_INSURANCE_AGREEMENT_CATEGORY, Long.class, Arrays.asList(650L, 311L, 312L, 313L, 671L)))
//                .thenReturn(Arrays.asList(650L, 311L, 312L, 313L, 671L));

        try (MockedStatic<Lion> mockedLion = Mockito.mockStatic(Lion.class)) {
            mockedLion.when(() -> Lion.getList(
                            eq(Environment.getAppName()),
                            eq(LionConstants.LE_INSURANCE_AGREEMENT_CATEGORY),
                            eq(Long.class),
                            any(List.class)))
                    .thenReturn(Arrays.asList(650L, 311L, 312L, 313L, 671L));

            // act
            boolean result = processor.isEnable(ctx);

            // assert
            assertTrue("ServiceTypeId在目标列表中时应返回true", result);
        }

    }

    /**
     * 测试prepare方法，正常情况
     */
    @Test
    public void testPrepareNormal() {
        // arrange
        when(shopTagWrapper.preGetDpShopTags(123L)).thenReturn(future);

        // act
        processor.prepare(ctx);

        // assert
        verify(shopTagWrapper, times(1)).preGetDpShopTags(123L);
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testProcessNormalScenario() throws Throwable {
        // arrange
        when(shopTagWrapper.getShopId2TagsMap(future)).thenReturn(data);

        // act
        processor.process(ctx);

        // assert
        assertNotNull(ctx.getLEInsuranceAgreementEnum());
    }

    /**
     * 测试空响应场景
     */
    @Test
    public void testProcessNullResponseScenario() throws Throwable {
        // arrange
        when(shopTagWrapper.getShopId2TagsMap(future)).thenReturn(null);

        // act
        processor.process(ctx);

        // assert
        assertNull(ctx.getLEInsuranceAgreementEnum());
    }

    /**
     * 测试空标签列表场景
     */
    @Test
    public void testProcessEmptyTagsScenario() throws Throwable {
        // arrange
        Map<Long, List<DisplayTagDto>> data = Collections.emptyMap();
        when(shopTagWrapper.getShopId2TagsMap(future)).thenReturn(data);

        // act
        processor.process(ctx);

        // assert
        assertNull(ctx.getLEInsuranceAgreementEnum());
    }

    /**
     * 测试无匹配标签场景
     */
    @Test
    public void testProcessNoMatchingTagsScenario() throws Throwable {
        // arrange
        Map<Long, List<DisplayTagDto>> data = new HashMap<>();
        DisplayTagDto tagDto = new DisplayTagDto();
        tagDto.setTagId(999L); // 不存在的TagId
        data.put(123L, Collections.singletonList(tagDto));
        when(shopTagWrapper.getShopId2TagsMap(future)).thenReturn(data);

        // act
        processor.process(ctx);

        // assert
        assertNull(ctx.getLEInsuranceAgreementEnum());
    }



    /**
     * 测试无匹配标签场景
     */
    @Test
    public void testProcessNoMatchingTagsScenario7449L() throws Throwable {
        // arrange
        Map<Long, List<DisplayTagDto>> data = new HashMap<>();
        DisplayTagDto tagDto = new DisplayTagDto();
        tagDto.setTagId(999L); // 不存在的TagId
        data.put(7449L, Collections.singletonList(tagDto));
        when(shopTagWrapper.getShopId2TagsMap(future)).thenReturn(data);
        // act
        processor.process(ctx);
        // assert
        assertNull(ctx.getLEInsuranceAgreementEnum());
    }

    /**
     * 测试无匹配标签场景
     */
    @Test
    public void testProcessNoMatchingTagsScenario74149L() throws Throwable {
        // arrange
        Map<Long, List<DisplayTagDto>> data = new HashMap<>();
        DisplayTagDto tagDto = new DisplayTagDto();
        tagDto.setTagId(999L); // 不存在的TagId
        data.put(21345L, Collections.singletonList(tagDto));
        when(shopTagWrapper.getShopId2TagsMap(future)).thenReturn(data);
        // act
        processor.process(ctx);
        // assert
        assertNull(ctx.getLEInsuranceAgreementEnum());
    }
}
