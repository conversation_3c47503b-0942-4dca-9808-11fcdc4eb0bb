package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.base.DealReceiptQueryService;
import com.dianping.deal.base.dto.DealReceiptDTO;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.After;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Future;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import org.junit.Before;
import static org.mockito.Mockito.doNothing;
import java.util.ArrayList;

public class DealGroupWrapper_PreQueryDealGroupReceiptTest {

    @InjectMocks
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private DealReceiptQueryService dealReceiptQueryService;

    @Mock
    private Future mockFuture;

    public DealGroupWrapper_PreQueryDealGroupReceiptTest() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Tests preQueryDealGroupReceipt method under exception conditions.
     */
    @Test(expected = RuntimeException.class)
    public void testPreQueryDealGroupReceiptException() throws Throwable {
        // Arrange
        int dealGroupId = 1;
        Date bizDate = new Date();
        doThrow(RuntimeException.class).when(dealReceiptQueryService).queryDealGroupReceiptDTO(dealGroupId, bizDate);
        // Act
        dealGroupWrapper.preQueryDealGroupReceipt(dealGroupId, bizDate);
        // Assert
        // Expecting RuntimeException
    }
}
