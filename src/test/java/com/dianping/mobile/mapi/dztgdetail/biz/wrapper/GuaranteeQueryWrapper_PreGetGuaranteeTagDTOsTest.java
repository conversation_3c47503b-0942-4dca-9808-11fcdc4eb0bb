package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.common.response.Response;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.service.GuaranteeQueryService;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import org.apache.thrift.TException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;
import java.util.concurrent.Future;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GuaranteeQueryWrapper_PreGetGuaranteeTagDTOsTest {

    @InjectMocks
    private GuaranteeQueryWrapper guaranteeQueryWrapper;

    @Mock
    private GuaranteeQueryService guaranteeQueryService;

    @Mock
    private Future mockFuture;

    private BatchQueryGuaranteeTagRequest request;

    @Before
    public void setUp() {
        request = new BatchQueryGuaranteeTagRequest();
    }

    /**
     * Tests the behavior of preGetGuaranteeTagDTOs method under normal conditions.
     */
    @Test
    public void testPreGetGuaranteeTagDTOsNormal() throws Throwable {
        // Arrange
        Response mockResponse = new Response();
        when(guaranteeQueryService.batchQueryGuaranteeTag(any(SessionContextDTO.class), eq(request))).thenReturn(mockResponse);
        try (MockedStatic<ContextStore> mockedStatic = mockStatic(ContextStore.class)) {
            mockedStatic.when(ContextStore::getFuture).thenReturn(mockFuture);
            // Act
            Future result = guaranteeQueryWrapper.preGetGuaranteeTagDTOs(request);
            // Assert
            assertNotNull(result);
            verify(guaranteeQueryService, times(1)).batchQueryGuaranteeTag(any(SessionContextDTO.class), eq(request));
        }
    }

    /**
     * Tests the behavior of preGetGuaranteeTagDTOs method when an exception occurs.
     */
    @Test
    public void testPreGetGuaranteeTagDTOsException() throws Throwable {
        // Arrange
        when(guaranteeQueryService.batchQueryGuaranteeTag(any(SessionContextDTO.class), eq(request))).thenThrow(TException.class);
        // Act
        Future result = guaranteeQueryWrapper.preGetGuaranteeTagDTOs(request);
        // Assert
        assertNull(result);
        verify(guaranteeQueryService, times(1)).batchQueryGuaranteeTag(any(SessionContextDTO.class), eq(request));
    }
}
