package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.AtmosphereInfoDetailVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.InventoryDetail;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.ProductAtmosphereModuleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.ProductDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.StructuredDetail;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.BaseTradeButtonVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.BuyActionVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.StandardTradeBlockVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.StandardTradeBottomBarVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.bottombar.StandardTradeButtonVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.RichContentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.TextRichContentVO;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProductDetailBuilderServiceBuildTest {

    @Spy
    private ProductDetailBuilderService productDetailBuilderService;

    private DealCtx dealCtx;

    private GenericProductDetailPageResponse commonResponse;

    private Map<String, GenericModuleResponse> moduleResponse;

    @Before
    public void setUp() {
        dealCtx = mock(DealCtx.class);
        commonResponse = new GenericProductDetailPageResponse();
        moduleResponse = new HashMap<>();
    }

    /**
     * Test build method when commonModule has valid structDetail and dealInventory
     */
    @Test
    public void testBuild_WithValidCommonModule() throws Throwable {
        // arrange
        // Setup structured detail
        GenericModuleResponse structuredDetailResponse = new GenericModuleResponse();
        JSONObject structDetailVO = new JSONObject();
        List<StructuredDetail> structDetails = new ArrayList<>();
        StructuredDetail detail = new StructuredDetail();
        detail.setTitle("Test Title");
        structDetails.add(detail);
        structDetailVO.put("dealDetails", structDetails);
        structuredDetailResponse.setModuleVO(structDetailVO);
        moduleResponse.put("module_detail_structured_detail", structuredDetailResponse);
        // Setup inventory detail
        GenericModuleResponse inventoryResponse = new GenericModuleResponse();
        JSONObject inventoryVO = new JSONObject();
        List<InventoryDetail> inventoryDetails = new ArrayList<>();
        InventoryDetail invDetail = new InventoryDetail();
        invDetail.setTitle("Test Inventory");
        inventoryDetails.add(invDetail);
        inventoryVO.put("inventoryDetails", inventoryDetails);
        inventoryResponse.setModuleVO(inventoryVO);
        moduleResponse.put("module_detail_inventory_module", inventoryResponse);
        commonResponse.setModuleResponse(moduleResponse);
        when(dealCtx.getCommonModuleResponse()).thenReturn(commonResponse);
        when(dealCtx.getProductDetailTradeModuleResponse()).thenReturn(null);
        // act
        ProductDetailModule result = productDetailBuilderService.build(dealCtx);
        // assert
        assertNotNull("Result should not be null", result);
        assertNotNull("StructDetail should not be null", result.getStructDetail());
        assertNotNull("DealInventory should not be null", result.getDealInventory());
        assertFalse("StructDetail should not be empty", result.getStructDetail().isEmpty());
        assertFalse("DealInventory should not be empty", result.getDealInventory().isEmpty());
        verify(dealCtx).getCommonModuleResponse();
        verify(dealCtx).getProductDetailTradeModuleResponse();
    }

    /**
     * Test build method when commonModule is null
     */
    @Test
    public void testBuild_WithNullCommonModule() throws Throwable {
        // arrange
        when(dealCtx.getCommonModuleResponse()).thenReturn(null);
        when(dealCtx.getProductDetailTradeModuleResponse()).thenReturn(null);
        // act
        ProductDetailModule result = productDetailBuilderService.build(dealCtx);
        // assert
        assertNotNull("Result should not be null", result);
        assertNull("StructDetail should be null", result.getStructDetail());
        assertNull("DealInventory should be null", result.getDealInventory());
        verify(dealCtx).getCommonModuleResponse();
        verify(dealCtx).getProductDetailTradeModuleResponse();
    }

    /**
     * Test build method when commonModule has null moduleVO
     */
    @Test
    public void testBuild_WithNullModuleVO() throws Throwable {
        // arrange
        GenericModuleResponse structuredDetailResponse = new GenericModuleResponse();
        structuredDetailResponse.setModuleVO(null);
        moduleResponse.put("module_detail_structured_detail", structuredDetailResponse);
        GenericModuleResponse inventoryResponse = new GenericModuleResponse();
        inventoryResponse.setModuleVO(null);
        moduleResponse.put("module_detail_inventory_module", inventoryResponse);
        commonResponse.setModuleResponse(moduleResponse);
        when(dealCtx.getCommonModuleResponse()).thenReturn(commonResponse);
        when(dealCtx.getProductDetailTradeModuleResponse()).thenReturn(null);
        // act
        ProductDetailModule result = productDetailBuilderService.build(dealCtx);
        // assert
        assertNotNull("Result should not be null", result);
        assertNull("StructDetail should be null", result.getStructDetail());
        assertNull("DealInventory should be null", result.getDealInventory());
        verify(dealCtx).getCommonModuleResponse();
        verify(dealCtx).getProductDetailTradeModuleResponse();
    }

    /**
     * Test build method when commonModule has invalid JSON data
     */
    @Test
    public void testBuild_WithInvalidJsonData() throws Throwable {
        // arrange
        GenericModuleResponse structuredDetailResponse = new GenericModuleResponse();
        JSONObject invalidJson = new JSONObject();
        invalidJson.put("dealDetails", "invalid");
        structuredDetailResponse.setModuleVO(invalidJson);
        moduleResponse.put("module_detail_structured_detail", structuredDetailResponse);
        commonResponse.setModuleResponse(moduleResponse);
        when(dealCtx.getCommonModuleResponse()).thenReturn(commonResponse);
        when(dealCtx.getProductDetailTradeModuleResponse()).thenReturn(null);
        // act
        ProductDetailModule result = productDetailBuilderService.build(dealCtx);
        // assert
        assertNotNull("Result should not be null", result);
        assertNull("StructDetail should be null", result.getStructDetail());
        assertNull("DealInventory should be null", result.getDealInventory());
        verify(dealCtx).getCommonModuleResponse();
        verify(dealCtx).getProductDetailTradeModuleResponse();
    }

    private GenericModuleResponse createBottomBarWithValidButton() {
        GenericModuleResponse bottomBar = new GenericModuleResponse();
        JSONObject moduleVO = new JSONObject();
        // 创建StandardTradeBottomBarVO
        StandardTradeBottomBarVO standardTradeBottomBarVO = new StandardTradeBottomBarVO();
        StandardTradeBlockVO rightBottomBar = new StandardTradeBlockVO();
        // 创建按钮列表
        List<BaseTradeButtonVO> buttonList = new ArrayList<>();
        StandardTradeButtonVO buttonVO = new StandardTradeButtonVO();
        // 设置主标题
        List<RichContentVO> mainTitleList = new ArrayList<>();
        TextRichContentVO mainTitle = new TextRichContentVO();
        mainTitle.setText("购买按钮");
        mainTitleList.add(mainTitle);
        buttonVO.setMainTitle(mainTitleList);
        // 设置副标题
        List<RichContentVO> subTitleList = new ArrayList<>();
        TextRichContentVO subTitle = new TextRichContentVO();
        subTitle.setText("副标题");
        subTitleList.add(subTitle);
        buttonVO.setSubTitle(subTitleList);
        // 设置跳转链接
        BuyActionVO actionData = new BuyActionVO();
        actionData.setUrl("https://example.com/buy");
        buttonVO.setActionData(actionData);
        buttonList.add(buttonVO);
        rightBottomBar.setButtonList(buttonList);
        standardTradeBottomBarVO.setRightBottomBar(rightBottomBar);
        // 将对象转为JSON
        JSONObject bottomBarJson = new JSONObject();
        bottomBarJson.put("bottomBar", JSON.parseObject(JSON.toJSONString(standardTradeBottomBarVO)));
        moduleVO.put("bottomBar", bottomBarJson.getJSONObject("bottomBar"));
        bottomBar.setModuleVO(moduleVO);
        return bottomBar;
    }

    @Test
    public void testBuildTradeModule_ShouldSetHideMemberCardGuideTrue_WhenAtmosphereShowNewAtmosphereBar() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        // 初始设置为false
        ctx.setHideMemberCardGuide(false);
        GenericProductDetailPageResponse response = new GenericProductDetailPageResponse();
        Map<String, GenericModuleResponse> moduleResponseMap = new HashMap<>();
        // 创建atmospherePriceBar模块
        GenericModuleResponse atmospherePriceBar = new GenericModuleResponse();
        JSONObject moduleVO = new JSONObject();
        // 使用mock创建ProductAtmosphereModuleVO和AtmosphereInfoDetailVO
        ProductAtmosphereModuleVO atmosphereModuleVO = mock(ProductAtmosphereModuleVO.class);
        AtmosphereInfoDetailVO atmosphere = mock(AtmosphereInfoDetailVO.class);
        when(atmosphere.isShowNewAtmosphereBar()).thenReturn(true);
        when(atmosphereModuleVO.getAtmosphere()).thenReturn(atmosphere);
        // 将mock对象转为JSONObject
        moduleVO = JSON.parseObject(JSON.toJSONString(atmosphereModuleVO));
        atmospherePriceBar.setModuleVO(moduleVO);
        moduleResponseMap.put("module_detail_deal_atmosphere_price_sale_bar", atmospherePriceBar);
        response.setModuleResponse(moduleResponseMap);
        ctx.setProductDetailTradeModuleResponse(response);
        // 创建被测试类实例
        ProductDetailBuilderService productDetailBuilderService = new ProductDetailBuilderService();
        // act
        ProductDetailModule result = productDetailBuilderService.buildTradeModule(ctx);
        // assert
        assertTrue(ctx.isHideMemberCardGuide());
        assertNotNull(result);
    }

    @Test
    public void testBuildTradeModule_ShouldProcessMonthlySubscription_WhenIsMonthlySubscription() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        // 设置结果对象
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        DealBuyBar buyBar = new DealBuyBar(0, new ArrayList<>());
        dealGroupPBO.setBuyBar(buyBar);
        ctx.setResult(dealGroupPBO);
        // 设置模块响应
        GenericProductDetailPageResponse response = new GenericProductDetailPageResponse();
        Map<String, GenericModuleResponse> moduleResponseMap = new HashMap<>();
        // 创建bottomBar模块
        GenericModuleResponse bottomBar = createBottomBarWithValidButton();
        moduleResponseMap.put("module_detail_deal_bottom_bar", bottomBar);
        response.setModuleResponse(moduleResponseMap);
        ctx.setProductDetailTradeModuleResponse(response);
        // 创建一个子类来覆盖isMonthlySubscription的行为
        ProductDetailBuilderService testService = new ProductDetailBuilderService() {

            @Override
            public ProductDetailModule buildTradeModule(DealCtx ctx) {
                try {
                    GenericProductDetailPageResponse response = ctx.getProductDetailTradeModuleResponse();
                    if (Objects.isNull(response) || MapUtils.isEmpty(response.getModuleResponse())) {
                        return null;
                    }
                    ProductDetailModule productDetailModule = new ProductDetailModule();
                    Map<String, GenericModuleResponse> moduleResponse = response.getModuleResponse();
                    // 跳过前面的代码，直接测试我们关心的部分
                    // 底部banner
                    GenericModuleResponse bottomBar = moduleResponse.get("module_detail_deal_bottom_bar");
                    if (Objects.nonNull(bottomBar) && Objects.nonNull(bottomBar.getModuleVO())) {
                        // 直接执行我们要测试的代码块
                        List<DealBuyBtn> dealBuyBtns = processMonthlySubscriptionBottomBarModule(bottomBar);
                        if (CollectionUtils.isNotEmpty(dealBuyBtns)) {
                            ctx.getResult().getBuyBar().setBuyBtns(dealBuyBtns);
                        }
                        return productDetailModule;
                    }
                    return productDetailModule;
                } catch (Exception e) {
                    return null;
                }
            }
        };
        // act
        ProductDetailModule result = testService.buildTradeModule(ctx);
        // assert
        assertNotNull(result);
        assertNotNull(ctx.getResult().getBuyBar().getBuyBtns());
        assertFalse(ctx.getResult().getBuyBar().getBuyBtns().isEmpty());
        assertEquals("购买按钮", ctx.getResult().getBuyBar().getBuyBtns().get(0).getBtnTitle());
    }
}
