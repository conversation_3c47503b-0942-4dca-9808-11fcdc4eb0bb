package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DouHuBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzDealThemeWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.util.DealMetaVersionUtils;
import com.google.common.collect.Lists;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.PHOTO_NEW_DEAL_DETAIL_CATEGORY;
import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.PHOTO_NEW_DEAL_DETAIL_DOUHU_SWITCH;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * @author: xiongyonghong
 * @create: 2024-09-24
 * @description:
 **/
@RunWith(MockitoJUnitRunner.class)
public class PhotoHighlightsProcessorTest {

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupCategoryDTO category;

    @InjectMocks
    private PhotoHighlightsProcessor photoHighlightsProcessor;

    @Mock
    private DouHuService douHuService;

    @Mock
    private DouHuBiz douHuBiz;

    @Mock
    private DzDealThemeWrapper dzDealThemeWrapper;

    private MockedStatic<DealMetaVersionUtils> dealMetaVersionUtilsMockedStatic;

    private EnvCtx envCtx;

    private DealGroupChannelDTO groupChannelDTO;

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        envCtx = new EnvCtx();
        envCtx.setAppId(1);
        lionMockedStatic = mockStatic(Lion.class);
        dealMetaVersionUtilsMockedStatic=mockStatic(DealMetaVersionUtils.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
        dealMetaVersionUtilsMockedStatic.close();
    }

    @Test
    public void testEthnicAndTravelPhotoAttrs() throws Throwable {
//        when(douHuService.getPhotoAbResult(any())).thenReturn("b");
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        DealGroupChannelDTO goupchannel = mock(DealGroupChannelDTO.class);
        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO attr = mock(AttrDTO.class);
        when(attr.getName()).thenReturn("shootpersoncount");
        when(attr.getValue()).thenReturn(Arrays.asList("3"));
        attrs.add(attr);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setChannelDTO(goupchannel);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        when(category.getCategoryId()).thenReturn(910L);
        when(category.getServiceType()).thenReturn("景点跟拍");
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");
        when(goupchannel.getCategoryId()).thenReturn(910);
        lionMockedStatic.when(() -> Lion.getBoolean(Environment.getAppName(), PHOTO_NEW_DEAL_DETAIL_DOUHU_SWITCH, true))
                .thenReturn(false);
        lionMockedStatic.when(() -> Lion.getList(Environment.getAppName(), PHOTO_NEW_DEAL_DETAIL_CATEGORY, String.class))
                .thenReturn(Lists.newArrayList("910:景点跟拍"));
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);
        assertNotNull(result);
    }

    /**
     * 测试getHighlightsAttrs方法，当服务类型为空时的场景
     */
    @Test
    public void testGetHighlightsAttrsForNullServiceType() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getServiceType()).thenReturn(null);

        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);

        // assert
        assertNull(result);
    }
    /**
     * 测试getHighlightsAttrs方法，当DealGroupDTO为null时的场景
     */
    @Test
    public void testGetHighlightsAttrsForNullDealGroupDTO() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.getDealGroupDTO()).thenReturn(null);

        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);

        // assert
        assertNull(result);
    }
    @Test
    public void testGetHighlightsAttrsForGroupPhotoOld() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(ctx.getCategoryId()).thenReturn(504);
        when(category.getCategoryId()).thenReturn(504L);

        when(category.getServiceType()).thenReturn("男士写真");
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");

        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试getHighlightsAttrs方法，当服务类型为"个人写真"且处于太极团单时的场景
     */
    @Test
    public void testGetHighlightsAttrsInTaiJi() throws Throwable {
        // arrange
        when(douHuService.getPhotoAbResult(any())).thenReturn("b");
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(buildCategory(504L,"个人写真"));
        when(ctx.getCategoryId()).thenReturn(504);
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试getHighlightsAttrs方法，当团单类别为504且不是太极团单时
     */
    @Test
    public void testGetHighlightsAttrsForCategory504NotTaiJi() throws Throwable {
        // arrange
        when(douHuService.getPhotoAbResult(any())).thenReturn("b");
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(buildCategory(504L, "个人写真"));
        // 模拟更多的必要条件和返回值
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);
        // assert
        assertNotNull(result);
        // 断言更多的结果条件
    }
    @Test
    public void testGetHighlightsAttrsIdIdentificationAdults() throws Throwable {
        // arrange
        when(douHuService.getPhotoAbResult(any())).thenReturn("b");
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(buildCategory(2001L, "成人证件照"));
        when(ctx.getCategoryId()).thenReturn(2001);
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetHighlightsAttrsIdIdentificationWedding() throws Throwable {
        // arrange
        when(douHuService.getPhotoAbResult(any())).thenReturn("b");
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(buildCategory(2001L, "结婚登记照"));
        when(ctx.getCategoryId()).thenReturn(2001);
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetHighlightsAttrsIdIdentification() throws Throwable {
        // arrange
        when(douHuService.getPhotoAbResult(any())).thenReturn("b");
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(buildCategory(2001L, "身份证照"));
        when(ctx.getCategoryId()).thenReturn(2001);
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetHighlightsAttrsPersonalPregnant() throws Throwable {
        // arrange
        when(douHuService.getPhotoAbResult(any())).thenReturn("b");
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(buildCategory(504L, "孕妇写真"));
        when(ctx.getCategoryId()).thenReturn(504);
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetHighlightsAttrsFamily() throws Throwable {
        // arrange
        when(douHuService.getPhotoAbResult(any())).thenReturn("b");
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(buildCategory(504L, "全家福/亲子照"));
        when(ctx.getCategoryId()).thenReturn(504);
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
    }

    /**
     * Test for categoryId=2003 and serviceType in SNAPSHOT_PERSONAL_PHOTO
     */

    @Test
    public void testGetHighlightsAttrs_ForPersonalPhoto() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        List<AttrDTO> attrs = Lists.newArrayList();
        DealGroupChannelDTO groupchannel = mock(DealGroupChannelDTO.class);
        ctx.setChannelDTO(groupchannel);
        when(groupchannel.getCategoryId()).thenReturn(2003);
        AttrDTO attr = mock(AttrDTO.class);
        when(attr.getName()).thenReturn("provideRefinementService");
//        when(attr.getValue()).thenReturn(Arrays.asList("是"));
        AttrDTO attr2 = mock(AttrDTO.class);
        when(attr2.getName()).thenReturn("intensiveRepairNum");
        when(attr2.getValue()).thenReturn(Arrays.asList("5"));
        attrs.add(attr);
        attrs.add(attr2);
        ctx.setDealGroupDTO(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        when(category.getCategoryId()).thenReturn(2003L);
        when(category.getServiceType()).thenReturn("职业形象照");
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);
        // assert
        assertNotNull(result);
    }


    @Test
    public void testGetHighlightsAttrs_WhenCategoryIs2003AndServiceTypeInMoka() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(2003L);
        when(category.getServiceType()).thenReturn("模卡照");
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");
        when(ctx.getCategoryId()).thenReturn(2003);
        // Mock attributes for image photo
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getName()).thenReturn("photoPlateAttachCount");
        when(attrDTO.getValue()).thenReturn(Arrays.asList("5"));
        AttrDTO attrDTO2 = mock(AttrDTO.class);
        when(attrDTO2.getName()).thenReturn("tag_unifyProduct");
        when(attrDTO2.getValue()).thenReturn(Arrays.asList("1"));
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO,attrDTO2));
        lionMockedStatic.when(() -> Lion.getBoolean(Environment.getAppName(), PHOTO_NEW_DEAL_DETAIL_DOUHU_SWITCH, true))
                .thenReturn(false);
        lionMockedStatic.when(() -> Lion.getList(Environment.getAppName(), PHOTO_NEW_DEAL_DETAIL_CATEGORY, String.class))
                .thenReturn(Lists.newArrayList("2003:模卡照"));
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // Verify the attributes returned for image photo
        boolean hasRefinementAttr = result.stream().anyMatch(attr -> "底片赠送".equals(attr.getName()));
        assertTrue(hasRefinementAttr);
    }
    /**
     * Test when category is null
     */
    @Test
    public void testGetHighlightsAttrs_WhenCategoryIsNull() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        ctx.setDealGroupDTO(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Test for group photo category (2005)
     */
    @Test
    public void testGetHighlightsAttrs_ForGroupPhoto() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        DealGroupChannelDTO groupchannel = mock(DealGroupChannelDTO.class);
        when(groupchannel.getCategoryId()).thenReturn(2005);
        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO attr = mock(AttrDTO.class);
        when(attr.getName()).thenReturn("applytarget");
        when(attr.getValue()).thenReturn(Arrays.asList("团体"));
        attrs.add(attr);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setChannelDTO(groupchannel);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        when(category.getCategoryId()).thenReturn(2005L);
        when(category.getServiceType()).thenReturn("团体照");
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);
        // assert
        assertNotNull(result);
    }

    /**
     * Test for experience photo category (921)
     */
    @Test
    public void testGetHighlightsAttrs_ForExperiencePhoto() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        DealGroupChannelDTO goupchannel = mock(DealGroupChannelDTO.class);
        when(goupchannel.getCategoryId()).thenReturn(921);
        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO attr = mock(AttrDTO.class);
        when(attr.getName()).thenReturn("availablePeopleNum");
        when(attr.getValue()).thenReturn(Arrays.asList("2"));
        attrs.add(attr);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setChannelDTO(goupchannel);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        when(category.getCategoryId()).thenReturn(921L);
        when(category.getServiceType()).thenReturn("自拍体验");
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetHighlightsAttrs_ForExperienceChange() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        DealGroupChannelDTO goupchannel = mock(DealGroupChannelDTO.class);
        when(goupchannel.getCategoryId()).thenReturn(921);
        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO attr = mock(AttrDTO.class);
        when(attr.getName()).thenReturn("availablePeopleNum");
        when(attr.getValue()).thenReturn(Arrays.asList("2"));
        attrs.add(attr);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setChannelDTO(goupchannel);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        when(category.getCategoryId()).thenReturn(921L);
        when(category.getServiceType()).thenReturn("换装体验");
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);
        // assert
        assertNotNull(result);
    }

    /**
     * Test for ID photo category (2001)
     */
    @Test
    public void testGetHighlightsAttrs_ForIdPhoto() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        DealGroupChannelDTO goupchannel = mock(DealGroupChannelDTO.class);
        when(goupchannel.getCategoryId()).thenReturn(2001);
        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO attr = mock(AttrDTO.class);
        when(attr.getName()).thenReturn("whetherToProvideClothing");
        attrs.add(attr);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setChannelDTO(goupchannel);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        when(category.getCategoryId()).thenReturn(2001L);
        when(category.getServiceType()).thenReturn("证件照");
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);
        // assert
        assertNotNull(result);
    }

    /**
     * Test for pregnant/baby photo category (1004)
     */
    @Test
    public void testGetHighlightsAttrs_ForPregnantBabyPhoto() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        DealGroupChannelDTO goupchannel = mock(DealGroupChannelDTO.class);
        when(goupchannel.getCategoryId()).thenReturn(1004);
        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO attr = mock(AttrDTO.class);
        when(attr.getName()).thenReturn("intensiveRepairNum");
        when(attr.getValue()).thenReturn(Arrays.asList("10"));
        attrs.add(attr);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setChannelDTO(goupchannel);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        when(category.getCategoryId()).thenReturn(1004L);
        when(category.getServiceType()).thenReturn("孕妇摄影");
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetHighlightsAttrs_ForComprehensiveFollow() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        DealGroupChannelDTO goupchannel = mock(DealGroupChannelDTO.class);
        when(goupchannel.getCategoryId()).thenReturn(910);
        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO attr = mock(AttrDTO.class);
        when(attr.getName()).thenReturn("IsTheFilmPresentedFree");
        when(attr.getValue()).thenReturn(Arrays.asList("底片部分赠送"));
        AttrDTO attr2 = mock(AttrDTO.class);
        when(attr2.getName()).thenReturn("photoPlateAttachCount");
        when(attr2.getValue()).thenReturn(Arrays.asList("3"));
        attrs.add(attr);
        attrs.add(attr2);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setChannelDTO(goupchannel);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        when(category.getCategoryId()).thenReturn(910L);
        when(category.getServiceType()).thenReturn("毕业跟拍");
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");
        lionMockedStatic.when(() -> Lion.getBoolean(Environment.getAppName(), PHOTO_NEW_DEAL_DETAIL_DOUHU_SWITCH, true))
                .thenReturn(false);
        lionMockedStatic.when(() -> Lion.getList(Environment.getAppName(), PHOTO_NEW_DEAL_DETAIL_CATEGORY, String.class))
                .thenReturn(Lists.newArrayList("910:毕业跟拍"));
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetHighlightsAttrs_ForActivityFollow() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(envCtx);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        DealGroupChannelDTO goupchannel = mock(DealGroupChannelDTO.class);
        when(goupchannel.getCategoryId()).thenReturn(910);
        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO attr = mock(AttrDTO.class);
        when(attr.getName()).thenReturn("IsTheFilmPresentedFree");
        when(attr.getValue()).thenReturn(Arrays.asList("同底原片"));
        AttrDTO attr2 = mock(AttrDTO.class);
        when(attr2.getName()).thenReturn("intensiveRepairNum");
        when(attr2.getValue()).thenReturn(Arrays.asList("3"));
        attrs.add(attr);
        attrs.add(attr2);
        ctx.setDealGroupDTO(dealGroupDTO);
        ctx.setChannelDTO(goupchannel);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        when(category.getCategoryId()).thenReturn(910L);
        when(category.getServiceType()).thenReturn("活动跟拍");
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");
        lionMockedStatic.when(() -> Lion.getBoolean(Environment.getAppName(), PHOTO_NEW_DEAL_DETAIL_DOUHU_SWITCH, true))
                .thenReturn(false);
        lionMockedStatic.when(() -> Lion.getList(Environment.getAppName(), PHOTO_NEW_DEAL_DETAIL_CATEGORY, String.class))
                .thenReturn(Lists.newArrayList("910:活动跟拍"));
        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetHighlightsAttrsForWeddingPhoto() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(buildCategory(901L, "婚纱摄影"));

        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO attr = mock(AttrDTO.class);
        when(attr.getName()).thenReturn("intensiveRepairNum");
        when(attr.getValue()).thenReturn(Arrays.asList("3"));
        attrs.add(attr);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");

        mockFutureCtxAndDealProduct(901L,false);

        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetHighlightsAttrsForTourWeddingPhoto() throws Throwable {
        // arrange
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(buildCategory(916L, "旅游婚纱照"));

        List<AttrDTO> attrs = Lists.newArrayList();
        AttrDTO attr = mock(AttrDTO.class);
        when(attr.getName()).thenReturn("des_overseas");
        when(attr.getValue()).thenReturn(Arrays.asList("[\"普吉岛\",\"巴厘岛\",\"马尔代夫\"]"));
        attrs.add(attr);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        when(douHuService.getPhotoAbResult(ctx)).thenReturn("b");

        mockFutureCtxAndDealProduct(916L,false);


        // act
        List<CommonAttrVO> result = photoHighlightsProcessor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
        assertEquals("普吉岛、巴厘岛等",result.get(0).getValue());
    }

    @Test
    public void testPrepare() throws Exception {
        // arrange
        DealCtx ctx = new DealCtx(envCtx);
        FutureCtx futureCtx = mock(FutureCtx.class);
        ctx.setFutureCtx(futureCtx);
        ctx.setMtId(123);
        ctx.setDpId(456);
        ctx.setMtLongShopId(789L);
        ctx.setDpLongShopId(101112L);
        ctx.setEnvCtx(envCtx);
        when(dzDealThemeWrapper.preQueryDealProduct(any())).thenReturn(mock(Future.class));
        when(douHuBiz.getAbExpResult(eq(ctx),anyString())).thenReturn(null);

        // act
        photoHighlightsProcessor.prepare(ctx);

        // assert
        verify(dzDealThemeWrapper, times(1)).preQueryDealProduct(any());
        verify(futureCtx, times(1)).setDealThemeFuture(any());
    }


    private DealGroupCategoryDTO buildCategory(Long categoryId, String serviceType)  {
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(categoryId);
        category.setServiceType(serviceType);
        category.setServiceTypeId(122003L);
        return category;
    }

    private void mockFutureCtxAndDealProduct(Long categoryId, boolean isOldMetaVersion) {
        FutureCtx futureCtx = mock(FutureCtx.class);
        Future future = mock(Future.class);
        DealProductResult dealProductResult = mock(DealProductResult.class);
        DealProductDTO dealProductDTO = mock(DealProductDTO.class);

        when(ctx.getFutureCtx()).thenReturn(futureCtx);
        when(futureCtx.getDealThemeFuture()).thenReturn(future);
        when(dzDealThemeWrapper.getFutureResult(future)).thenReturn(dealProductResult);
        when(dealProductResult.getDeals()).thenReturn(Lists.newArrayList(dealProductDTO));
        dealMetaVersionUtilsMockedStatic.when(() -> DealMetaVersionUtils.isOldMetaVersion(dealProductDTO, categoryId)).thenReturn(isOldMetaVersion);
    }
}
