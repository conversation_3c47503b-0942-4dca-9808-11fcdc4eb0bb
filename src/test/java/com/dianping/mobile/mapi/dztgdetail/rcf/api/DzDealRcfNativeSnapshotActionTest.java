package com.dianping.mobile.mapi.dztgdetail.rcf.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.bff.cache.enums.RcfDealBffInterfaceEnum;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.mapi.dztgdetail.common.constants.Resps;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.req.DealNativeSnapshotReq;
import com.dianping.mobile.mapi.dztgdetail.rcf.api.vo.DealRcfNativeSnapshot;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.DealNativeSnapshotMainProcessor;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.dto.DealBffResponseDTO;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.DealHeadPicHeightCalculateHandler;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.TransparentContainerHeightCalculateHandler;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

/**
 * DzDealRcfNativeSnapshotAction测试类
 */
public class DzDealRcfNativeSnapshotActionTest {

    @Mock
    private IMobileContext context;

    private DzDealRcfNativeSnapshotAction action;

    @InjectMocks
    private DzDealRcfNativeSnapshotAction dzDealRcfNativeSnapshotAction;

    @Mock
    private DealNativeSnapshotMainProcessor dealNativeSnapshotMainProcessor;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        action = new DzDealRcfNativeSnapshotAction();
    }

    /**
     * 测试validate方法，当request为null时
     */
    @Test
    public void testValidateRequestIsNull() {
        // arrange
        DealNativeSnapshotReq request = null;
        // act
        IMobileResponse response = action.validate(request, context);
        // assert
        assertEquals("验证request为null时返回参数错误响应", Resps.PARAM_ERROR, response);
    }

    /**
     * 测试validate方法，当request不为null时
     */
    @Test
    public void testValidateRequestIsNotNull() {
        // arrange
        DealNativeSnapshotReq request = mock(DealNativeSnapshotReq.class);
        // act
        IMobileResponse response = action.validate(request, context);
        // assert
        assertEquals("验证request不为null时返回null", null, response);
    }

    /**
     * 测试 execute 方法，当 context 为 null 时
     */
    @Test
    public void testExecuteWhenContextIsNull() {
        // arrange
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        when(dealNativeSnapshotMainProcessor.process(any(DealNativeSnapshotReq.class), any(EnvCtx.class))).thenReturn(DealNativeSnapshotMainProcessor.Response.succeed(new DealRcfNativeSnapshot()));
        // act
        IMobileResponse response = dzDealRcfNativeSnapshotAction.execute(request, null);
        // assert
        assert response instanceof CommonMobileResponse;
    }

    /**
     * 测试 execute 方法，正常情况
     */
    @Test
    public void testExecuteNormal() {
        // arrange
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        when(context.getRequest()).thenReturn(mock(HttpServletRequest.class));
        when(context.getHeader()).thenReturn(mock(MobileHeader.class));
        when(dealNativeSnapshotMainProcessor.process(any(DealNativeSnapshotReq.class), any(EnvCtx.class))).thenReturn(DealNativeSnapshotMainProcessor.Response.succeed(new DealRcfNativeSnapshot()));
        // act
        IMobileResponse response = dzDealRcfNativeSnapshotAction.execute(request, context);
        // assert
//        verify(dealNativeSnapshotMainProcessor, times(1)).process(any(DealNativeSnapshotReq.class), any(EnvCtx.class));
        assert response instanceof CommonMobileResponse;
    }

    /**
     * 测试 execute 方法，处理器抛出异常情况
     */
    @Test(expected = Exception.class)
    public void testExecuteWithProcessorException() {
        // arrange
        DealNativeSnapshotReq request = new DealNativeSnapshotReq();
        when(context.getRequest()).thenReturn(mock(HttpServletRequest.class));
        when(dealNativeSnapshotMainProcessor.process(any(DealNativeSnapshotReq.class), any(EnvCtx.class))).thenThrow(new RuntimeException("Mock exception"));
        // act
        dzDealRcfNativeSnapshotAction.execute(request, context);
        // assert is handled by expected exception
    }
    
    @Test
    public void testExecute() {
        String requestStr = "{\"dealGroupId\":*********,\"poiId\":403561,\"userLng\":0.0,\"userLat\":0.0,\"appVersion\":\"1200270204\",\"mrnVersion\":\"0.6.1864\",\"deviceHeight\":844.0,\"deviceWidth\":390.0}";
        String responseStr = "{\"bffResponse\":{\"dzgoodreview\":{\"userIcons\":[\"https://p0.meituan.net/travelcube/34c81dbcfc46b64d8148edfe8d7a8d5e7896.png\"],\"redirectUrl\":\"imeituan://www.meituan.com/reviewlist?refertype=1&referid=*********\",\"reviewScore\":5,\"goodReviewRatio\":\"100%\",\"totalReviewDesc\":\"共1个消费评价\"},\"dzdealbase\":{\"hasReserveEntrance\":false,\"serviceType\":\"美甲\",\"pnPurchaseNoteDTO\":{\"subTitle\":\"周一至周日可用\",\"pnTitle\":\"购买须知\",\"pnModules\":[{\"pnIcon\":\"https://p0.meituan.net/travelcube/eb358d1a211285207c636aa95594f3751524.png\",\"pnModuleName\":\"适用时间\",\"pnItems\":[{\"pnItemValues\":[{\"pnValue\":\"购买后90天内有效\",\"pnType\":1}],\"pnItemName\":\"有效时间\"}]},{\"pnIcon\":\"https://p0.meituan.net/travelcube/17303e587e8242902a1bf6ecd3eab10b1029.png\",\"pnModuleName\":\"预约规则\",\"pnItems\":[{\"pnItemValues\":[{\"pnValue\":\"无需预约，如遇消费高峰时段您可能需要排队\",\"pnType\":1}],\"pnItemName\":\"\"}]},{\"pnIcon\":\"https://p0.meituan.net/travelcube/1f18aa2c0100e75060daf348283f3c861351.png\",\"pnModuleName\":\"适用人数\",\"pnItems\":[{\"pnItemValues\":[{\"pnValue\":\"每张团购券不限使用人数\",\"pnType\":1}],\"pnItemName\":\"\"}]},{\"pnIcon\":\"https://p1.meituan.net/travelcube/4dac95c5f43a52f49b81ece1918925ea858.png\",\"pnModuleName\":\"其他规则\",\"pnItems\":[{\"pnItemValues\":[{\"pnValue\":\"不再与其他优惠同享\",\"pnType\":1}],\"pnItemName\":\"\"}]},{\"pnIcon\":\"https://p1.meituan.net/travelcube/09a2f606b1636f8b135b8582e3c0a53d1494.png\",\"pnModuleName\":\"温馨提示\",\"pnItems\":[{\"pnItemValues\":[{\"pnValue\":\"如需团购券发票，请您在消费时向商户咨询\",\"pnType\":1},{\"pnValue\":\"为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\",\"pnType\":1}],\"pnItemName\":\"\"}]}]},\"generalFeaturesLayer\":{\"layerConfigs\":[{\"textType\":1,\"textStyle\":\"#FF4B10\",\"title\":\"每人不限购买次数，不限会员使用\",\"type\":101,\"desc\":\"单用户可享受团购价重复购买不限次数，如线下实际情况与描述不符，请您在消费时先与商户确认，同时可以向平台反馈情况。\"}]},\"type\":0,\"mtId\":*********,\"features\":[\"随时退\",\"过期退\"],\"specialFeatures\":[],\"titleTagIcon\":\"\",\"userCardState\":4,\"isStandardDealGroup\":false,\"adModule\":{\"mtDealGroupId\":*********,\"dealID\":0,\"shopId\":403561},\"skuId\":\"451181617\",\"featuresLayer\":{\"layerConfigs\":[{\"icon\":\"https://p0.meituan.net/ingee/2860c04f209c5ebe48ad7e05a726de711937.png\",\"textType\":0,\"title\":\"随时退·过期退\",\"type\":1,\"jumpUrl\":\"https://shangou.meituan.net/v1/mss_24c1e05b968a4937bf34e2f4ff68639e/shangou-fe-maker-html/sg/html/1611211020310_d5e044/index.html\",\"desc\":\"未消费随时退款、过期未消费自动退款。\"}]},\"meetPurchaseLimit\":false,\"dpDealId\":451181617,\"saleDesc\":\"年售24\",\"subTitleList\":[],\"displayPriceDesc\":\"团购价\",\"skuModule\":{\"skuAttrCnNameList\":[],\"skuAttrValueList\":[],\"url\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=dealsubmitorderpage-popup&mrn_min_version=0.0.540&dealid=*********&shopid=403561&skuid=&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&shopidEncrypt=h1yj495877772b2b4770171d8c0ced56c0004db0d81e754d8cb285099565ab8bc7025ad1d1ae290a4386bbd9c7h7fh&pagesource=dealGroupDetail&is_sku=0\"},\"reminderExtend\":[],\"ssrExperimentEnabled\":false,\"businessFigure\":\"\",\"moduleConfigsModule\":{\"isDzx\":true,\"isDpOrder\":true,\"generalInfo\":\"card_style_v2\",\"isTort\":false,\"key\":\"default\",\"extraInfo\":\"newtuandeal\"},\"responseTimestamp\":1732874237853,\"structedDetails\":[{\"name\":\"<div>\\n        <div class=\\\"detail-tit\\\"></div>\\n        <div>\\n            <table width=\\\"100%\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" class=\\\"detail-table\\\">\\n                <thead>\\n                <tr>\\n                    <th width=\\\"50%\\\">名称</th>\\n                    <th width=\\\"25%\\\">数量</th>\\n                    <th width=\\\"25%\\\">单价</th>\\n                </tr>\\n                </thead>\\n                <tbody>\\n                            <tr>\\n                                <td>本次到店卸甲/卸睫</td>\\n                                    <td class=\\\"tc\\\">7</td>\\n                                    <td class=\\\"tc\\\">111元</td>\\n                            </tr>\\n                    <tr class=\\\"total\\\">\\n                        <td></td>\\n                        <td class=\\\"tc\\\">总价<br><strong>团购价</strong></td>\\n                        <td class=\\\"tc\\\">777元<br><strong>666元</strong>\\n                        </td>\\n                    </tr>\\n                </tbody>\\n            </table>\\n        </div>\\n\\n\\n</div>\",\"id\":\"套餐\",\"iD\":\"套餐\",\"type\":1,\"isUsed\":false,\"key\":\"套餐\"},{\"subTitle\":\"周一至周日可用\",\"name\":\"<div>\\n<div class=\\\"detail-box\\\">\\n    <div class=\\\"purchase-notes\\\">\\n\\t\\t        <dl>\\n            <dt>有效期</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">\\n    购买后90天内有效\\n        </p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>预约信息</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">无需预约，如遇消费高峰时段您可能需要排队</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>适用人数</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">每张团购券不限使用人数</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>规则提醒</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">不再与其他优惠同享\\n</p>\\n            </dd>\\n        </dl>\\n        <dl>\\n            <dt>温馨提示</dt>\\n            <dd>\\n                <p class=\\\"listitem\\\">如需团购券发票，请您在消费时向商户咨询</p>\\n                <p class=\\\"listitem\\\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\\n            </dd>\\n        </dl>\\n    </div>\\n</div>\\n\\n</div>\",\"id\":\"购买须知\",\"iD\":\"购买须知\",\"type\":2,\"isUsed\":false,\"key\":\"购买须知\"}],\"promoDetailModule\":{\"showPriceCompareEntrance\":false,\"marketPrice\":\"777\",\"dealGifts\":[{\"couponNum\":1,\"activityId\":119217,\"thumbnail\":\"http://p0.meituan.net/bizoperate/defb40aa44789acb139ee22cb0831965400867.jpg\",\"productTag\":\"下单后发放\",\"customerActivityPrefix\":\"下单赠\",\"title\":\"无门槛10元券\",\"specificTag\":\"赠品\"}],\"bestPromoDetails\":[{\"promoName\":\"团购优惠111元\",\"promoAmount\":\"111\",\"promoDesc\":\"下单立省111元\",\"iconUrl\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoTag\":\"团购优惠\"},{\"promoName\":\"满100减67\",\"promoAmount\":\"67\",\"promoDesc\":\"商家优惠券\",\"iconUrl\":\"https://p0.meituan.net/travelcube/************************************.png\",\"promoTag\":\"商家券\"}],\"marketPricePromo\":\"178\",\"promoAbstractList\":[\"下单赠无门槛10元券\",\"满100减67\",\"团购优惠111元\",\"[{\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"strikethrough\\\":false,\\\"text\\\":\\\"金融券test环境使用\\\",\\\"textcolor\\\":\\\"#222222\\\",\\\"textsize\\\":12,\\\"textstyle\\\":\\\"Default\\\",\\\"underline\\\":false}]\"],\"finalPrice\":\"599\",\"priceDisplayType\":0,\"totalPromo\":\"67\",\"showBestPromoDetails\":true,\"couponPromo\":\"67\",\"promoPrice\":\"599\",\"promoNewStyle\":true,\"reductionPromo\":\"0\",\"couponList\":[{\"promoCouponButton\":{\"clickUrl\":\"\",\"actionType\":0,\"title\":\"点击领取\"},\"amount\":\"105\",\"amountDesc\":\"满1000可用\",\"couponType\":0,\"couponGroupId\":*********,\"timeDesc\":\"领取后15天有效\",\"amountCornerMark\":\"元\",\"sourceTag\":\"商家券\",\"title\":\"商家券\",\"status\":0},{\"promoCouponButton\":{\"clickUrl\":\"\",\"actionType\":0,\"title\":\"点击领取\"},\"amount\":\"104\",\"amountDesc\":\"满1000可用\",\"couponType\":0,\"couponGroupId\":101760790,\"timeDesc\":\"领取后15天有效\",\"amountCornerMark\":\"元\",\"sourceTag\":\"商家券\",\"title\":\"商家券\",\"status\":0},{\"promoCouponButton\":{\"clickUrl\":\"\",\"actionType\":0,\"title\":\"点击领取\"},\"amount\":\"103\",\"amountDesc\":\"满1000可用\",\"couponType\":0,\"couponGroupId\":982363189,\"timeDesc\":\"领取后15天有效\",\"amountCornerMark\":\"元\",\"sourceTag\":\"商家券\",\"title\":\"商家券\",\"status\":0},{\"promoCouponButton\":{\"clickUrl\":\"\",\"actionType\":0,\"title\":\"点击领取\"},\"amount\":\"102\",\"amountDesc\":\"满1000可用\",\"couponType\":0,\"couponGroupId\":962090966,\"timeDesc\":\"领取后15天有效\",\"amountCornerMark\":\"元\",\"sourceTag\":\"商家券\",\"title\":\"商家券\",\"status\":0},{\"promoCouponButton\":{\"clickUrl\":\"\",\"actionType\":0,\"title\":\"点击领取\"},\"amount\":\"67\",\"amountDesc\":\"满100可用\",\"couponType\":0,\"couponGroupId\":838211884,\"timeDesc\":\"领取后15天有效\",\"amountCornerMark\":\"元\",\"sourceTag\":\"商家券\",\"title\":\"商家券\",\"status\":0},{\"promoCouponButton\":{\"clickUrl\":\"\",\"actionType\":0,\"title\":\"点击领取\"},\"amount\":\"51\",\"amountDesc\":\"满100可用\",\"couponType\":0,\"couponGroupId\":221433127,\"timeDesc\":\"领取后15天有效\",\"amountCornerMark\":\"元\",\"sourceTag\":\"商家券\",\"title\":\"商家券\",\"status\":0},{\"promoCouponButton\":{\"clickUrl\":\"\",\"actionType\":0,\"title\":\"点击领取\"},\"amount\":\"45\",\"amountDesc\":\"满102可用\",\"couponType\":0,\"couponGroupId\":141087755,\"timeDesc\":\"领取后15天有效\",\"amountCornerMark\":\"元\",\"sourceTag\":\"商家券\",\"title\":\"商家券\",\"status\":0},{\"promoCouponButton\":{\"clickUrl\":\"\",\"actionType\":0,\"title\":\"点击领取\"},\"amount\":\"36\",\"amountDesc\":\"满80可用\",\"couponType\":0,\"couponGroupId\":478928199,\"timeDesc\":\"领取后15天有效\",\"amountCornerMark\":\"元\",\"sourceTag\":\"商家券\",\"title\":\"商家券\",\"status\":0},{\"promoCouponButton\":{\"clickUrl\":\"\",\"actionType\":0,\"title\":\"点击领取\"},\"amount\":\"23\",\"amountDesc\":\"满100可用\",\"couponType\":0,\"couponGroupId\":504371099,\"timeDesc\":\"领取后15天有效\",\"amountCornerMark\":\"元\",\"sourceTag\":\"商家券\",\"title\":\"商家券\",\"status\":0},{\"promoCouponButton\":{\"clickUrl\":\"\",\"actionType\":0,\"title\":\"点击领取\"},\"amount\":\"22\",\"amountDesc\":\"满100可用\",\"couponType\":0,\"couponGroupId\":509689839,\"timeDesc\":\"领取后15天有效\",\"amountCornerMark\":\"元\",\"sourceTag\":\"商家券\",\"title\":\"商家券\",\"status\":0},{\"promoCouponButton\":{\"clickUrl\":\"\",\"actionType\":0,\"title\":\"点击领取\"},\"amount\":\"21\",\"amountDesc\":\"满100可用\",\"couponType\":0,\"couponGroupId\":665198351,\"timeDesc\":\"领取后15天有效\",\"amountCornerMark\":\"元\",\"sourceTag\":\"商家券\",\"title\":\"商家券\",\"status\":0}],\"exposureList\":[],\"showMarketPrice\":true,\"dealGroupPrice\":\"666\",\"promoActivityList\":[{\"leadUrl\":\"https://stable.pay.test.sankuai.com/portal/bindcard/bindcard.html?merchant_no=1&ext_dim_stat_entry=1&callback_type=close_webview&_mtcq=0&utm_source=pay_app-pay-banner410419_540904&campaignId=1477039\",\"shortText\":\"[{\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"strikethrough\\\":false,\\\"text\\\":\\\"金融券test环境使用\\\",\\\"textcolor\\\":\\\"#222222\\\",\\\"textsize\\\":12,\\\"textstyle\\\":\\\"Default\\\",\\\"underline\\\":false}]\",\"style\":0,\"text\":\"[{\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"strikethrough\\\":false,\\\"text\\\":\\\"金融券test环境使用\\\",\\\"textcolor\\\":\\\"#222222\\\",\\\"textsize\\\":12,\\\"textstyle\\\":\\\"Default\\\",\\\"underline\\\":false}]\",\"bonusType\":\"省\"}],\"marketPromoDiscount\":\"7.8折\"},\"position\":\"2104\",\"originalOnlineConsultUrl\":\"https://test-g.meituan.com/arche/dzbiz/node-im-h5/index.html?toUid=sk5NzHlo0hThDaQoh&clientType=200501&chatType=0&bizId=*********&sendUnitType=6&secondCateId=157&firstCateId=50\",\"shop\":{\"shopPic\":\"http://p0.inf.test.sankuai.com/dpmerchantpic/da90e5e4ef7edea036214706785b424e271676.jpg\",\"buyBarIconType\":0,\"shopIdEncrypt\":\"h1yj79312f74326caa0cb878b5086bedd5fe93e1a454570577427711a2c5e14ca4e43d81b236a649bed82e7a82628e7b8397d6a3e55cd1fabb27522b483097e8225b250c311174802d55f7c7dc5b493b19cebf9dbdh7fh\",\"distance\":\"9.39km\",\"shopNum\":1,\"isLyyShop\":false,\"shopCategoryId\":0,\"avgPrice\":\"¥226/人\",\"hideStars\":false,\"shopName\":\"京世沙龙\",\"imUrl\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Ftest-g.meituan.com%2Farche%2Fdzbiz%2Fnode-im-h5%2Findex.html%3FtoUid%3Dsk5NzHlo0hThDaQoh%26clientType%3D200501%26chatType%3D0%26bizId%3D*********%26sendUnitType%3D6%26secondCateId%3D157%26firstCateId%3D50\",\"businessState\":\"营业中\",\"showType\":\"beauty\",\"shopId\":403561,\"displayPosition\":1,\"lat\":31.243981201581768,\"address\":\"澳门路729号\",\"lng\":121.43149322989431,\"businessHour\":\"周一至周日 10:00-21:00\",\"branchName\":\"澳门店\",\"shopPower\":43,\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=403561&mmcinflate=&mmcuse=&mmcbuy=&mmcfree=&channelType=\",\"phoneNos\":[\"***********\",\"***********\"],\"distanceDesc\":\"距您9.39km\",\"mapUrl\":\"imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=403561&latitude=31.243981201581768&longitude=121.43149322989431\",\"shopType\":50,\"hideAddrEnable\":false,\"shopBizType\":0},\"limitsExtend\":[{\"iconHeight\":0,\"iconWidth\":0,\"style\":\"#FF4B10\",\"text\":\"每人不限购买次数，不限会员使用\",\"type\":1}],\"guarantee\":[{\"iconHeight\":0,\"iconWidth\":0,\"text\":\"随时退\",\"type\":0},{\"iconHeight\":0,\"iconWidth\":0,\"text\":\"过期退\",\"type\":0}],\"title\":\"自动化测试勿动\",\"buyBar\":{\"styleType\":1,\"buyType\":0,\"buyBanner\":{\"iconHeight\":0,\"leadAction\":0,\"iconWidth\":0,\"bannerType\":4,\"show\":true,\"iconUrl\":\"https://p0.meituan.net/ingee/da8c587fba577ab8d535a2c3a5ab20773446.png\",\"content\":\"[{\\\"text\\\":\\\"你有\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"222222\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"67\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF4B10\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Bold\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"元专属优惠券，点击下单领取可立享优惠\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"222222\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false}]\"},\"buyBtns\":[{\"usePhone\":false,\"redirectUrl\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=dealsubmitorderpage-popup&mrn_min_version=0.0.540&dealid=*********&shopid=403561&skuid=&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&shopidEncrypt=h1yj495877772b2b4770171d8c0ced56c0004db0d81e754d8cb285099565ab8bc7025ad1d1ae290a4386bbd9c7h7fh&pagesource=dealGroupDetail&is_sku=0&isMagicalPrice=false&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUg5AEyBGXFIrPBl7wrTdGIDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO12lOiBtX9-ed6Fz8VDbvf5R4xLuKZIuoUnVCl9iZRhn4kCKYZ_GRPFWdFem69xUiMKD454ZUT648SFJb5Xmlo-wtoa25ruW8udImOSQfWYNKUInZksn1hcu7EONmuO5JfXhRxWfAniRMus9hLrPmLFCIwmUCG1fHGrOmgJIrnfBqCwXDcsepHKeBIbTx4x4ERjAkG2jC4nRjK5jODQYaUO3vWhgdDgoBaCk3VfUbLMZkLxLpkdlPqiEuowqz9aPE2ZLqWFARUzux_v5jcIxOyXcGHEXpXWz7ZdRXbOECY5mQ\",\"btnText\":\"￥599 立即购买\",\"addShoppingCartStatus\":1,\"coupon\":{\"unifiedcoupongroupids\":\"838211884\",\"couponGroupId\":838211884,\"status\":0},\"pricePrefix\":\"券后\",\"btnDesc\":\"[{\\\"text\\\":\\\"门市价 ￥\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF999999\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":false,\\\"underline\\\":false},{\\\"text\\\":\\\"777\\\",\\\"textsize\\\":12,\\\"textcolor\\\":\\\"#FF999999\\\",\\\"backgroundcolor\\\":\\\"#00FFFFFF\\\",\\\"textstyle\\\":\\\"Default\\\",\\\"strikethrough\\\":true,\\\"underline\\\":false}]\",\"btnTag\":\"共省¥178\",\"priceRuleModule\":{\"priceRuleTags\":[\"团购价\",\"￥599\"],\"priceRuleType\":1,\"promoDesc\":\"共省￥178\"},\"btnTitle\":\"领券抢购\",\"btnIcons\":[{\"borderColor\":\"#646464\",\"titleColor\":\"#646464\",\"bgColor\":\"#FFFFFF\",\"style\":\"#646464\",\"title\":\"共省¥178\",\"type\":1}],\"priceStr\":\"599\",\"detailBuyType\":1,\"btnEnable\":true}]},\"mLiveInfoVo\":{\"mLiveId\":0,\"goodsTypeId\":\"0\",\"channelIdentity\":false},\"onlineConsultUrl\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Ftest-g.meituan.com%2Farche%2Fdzbiz%2Fnode-im-h5%2Findex.html%3FtoUid%3Dsk5NzHlo0hThDaQoh%26clientType%3D200501%26chatType%3D0%26bizId%3D*********%26sendUnitType%3D6%26secondCateId%3D157%26firstCateId%3D50\",\"saleDescStr\":\"年售24\",\"hitStructuredPurchaseNote\":true,\"picAspectRatio\":0,\"displayPrice\":\"666\",\"moduleExtra\":{\"moduleConfigDos\":[{\"value\":\"gcdealdetail_newtuandealtab_tuandetail\",\"key\":\"团购详情\"},{\"value\":\"gcdealdetail_newtuandealtab_beauty_nail_buyrules\",\"key\":\"购买须知\"},{\"value\":\"gcdealdetail_newtuandealtab_reviews\",\"key\":\"网友评价\"}],\"success\":true,\"expResults\":[{\"configs\":[{\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"e5557dc3-e124-468b-8cf1-d1394edb1bbb\\\",\\\"ab_id\\\":\\\"exp001434_a\\\"}\",\"expId\":\"exp001434\"}],\"key\":\"MTSalesGeneralSection\"},{\"configs\":[{\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"ef9c81cc-d596-425a-b137-c592f79849fc\\\",\\\"ab_id\\\":\\\"EXP2024082700008_c\\\"}\",\"expId\":\"EXP2024082700008\"}],\"key\":\"MtCouponAlleviate2Exp\"},{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"f30e3400-4f79-4629-acf0-66f0f6724400\\\",\\\"ab_id\\\":\\\"exp001510_b\\\"}\",\"expId\":\"exp001510\"}],\"key\":\"MTShoppingCartBuyBar\"},{\"configs\":[{\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"a24e8b89-c3ca-43a6-804b-e2efb731e132\\\",\\\"ab_id\\\":\\\"exp001692_a\\\"}\",\"expId\":\"exp001692\"}],\"key\":\"MTShoppingCartBuyBarNew\"},{\"configs\":[{\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"0fc4d091-9e15-4367-8851-37779afd4bb8\\\",\\\"ab_id\\\":\\\"exp001707_c\\\"}\",\"expId\":\"exp001707\"}],\"key\":\"MTCouponBar\"},{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"9a833e85-1f06-47d4-821f-89b0decb0a50\\\",\\\"ab_id\\\":\\\"exp001872_b\\\"}\",\"expId\":\"exp001872\"}],\"key\":\"MtPurchaseNoteStructure\"},{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"3443f588-b3f6-4185-b2d3-525663ea0810\\\",\\\"ab_id\\\":\\\"exp001799_b\\\"}\",\"expId\":\"exp001799\"}],\"key\":\"CardStyleAB_V2_MT_meijia\"},{\"configs\":[{\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"b5212dc4-c8b2-44a8-a1f4-51bfa08808f4\\\",\\\"ab_id\\\":\\\"exp001940_a\\\"}\",\"expId\":\"exp001940\"}],\"key\":\"MtComparePrice\"},{\"configs\":[{\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"a970676a-add7-485c-998a-57e81d8c44f3\\\",\\\"ab_id\\\":\\\"exp001940_a\\\"}\",\"expId\":\"exp001940\"}],\"key\":\"MtHotNailModule\"},{\"configs\":[{\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"398cdf80-a8a5-4740-8760-cc9237be25a3\\\",\\\"ab_id\\\":\\\"EXP2024082700008_c\\\"}\",\"expId\":\"EXP2024082700008\"}],\"key\":\"MtCouponAlleviate1Exp\"}]},\"serviceTypeId\":651,\"limits\":[\"每人不限购买次数，不限会员使用\"],\"tradeType\":1,\"purchaseLimitDeal\":false,\"reminderInfo\":[\"免预约\",\"周一至周日全天可用\",\"购买后90天内有效\"],\"priceDisplayModuleDo\":{\"promoPrice\":\"减后价 ￥599\",\"enableDisplay\":false,\"marketPrice\":\"777\",\"price\":\"599\",\"dealGroupPrice\":\"666\",\"promoTag\":\"已优惠67\"},\"moduleAbConfigs\":[{\"configs\":[{\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"e5557dc3-e124-468b-8cf1-d1394edb1bbb\\\",\\\"ab_id\\\":\\\"exp001434_a\\\"}\",\"expId\":\"exp001434\"}],\"key\":\"MTSalesGeneralSection\"},{\"configs\":[{\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"ef9c81cc-d596-425a-b137-c592f79849fc\\\",\\\"ab_id\\\":\\\"EXP2024082700008_c\\\"}\",\"expId\":\"EXP2024082700008\"}],\"key\":\"MtCouponAlleviate2Exp\"},{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"f30e3400-4f79-4629-acf0-66f0f6724400\\\",\\\"ab_id\\\":\\\"exp001510_b\\\"}\",\"expId\":\"exp001510\"}],\"key\":\"MTShoppingCartBuyBar\"},{\"configs\":[{\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"a24e8b89-c3ca-43a6-804b-e2efb731e132\\\",\\\"ab_id\\\":\\\"exp001692_a\\\"}\",\"expId\":\"exp001692\"}],\"key\":\"MTShoppingCartBuyBarNew\"},{\"configs\":[{\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"0fc4d091-9e15-4367-8851-37779afd4bb8\\\",\\\"ab_id\\\":\\\"exp001707_c\\\"}\",\"expId\":\"exp001707\"}],\"key\":\"MTCouponBar\"},{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"9a833e85-1f06-47d4-821f-89b0decb0a50\\\",\\\"ab_id\\\":\\\"exp001872_b\\\"}\",\"expId\":\"exp001872\"}],\"key\":\"MtPurchaseNoteStructure\"},{\"configs\":[{\"expResult\":\"b\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"3443f588-b3f6-4185-b2d3-525663ea0810\\\",\\\"ab_id\\\":\\\"exp001799_b\\\"}\",\"expId\":\"exp001799\"}],\"key\":\"CardStyleAB_V2_MT_meijia\"},{\"configs\":[{\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"b5212dc4-c8b2-44a8-a1f4-51bfa08808f4\\\",\\\"ab_id\\\":\\\"exp001940_a\\\"}\",\"expId\":\"exp001940\"}],\"key\":\"MtComparePrice\"},{\"configs\":[{\"expResult\":\"a\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"a970676a-add7-485c-998a-57e81d8c44f3\\\",\\\"ab_id\\\":\\\"exp001940_a\\\"}\",\"expId\":\"exp001940\"}],\"key\":\"MtHotNailModule\"},{\"configs\":[{\"expResult\":\"c\",\"expBiInfo\":\"{\\\"query_id\\\":\\\"398cdf80-a8a5-4740-8760-cc9237be25a3\\\",\\\"ab_id\\\":\\\"EXP2024082700008_c\\\"}\",\"expId\":\"EXP2024082700008\"}],\"key\":\"MtCouponAlleviate1Exp\"}],\"showNewReserveEntrance\":false,\"shareModule\":{\"mt\":{\"imgUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/b16914bb56ee35c7d548a1bedc107b07108537.png%40180w_132h_1e_1c_1l%7Cwatermark%3D0\",\"brandName\":\"自动化测试勿动\",\"mtDealGroupId\":*********,\"price\":666},\"shareId\":\"m42kkvaiet9q\",\"screenShotShareEnable\":true},\"dpId\":*********,\"needLogin\":false,\"dealName\":\"自动化测试勿动\",\"shareAble\":true,\"shopCardState\":4,\"moreDealsModule\":{\"publishCategoryId\":502,\"buCode\":5},\"extraStyles\":[],\"bgName\":\"general\",\"dealContents\":[{\"scale\":\"16:9\",\"type\":1,\"content\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/b16914bb56ee35c7d548a1bedc107b07108537.png%40960w_540h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D2%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"}],\"categoryId\":502,\"maxPerUser\":0},\"grouponmembermodule\":{\"msg\":\"成功\",\"code\":200,\"data\":{\"commonResp\":{\"msg\":\"成功\",\"code\":200},\"displayMemberModule\":false}},\"leadbuycardtips\":{\"code\":200,\"flag\":0,\"icon\":0,\"title\":\"提示\",\"content\":\"未查询到卡信息\",\"statusCode\":{\"code\":200}},\"activity\":{\"success\":true},\"queryproductranklabel\":{\"rankType\":0,\"ranking\":0},\"dzdealstyle\":{\"moduleConfigsModule\":{\"isDzx\":true,\"isDpOrder\":true,\"generalInfo\":\"card_style_v2\",\"isTort\":false,\"key\":\"default\",\"extraInfo\":\"newtuandeal\"}},\"timescardmiddleinfo\":{}}}";
        DealNativeSnapshotReq request = JSON.parseObject(requestStr, DealNativeSnapshotReq.class);
        DealBffResponseDTO bffResponse = JSON.parseObject(responseStr, DealBffResponseDTO.class);
        DealHeadPicHeightCalculateHandler handler = new DealHeadPicHeightCalculateHandler();
        TransparentContainerHeightCalculateHandler handler2 = new TransparentContainerHeightCalculateHandler();
        handler.customerProcess(request, bffResponse);
        handler2.customerProcess(request, bffResponse);
        System.out.println(JSON.toJSONString(bffResponse));
        JSONObject dealMainInterfaceData = bffResponse.getBffResponse(RcfDealBffInterfaceEnum.dzdealbase);
        JSONArray dealContents = (JSONArray) dealMainInterfaceData.get("dealContents");
        JSONObject dealContent = (JSONObject) dealContents.stream().findFirst().get();
        double rcfPicHeight = (double) dealContent.get("rcfPicHeight");
        double rcfTransparentContainerHeight = (double) dealContent.get("rcfTransparentContainerHeight");
        Assert.assertTrue(rcfPicHeight > 0 && rcfTransparentContainerHeight > 0);
    }
}
