package com.dianping.mobile.mapi.dztgdetail.faulttolerance.deal.executor;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.framework.base.datatypes.StatusCode;
import com.dianping.mobile.framework.datatypes.CommonMobileResponse;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.mapi.dztgdetail.biz.CreateOrderPageUrlBiz;
import com.dianping.mobile.mapi.dztgdetail.biz.DealStyleStatisticService;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.Response;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.facade.DealQueryFacade;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.facade.DealQueryParallFacade;
import com.dianping.mobile.mapi.dztgdetail.util.AntiCrawlerUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.meituan.mafka.client.bean.MafkaProducer;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DzDealBaseExecutorGetExecuteResultTest {

    @InjectMocks
    private DzDealBaseExecutor executor;

    @Mock
    private DealQueryFacade dealQueryFacade;

    @Mock
    private DealQueryParallFacade dealQueryParallFacade;

    @Mock
    private CreateOrderPageUrlBiz createOrderPageUrlBiz;

    @Mock
    private DealStyleStatisticService dealStyleStatisticService;

    @Mock
    private MafkaProducer itemBrowseProducer;

    @Mock
    private IMobileContext iMobileContext;

    @Mock
    private EnvCtx envCtx;

    private DealBaseReq request;

    @Before
    public void setUp() {
        request = new DealBaseReq();
        request.setDealgroupid(123);
        request.setMrnversion("0.5.6");
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.DIANPING_APP);
    }

    /**
     * Test unlogin user scenario
     */
    @Test
    public void testGetExecuteResult_UnloginUser() throws Throwable {
        // arrange
        when(envCtx.isLogin()).thenReturn(false);
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        dealGroupPBO.setNeedLogin(true);
        Response<DealGroupPBO> response = Response.createSuccessResponse(dealGroupPBO);
        // act
        CommonMobileResponse result = executor.getExecuteResult(request, iMobileContext, envCtx, true);
        // assert
        assertNotNull(result);
        assertTrue(result.getData() instanceof DealGroupPBO);
        DealGroupPBO pbo = (DealGroupPBO) result.getData();
        assertTrue(pbo.isNeedLogin());
    }

    /**
     * Test Rhino rejection scenario
     */
    @Test
    public void testGetExecuteResult_RhinoReject() throws Throwable {
        // arrange
        when(envCtx.isLogin()).thenReturn(true);
        when(envCtx.getUnionId()).thenReturn("test123");
        Response<DealGroupPBO> rhinoResponse = Response.createRhinoRejectResponse("rejected", null);
        when(dealQueryParallFacade.queryDealGroup(any(), any(), any())).thenReturn(rhinoResponse);
        // act
        CommonMobileResponse result = executor.getExecuteResult(request, iMobileContext, envCtx, true);
        // assert
        assertNotNull(result);
        assertEquals(StatusCode.ACTIONEXCEPTION.getCode(), result.getStatusCode().getCode());
    }

    /**
     * Test error scenario
     */
    @Test
    public void testGetExecuteResult_Error() throws Throwable {
        // arrange
        when(envCtx.isLogin()).thenReturn(true);
        when(envCtx.getUnionId()).thenReturn("test123");
        when(dealQueryParallFacade.queryDealGroup(any(), any(), any())).thenThrow(new RuntimeException("Test error"));
        // act
        CommonMobileResponse result = executor.getExecuteResult(request, iMobileContext, envCtx, true);
        // assert
        assertNotNull(result);
        assertEquals(StatusCode.ACTIONEXCEPTION.getCode(), result.getStatusCode().getCode());
    }

    /**
     * Test module config with tort flag
     */
    @Test
    public void testGetExecuteResult_ModuleConfigWithTort() throws Throwable {
        // arrange
        when(envCtx.isLogin()).thenReturn(true);
        when(envCtx.getUnionId()).thenReturn("test123");
        DealGroupPBO dealGroupPBO = new DealGroupPBO();
        dealGroupPBO.setDpId(123);
        ModuleConfigsModule moduleConfigsModule = new ModuleConfigsModule();
        moduleConfigsModule.setTort(true);
        dealGroupPBO.setModuleConfigsModule(moduleConfigsModule);
        Response<DealGroupPBO> successResponse = Response.createSuccessResponse(dealGroupPBO);
        when(dealQueryParallFacade.queryDealGroup(any(), any(), any())).thenReturn(successResponse);
        // act
        CommonMobileResponse result = executor.getExecuteResult(request, iMobileContext, envCtx, true);
        // assert
        assertNotNull(result);
        assertTrue(result.getData() instanceof DealGroupPBO);
        DealGroupPBO pbo = (DealGroupPBO) result.getData();
        assertEquals(123, pbo.getDpId());
        assertTrue(pbo.getModuleConfigsModule().isTort());
    }
}
