package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.exception.DealGroupResultException;
import com.dianping.mobile.mapi.dztgdetail.exception.DealMatchSalePlatformException;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/10/15 20:03
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest()
public class DealSalesPlatformProcessorTest {
    @InjectMocks
    private DealSalesPlatformProcessor dealSalesPlatformProcessor;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Rule
    public final ExpectedException exception = ExpectedException.none();

    @Test
    public void testProcessByQueryCenter() throws InvocationTargetException, IllegalAccessException {
        DealCtx ctx = new DealCtx(new EnvCtx());
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setBasic(new DealGroupBasicDTO());
        ctx.setDealGroupDTO(dealGroup);

        Method method = PowerMockito.method(DealSalesPlatformProcessor.class, "processByQueryCenter");
        exception.expect(DealMatchSalePlatformException.class);
        method.invoke(dealSalesPlatformProcessor,ctx);

    }

    @Test
    public void testProcessException() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setUseQueryCenter(false);
        // GreyUtils不是静态类
        // PowerMockito.mockStatic(GreyUtils.class);
        // when(GreyUtils.enableQueryCenterForMainApi(any())).thenReturn(false);
        exception.expect(DealGroupResultException.class);
        dealSalesPlatformProcessor.process(ctx);
    }
}