package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.dianping.account.MeituanUserService;
import com.dianping.account.UserAccountService;
import com.dianping.account.dto.MeituanUserInfoDTO;
import com.dianping.account.dto.UserAccountDTO;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

public class UserAccountInfoProcessorTest {

    @Mock
    private UserAccountService userAccountServiceFuture;
    @Mock
    private MeituanUserService meituanUserServiceFuture;
    @InjectMocks
    private UserAccountInfoProcessor processor;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        Mockito.doReturn(buildUserAccountDTO()).when(userAccountServiceFuture).loadById(Mockito.anyLong());
        Mockito.doReturn(buildMeituanUserInfoDTO()).when(meituanUserServiceFuture).loadUserByMTUid(Mockito.anyLong());
    }

    /**
     * 构建点评用户对象
     */
    private UserAccountDTO buildUserAccountDTO() {
        UserAccountDTO userAccountDTO = new UserAccountDTO();
        userAccountDTO.setMobile("***********");
        return userAccountDTO;
    }

    /**
     * 构建美团用户对象
     */
    private MeituanUserInfoDTO buildMeituanUserInfoDTO() {
        String json = "{\"dPUid\":9000000000092554878,\"mTUid\":**********,\"mobileNo\":\"***********\",\"type\":0}";
        return JSON.parseObject(json, MeituanUserInfoDTO.class);
    }

    private DealCtx buildMtDealCtx() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setMtUserId(123L);
        DealCtx ctx = new DealCtx(envCtx);
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("dealGroupFitnessPassConfig");
        attributeDTO.setValue(Collections.singletonList("fitnessPass"));
        ctx.setAttrs(Collections.singletonList(attributeDTO));
        return ctx;
    }

    private DealCtx buildDpDealCtx() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        envCtx.setDpUserId(123L);
        DealCtx ctx = new DealCtx(envCtx);
        AttributeDTO attributeDTO = new AttributeDTO();
        attributeDTO.setName("dealGroupFitnessPassConfig");
        attributeDTO.setValue(Collections.singletonList("fitnessPass"));
        ctx.setAttrs(Collections.singletonList(attributeDTO));
        return ctx;
    }

    @Test
    @Ignore
    public void test() throws Exception {
        DealCtx dpCtx = buildDpDealCtx();
        DealCtx mtCtx = buildMtDealCtx();

        Assert.assertTrue(processor.isEnable(dpCtx));

        processor.prepare(dpCtx);
        Assert.assertNull(dpCtx.getFutureCtx().getDpUserAccountDTOFuture());

        processor.prepare(mtCtx);
        Assert.assertNull(mtCtx.getFutureCtx().getMtUserAccountDTOFuture());
    }

}