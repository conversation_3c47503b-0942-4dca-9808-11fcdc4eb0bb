package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryEnum;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.utils.EducationDealAttrUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SpecificModuleHandler_1205GetSuitableClassTest {

    @InjectMocks
    private SpecificModuleHandler_1205 specificModuleHandler_1205;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    private Method getSuitableClassMethod;

    @Before
    public void setUp() throws Exception {
        // Use reflection to access the private method
        getSuitableClassMethod = SpecificModuleHandler_1205.class.getDeclaredMethod("getSuitableClass", SpecificModuleCtx.class);
        getSuitableClassMethod.setAccessible(true);
    }

    @Test(expected = NullPointerException.class)
    public void testGetSuitableClassWhenContextIsNull() throws Throwable {
        try {
            // Invoke the private method using reflection
            getSuitableClassMethod.invoke(specificModuleHandler_1205, (SpecificModuleCtx) null);
        } catch (InvocationTargetException e) {
            // Unwrap the InvocationTargetException to get the actual exception
            throw e.getCause();
        }
    }

    @Test(expected = NullPointerException.class)
    public void testGetSuitableClassWhenDealGroupDTOIsNull() throws Throwable {
        SpecificModuleCtx context = new SpecificModuleCtx();
        try {
            getSuitableClassMethod.invoke(specificModuleHandler_1205, context);
        } catch (InvocationTargetException e) {
            // Unwrap the InvocationTargetException to get the actual exception
            throw e.getCause();
        }
    }

    @Test
    public void testGetSuitableClassWhenSuitableClassIsNull() throws Throwable {
        SpecificModuleCtx context = new SpecificModuleCtx();
        context.setDealGroupDTO(new DealGroupDTO());
        DealDetailDisplayUnitVO result = (DealDetailDisplayUnitVO) getSuitableClassMethod.invoke(specificModuleHandler_1205, context);
        assertNull(result);
    }
}
