package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import java.lang.reflect.Constructor;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PriceDisplayWrapper_GetProductMapTest {

    @InjectMocks
    private PriceDisplayWrapper priceDisplayWrapper;

    @Mock
    private Future<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> priceFuture;

    private void setUpCommonMocks(boolean isSuccess, boolean isDataEmpty) throws Exception {
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> priceResponse = mock(PriceResponse.class);
        Map<Long, List<PriceDisplayDTO>> priceData = new HashMap<>();
        if (!isDataEmpty) {
            PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
            // Using reflection to instantiate ProductIdentity
            Constructor<ProductIdentity> constructor = ProductIdentity.class.getDeclaredConstructor();
            constructor.setAccessible(true);
            ProductIdentity identity = constructor.newInstance();
            identity.setProductId(1);
            priceDisplayDTO.setIdentity(identity);
            priceData.put(1L, Collections.singletonList(priceDisplayDTO));
        }
        when(priceFuture.get()).thenReturn(priceResponse);
        when(priceResponse.isSuccess()).thenReturn(isSuccess);
        when(priceResponse.getData()).thenReturn(priceData);
    }

    @Test
    public void testGetProductMap0WhenPriceFutureIsNull() throws Throwable {
        Map<Long, Map<Integer, PriceDisplayDTO>> result = priceDisplayWrapper.getProductMap0(null);
        assertNull(result);
    }

    @Test
    public void testGetProductMap0WhenPriceResponseIsNotSuccess() throws Throwable {
        setUpCommonMocks(false, false);
        Map<Long, Map<Integer, PriceDisplayDTO>> result = priceDisplayWrapper.getProductMap0(priceFuture);
        assertNull(result);
    }

    @Test
    public void testGetProductMap0WhenPriceDataIsEmpty() throws Throwable {
        setUpCommonMocks(true, true);
        Map<Long, Map<Integer, PriceDisplayDTO>> result = priceDisplayWrapper.getProductMap0(priceFuture);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetProductMap0WhenPriceDataIsNotEmpty() throws Throwable {
        setUpCommonMocks(true, false);
        Map<Long, Map<Integer, PriceDisplayDTO>> result = priceDisplayWrapper.getProductMap0(priceFuture);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(1L));
        assertEquals(1, result.get(1L).size());
        assertTrue(result.get(1L).containsKey(1));
        assertNotNull(result.get(1L).get(1));
    }

    @Test
    public void testGetProductMap0WhenExceptionOccurs() throws Throwable {
        when(priceFuture.get()).thenThrow(new RuntimeException());
        Map<Long, Map<Integer, PriceDisplayDTO>> result = priceDisplayWrapper.getProductMap0(priceFuture);
        assertNull(result);
    }

    @Test
    public void testGetProductMap_Success() throws Throwable {
        // arrange
        PriceDisplayWrapper priceDisplayWrapper = new PriceDisplayWrapper();
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> priceResponse = mock(PriceResponse.class);
        when(priceResponse.isSuccess()).thenReturn(true);
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        // Assuming productType 1 is valid
        ProductIdentity productIdentity = new ProductIdentity(1, 1);
        priceDisplayDTO.setIdentity(productIdentity);
        Map<Long, List<PriceDisplayDTO>> data = new HashMap<>();
        data.put(1L, Collections.singletonList(priceDisplayDTO));
        when(priceResponse.getData()).thenReturn(data);
        when(priceFuture.get()).thenReturn(priceResponse);
        // act
        Map<Integer, PriceDisplayDTO> result = priceDisplayWrapper.getProductMap(priceFuture);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        // Check if the key (product ID) is present
        assertTrue(result.containsKey(1));
    }

    @Test
    public void testGetProductMap_Failure() throws Throwable {
        // arrange
        PriceDisplayWrapper priceDisplayWrapper = new PriceDisplayWrapper();
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> priceResponse = mock(PriceResponse.class);
        when(priceResponse.isSuccess()).thenReturn(false);
        when(priceFuture.get()).thenReturn(priceResponse);
        // act
        Map<Integer, PriceDisplayDTO> result = priceDisplayWrapper.getProductMap(priceFuture);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetProductMap_Exception() throws Throwable {
        // arrange
        PriceDisplayWrapper priceDisplayWrapper = new PriceDisplayWrapper();
        when(priceFuture.get()).thenThrow(new InterruptedException());
        // act
        Map<Integer, PriceDisplayDTO> result = null;
        try {
            result = priceDisplayWrapper.getProductMap(priceFuture);
        } catch (Exception e) {
            assertNull(result);
        }
    }

    @Test
    public void testGetProductMap_Null() throws Throwable {
        // arrange
        PriceDisplayWrapper priceDisplayWrapper = new PriceDisplayWrapper();
        when(priceFuture.get()).thenReturn(null);
        // act
        Map<Integer, PriceDisplayDTO> result = priceDisplayWrapper.getProductMap(priceFuture);
        // assert
        assertNull(result);
    }
}
