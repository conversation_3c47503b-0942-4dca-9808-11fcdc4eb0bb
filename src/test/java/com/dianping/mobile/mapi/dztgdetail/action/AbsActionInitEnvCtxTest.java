package com.dianping.mobile.mapi.dztgdetail.action;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.mobile.base.datatypes.bean.ClientInfoRule;
import com.dianping.mobile.framework.action.DefaultAction;
import com.dianping.mobile.framework.datatypes.IMobileContext;
import com.dianping.mobile.framework.datatypes.IMobileRequest;
import com.dianping.mobile.framework.datatypes.IMobileResponse;
import com.dianping.mobile.framework.datatypes.MobileHeader;
import com.dianping.mobile.framework.datatypes.token.TokenParseStatus;
import com.dianping.mobile.framework.datatypes.token.UserStatusResult;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import com.maoyan.mtrace.Tracer;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbsActionInitEnvCtxTest {

    @Mock
    private IMobileContext appCtx;

    @Mock
    private HttpServletRequest request;

    @Mock
    private MobileHeader mobileHeader;

    private TestAbsAction absAction;

    @Before
    public void setUp() {
        absAction = new TestAbsAction();
        setupCommonMocks();
    }

    private void setupCommonMocks() {
        when(appCtx.getRequest()).thenReturn(request);
        when(appCtx.getHeader()).thenReturn(mobileHeader);
        when(mobileHeader.getUuid()).thenReturn("test-uuid");
        Map<String, String> headers = new HashMap<>();
        headers.put("mtsi-flag", "test-flag");
        when(appCtx.getHeaders()).thenReturn(headers);
        when(appCtx.getUserIp()).thenReturn("127.0.0.1");
        when(request.getRequestURI()).thenReturn("/test/uri");
        when(appCtx.getVersion()).thenReturn("1.0.0");
        when(appCtx.getAppDeviceId()).thenReturn("test-device-id");
        // Default client type mocks
        when(appCtx.isMeituanClient()).thenReturn(false);
        when(appCtx.isDianpingClient()).thenReturn(false);
        when(request.getHeader("mpAppId")).thenReturn(null);
    }

    /**
     * Test when appCtx is null
     */
    @Test
    public void testInitEnvCtxWhenAppCtxIsNull() throws Throwable {
        // act
        EnvCtx result = absAction.initEnvCtx(null);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getClientType());
    }

    /**
     * Test when appCtx is Meituan client with iOS device
     */
    @Test
    public void testInitEnvCtxWhenMeituanClientIOS() throws Throwable {
        // arrange
        when(appCtx.isMeituanClient()).thenReturn(true);
        when(appCtx.isIOS()).thenReturn(true);
        when(appCtx.getUserAgent()).thenReturn("meituan/11.4.10 (iPhone; iOS 14.0)");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.MEITUAN_APP, result.getDztgClientTypeEnum());
        assertEquals("test-uuid", result.getUuid());
        assertEquals("1.0.0", result.getVersion());
    }

    /**
     * Test when appCtx is Dianping client with Android device
     */
    @Test
    public void testInitEnvCtxWhenDianpingClientAndroid() throws Throwable {
        // arrange
        when(appCtx.isDianpingClient()).thenReturn(true);
        when(appCtx.isIOS()).thenReturn(false);
        when(appCtx.getUserAgent()).thenReturn("dianping/11.4.10 (Android)");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.DIANPING_APP, result.getDztgClientTypeEnum());
        assertEquals("test-uuid", result.getUuid());
    }

    /**
     * Test when appCtx is Meituan Map App
     */
    @Test
    public void testInitEnvCtxWhenMeituanMapApp() throws Throwable {
        // arrange
        when(appCtx.getAppId()).thenReturn(396);
        when(appCtx.getVersion()).thenReturn("2.0.0");
        when(appCtx.getAppDeviceId()).thenReturn("map-device-id");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.MEITUAN_MAP_APP, result.getDztgClientTypeEnum());
        assertEquals("2.0.0", result.getVersion());
        assertEquals("map-device-id", result.getUnionId());
    }

    /**
     * Test when appCtx is Kuaishou Mini Program
     */
    @Test
    public void testInitEnvCtxWhenKuaishouMiniProgram() throws Throwable {
        // arrange
        when(request.getHeader("mpAppId")).thenReturn("ks652177521456999275");
        when(request.getHeader("mpSource")).thenReturn("kuaishou");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.MEITUAN_KUAISHOU_MINIAPP, result.getDztgClientTypeEnum());
        assertEquals("kuaishou", result.getMpSource());
    }

    /**
     * Test when appCtx is WeChat Mini Program
     */
    @Test
    public void testInitEnvCtxWhenWechatMiniProgram() throws Throwable {
        // arrange
        when(request.getHeader("mpAppId")).thenReturn("wxde8ac0a21135c07d");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(DztgClientTypeEnum.MEITUAN_WEIXIN_MINIAPP, result.getDztgClientTypeEnum());
    }

    /**
     * Test when appCtx has user status
     */
    @Test
    public void testInitEnvCtxWithUserStatus() throws Throwable {
        // arrange
        UserStatusResult userStatus = new UserStatusResult(123L, 456L, TokenParseStatus.SUCCESS);
        when(appCtx.getUserStatus()).thenReturn(userStatus);
        when(appCtx.getUserId()).thenReturn(123L);
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(123L, result.getDpUserId());
        assertEquals(123L, result.getMtUserId());
    }

    /**
     * Test when appCtx has different userId and mtUserId
     */
    @Test
    public void testInitEnvCtxWithDifferentUserIds() throws Throwable {
        // arrange
        UserStatusResult userStatus = new UserStatusResult(123L, 456L, TokenParseStatus.SUCCESS);
        when(appCtx.getUserStatus()).thenReturn(userStatus);
        when(appCtx.getUserId()).thenReturn(0L);
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertEquals(123L, result.getDpUserId());
        assertEquals(456L, result.getMtUserId());
    }

    /**
     * Test when appCtx is harmony device
     */
    @Test
    public void testInitEnvCtxWhenHarmonyDevice() throws Throwable {
        // arrange
        when(request.getHeader("user-agent")).thenReturn("openharmony");
        // act
        EnvCtx result = absAction.initEnvCtx(appCtx);
        // assert
        assertTrue(result.isHarmony());
    }

    private static class TestAbsAction extends AbsAction<IMobileRequest> {

        @Override
        protected IMobileResponse validate(IMobileRequest request, IMobileContext context) {
            return null;
        }

        @Override
        protected IMobileResponse execute(IMobileRequest request, IMobileContext context) {
            return null;
        }

        @Override
        protected List<ClientInfoRule> getRule() {
            return Collections.emptyList();
        }
    }
}
