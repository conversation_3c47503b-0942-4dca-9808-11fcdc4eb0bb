package com.dianping.mobile.mapi.dztgdetail.rcf.domian.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten.DealListType1FlattenHandler;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten.DealModuleFlattenProcessor;
import com.dianping.mobile.mapi.dztgdetail.rcf.domian.handler.flatten.FlattenProcessorFactory;

import com.dianping.mobile.mapi.dztgdetail.util.ApplicationContextGetBeanHelper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.ApplicationContext;

import java.lang.reflect.Field;

public class DealDetailFlattenServiceTest {

    @Mock
    private FlattenProcessorFactory flattenProcessorFactory = new FlattenProcessorFactory();


    @InjectMocks
    private DealDetailFlattenService dealDetailFlattenService = new DealDetailFlattenService();


    @Test
   public void testFlattenDetail() {
        // 准备测试数据
        JSONObject dealModuleResult = new JSONObject();
        JSONArray moduleList = new JSONArray();
        JSONObject module = new JSONObject();
        module.put("type", "TEST_TYPE");
        moduleList.add(module);
        dealModuleResult.put("moduleList", moduleList);

        Class clazz = dealDetailFlattenService.getClass();
        try {
            Field field = clazz.getDeclaredField("flattenProcessorFactory");
            field.setAccessible(true);
            field.set(dealDetailFlattenService, flattenProcessorFactory);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }

        // 执行被测试的方法
        dealDetailFlattenService.flattenDetail(dealModuleResult);

        // 验证结果

        // 验证 module 中是否添加了 rcfSkuGroupsModule1Flatten
        assert !module.containsKey("rcfSkuGroupsModule1Flatten");
    }


}