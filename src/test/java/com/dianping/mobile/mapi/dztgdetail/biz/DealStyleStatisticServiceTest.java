package com.dianping.mobile.mapi.dztgdetail.biz;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.DealBaseReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigsModule;
import com.dianping.mobile.mapi.dztgdetail.entity.DealStyleConfig;
import com.dianping.mobile.mapi.dztgdetail.util.DealVersionUtils;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.VersionUtils;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealStyleStatisticServiceTest {

    private DealStyleStatisticService dealStyleStatisticService = new DealStyleStatisticService();

    @Mock
    private DealGroupPBO result;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private DealBaseReq request;

    @Mock
    private ModuleConfigsModule moduleConfigsModule;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupCategoryDTO categoryDTO;

    @Mock
    private DealStyleConfig dealStyleConfig;

//    private MockedStatic<Cat> catMockedStatic;
    private MockedStatic<Lion> lionMockedStatic;
    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;
    private MockedStatic<VersionUtils> versionUtilsMockedStatic;
    private MockedStatic<MdpContextUtils> mdpContextUtilsMockedStatic;
    private MockedStatic<DealVersionUtils> dealVersionUtilsMockedStatic;
//    private MockedStatic<Math> mathMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = mockStatic(Lion.class);
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
        versionUtilsMockedStatic = mockStatic(VersionUtils.class);
        mdpContextUtilsMockedStatic = mockStatic(MdpContextUtils.class);
        dealVersionUtilsMockedStatic = mockStatic(DealVersionUtils.class);
    }

    @After
    public void tearDown() {
        lionMockedStatic.close();
        lionConfigUtilsMockedStatic.close();
        versionUtilsMockedStatic.close();
        mdpContextUtilsMockedStatic.close();
        dealVersionUtilsMockedStatic.close();
    }

    /**
     * Test case for dealStyleStatistic method with service type.
     */
    @Test
    public void testDealStyleStatisticWithServiceType() {
        // arrange
        when(result.getModuleConfigsModule()).thenReturn(moduleConfigsModule);
        when(moduleConfigsModule.getGeneralInfo()).thenReturn("dealStyle");
        when(moduleConfigsModule.getKey()).thenReturn("testKey");
        when(moduleConfigsModule.getExtraInfo()).thenReturn("extraInfo");
        when(result.getCategoryId()).thenReturn(1);
        when(result.getServiceTypeId()).thenReturn(1L);
        when(result.getMtId()).thenReturn(123);
        when(result.getDpId()).thenReturn(456);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.getVersion()).thenReturn("11.0.0");


        when(envCtx.isMt()).thenReturn(true);
        when(request.getMrnversion()).thenReturn("1.0.0");


        lionConfigUtilsMockedStatic.when(LionConfigUtils::getDealStyleConfig).thenReturn(dealStyleConfig);
        lionMockedStatic.when(() -> Lion.getList(anyString(), anyString(), eq(Integer.class))).thenReturn(Arrays.asList(1, 2, 3));
        lionMockedStatic.when(() -> Lion.getInt(anyString(), anyString(), anyInt())).thenReturn(10);
        String config = "{\"batch1\": [\"1\", \"2\"]}";
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getMigrationConfig).thenReturn(config);
        mdpContextUtilsMockedStatic.when(MdpContextUtils::getAppKey).thenReturn("testApp");
//        mathMockedStatic.when(Math::random).thenReturn(0.005);

        when(dealStyleConfig.isLogAll()).thenReturn(false);
        when(dealStyleConfig.getMtAppVersion()).thenReturn("10.0.0");
        when(dealStyleConfig.getMtMrnVersion()).thenReturn("0.5.0");
        when(dealStyleConfig.getDealStyleExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getCardStyleExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getExtraInfoExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getClientExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getEventName()).thenReturn("TestEvent");

        versionUtilsMockedStatic.when(() -> VersionUtils.isGreatEqualThan(anyString(), anyString())).thenReturn(true);

        // act
        dealStyleStatisticService.dealStyleStatistic(request, envCtx, result, true, true);

        // assert
        verify(result, atLeastOnce()).getModuleConfigsModule();
    }

    /**
     * Test case for dealStyleStatistic method without service type.
     */
    @Test
    public void testDealStyleStatisticWithoutServiceType() {
        // arrange
        when(result.getModuleConfigsModule()).thenReturn(moduleConfigsModule);
        when(moduleConfigsModule.getGeneralInfo()).thenReturn("dealStyle");
        when(moduleConfigsModule.getKey()).thenReturn("testKey");
        when(moduleConfigsModule.getExtraInfo()).thenReturn("");
        when(result.getCategoryId()).thenReturn(1);
        when(result.getMtId()).thenReturn(123);
        when(result.getDpId()).thenReturn(456);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.getVersion()).thenReturn("11.0.0");

        when(envCtx.isMt()).thenReturn(true);
        when(request.getMrnversion()).thenReturn("1.0.0");
        String config = "{\"batch1\": [\"1\", \"2\"]}";
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getMigrationConfig).thenReturn(config);

        lionConfigUtilsMockedStatic.when(LionConfigUtils::getDealStyleConfig).thenReturn(dealStyleConfig);
        when(dealStyleConfig.isLogAll()).thenReturn(false);
        versionUtilsMockedStatic.when(() -> VersionUtils.isGreatEqualThan(anyString(), anyString())).thenReturn(false);

        // act
        dealStyleStatisticService.dealStyleStatistic(request, envCtx, result, false, true);

        // assert
        verify(result, atLeastOnce()).getModuleConfigsModule();
    }

    /**
     * Test case for dealStyleStatistic method with null ModuleConfigsModule.
     */
    @Test
    public void testDealStyleStatisticWithNullModuleConfigsModule() {
        // arrange
        when(result.getModuleConfigsModule()).thenReturn(null);

        String config = "{\"batch1\": [\"1\", \"2\"]}";
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getMigrationConfig).thenReturn(config);

        lionMockedStatic.when(() -> Lion.getList(anyString(), anyString(), eq(Integer.class))).thenReturn(Collections.emptyList());
        mdpContextUtilsMockedStatic.when(MdpContextUtils::getAppKey).thenReturn("testApp");

        // act
        dealStyleStatisticService.dealStyleStatistic(request, envCtx, result, true, true);

        // assert
        verify(result, atLeastOnce()).getModuleConfigsModule();
    }

    /**
     * Test case for dealStyleStatistic method with exception.
     */
    @Test
    public void testDealStyleStatisticWithException() {
        // arrange
        when(result.getModuleConfigsModule()).thenThrow(new RuntimeException("Test exception"));
        when(result.getMtId()).thenReturn(123);
        when(result.getDpId()).thenReturn(456);
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.getVersion()).thenReturn("11.0.0");
        when(request.getMrnversion()).thenReturn("1.0.0");
        String config = "{\"batch1\": [\"1\", \"2\"]}";
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getMigrationConfig).thenReturn(config);

        // act
        dealStyleStatisticService.dealStyleStatistic(request, envCtx, result, true, true);

        // assert - should not throw exception
        verify(result, atLeastOnce()).getModuleConfigsModule();
    }

    /**
     * Test case for getModuleDetailConfig method - MT platform.
     */
    @Test
    public void testGetModuleDetailConfigMT() {
        // arrange
        when(envCtx.isDp()).thenReturn(false);
        when(result.getModuleConfigsModule()).thenReturn(moduleConfigsModule);

        ModuleConfigDo config1 = mock(ModuleConfigDo.class);
        ModuleConfigDo config2 = mock(ModuleConfigDo.class);
        when(config1.getKey()).thenReturn("other_key");
        when(config2.getKey()).thenReturn("dealdetail_gc_packagedetail");
        when(config2.getValue()).thenReturn("test_value");

        when(moduleConfigsModule.getModuleConfigs()).thenReturn(Arrays.asList(config1, config2));

        // act
        String result = DealStyleStatisticService.getModuleDetailConfig(envCtx, this.result, false);

        // assert
        Assert.assertEquals("test_value", result);
    }

    /**
     * Test case for getModuleDetailConfig method - MT platform.
     */
    @Test
    public void testGetModuleDetailConfigMTWithNewCPV() {
        // act
        String result = DealStyleStatisticService.getModuleDetailConfig(envCtx, this.result, true);

        // assert
        Assert.assertEquals("new_cpv_module", result);
    }



    /**
     * Test case for hitEventLog method with logAll true.
     */
    @Test
    public void testHitEventLogWithLogAll() {
        // arrange
        when(dealStyleConfig.isLogAll()).thenReturn(true);

        // act
        boolean result = dealStyleStatisticService.hitEventLog(true, "client", "11.0.0", "1.0.0",
                "dealStyle", "cardStyle", "extraInfo", dealStyleConfig);

        // assert
        Assert.assertTrue(result);
    }

    /**
     * Test case for hitEventLog method with null config.
     */
    @Test
    public void testHitEventLogWithNullConfig() {
        // act
        boolean result = dealStyleStatisticService.hitEventLog(true, "client", "11.0.0", "1.0.0",
                "dealStyle", "cardStyle", "extraInfo", null);

        // assert
        Assert.assertFalse(result);
    }

    /**
     * Test case for hitEventLog method with version validation.
     */
    @Test
    public void testHitEventLogWithVersionValidation() {
        // arrange
        when(dealStyleConfig.isLogAll()).thenReturn(false);
        when(dealStyleConfig.getMtAppVersion()).thenReturn("10.0.0");
        when(dealStyleConfig.getMtMrnVersion()).thenReturn("0.5.0");
        when(dealStyleConfig.getDealStyleExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getCardStyleExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getExtraInfoExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getClientExclude()).thenReturn(Collections.emptyList());

        versionUtilsMockedStatic.when(() -> VersionUtils.isGreatEqualThan("11.0.0", "10.0.0")).thenReturn(true);
        versionUtilsMockedStatic.when(() -> VersionUtils.isGreatEqualThan("1.0.0", "0.5.0")).thenReturn(true);

        // act
        boolean result = dealStyleStatisticService.hitEventLog(true, "client", "11.0.0", "1.0.0",
                "dealStyle", "cardStyle", "extraInfo", dealStyleConfig);

        // assert
        Assert.assertTrue(result);
    }

    /**
     * Test case for hitEventLog method with DP version validation.
     */
    @Test
    public void testHitEventLogWithDPVersionValidation() {
        // arrange
        when(dealStyleConfig.isLogAll()).thenReturn(false);
        when(dealStyleConfig.getDpAppVersion()).thenReturn("10.0.0");
        when(dealStyleConfig.getDpMrnVersion()).thenReturn("0.5.0");
        when(dealStyleConfig.getDealStyleExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getCardStyleExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getExtraInfoExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getClientExclude()).thenReturn(Collections.emptyList());

        versionUtilsMockedStatic.when(() -> VersionUtils.isGreatEqualThan("11.0.0", "10.0.0")).thenReturn(true);
        versionUtilsMockedStatic.when(() -> VersionUtils.isGreatEqualThan("1.0.0", "0.5.0")).thenReturn(true);

        // act
        boolean result = dealStyleStatisticService.hitEventLog(false, "client", "11.0.0", "1.0.0",
                "dealStyle", "cardStyle", "extraInfo", dealStyleConfig);

        // assert
        Assert.assertTrue(result);
    }

    /**
     * Test case for hitEventLog method with failed version validation.
     */
    @Test
    public void testHitEventLogWithFailedVersionValidation() {
        // arrange
        when(dealStyleConfig.isLogAll()).thenReturn(false);
        when(dealStyleConfig.getMtAppVersion()).thenReturn("12.0.0");
        when(dealStyleConfig.getMtMrnVersion()).thenReturn("2.0.0");
        when(dealStyleConfig.getDealStyleExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getCardStyleExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getExtraInfoExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getClientExclude()).thenReturn(Collections.emptyList());

        versionUtilsMockedStatic.when(() -> VersionUtils.isGreatEqualThan("11.0.0", "12.0.0")).thenReturn(false);
        versionUtilsMockedStatic.when(() -> VersionUtils.isGreatEqualThan("1.0.0", "2.0.0")).thenReturn(false);

        // act
        boolean result = dealStyleStatisticService.hitEventLog(true, "client", "11.0.0", "1.0.0",
                "dealStyle", "cardStyle", "extraInfo", dealStyleConfig);

        // assert
        Assert.assertFalse(result);
    }

    /**
     * Test case for hitEventLog method with exclude lists.
     */
    @Test
    public void testHitEventLogWithExcludeLists() {
        // arrange
        when(dealStyleConfig.isLogAll()).thenReturn(false);
        when(dealStyleConfig.getMtAppVersion()).thenReturn("");
        when(dealStyleConfig.getMtMrnVersion()).thenReturn("");
        when(dealStyleConfig.getDealStyleExclude()).thenReturn(Arrays.asList("excludedStyle"));
        when(dealStyleConfig.getCardStyleExclude()).thenReturn(Arrays.asList("excludedCardStyle"));
        when(dealStyleConfig.getExtraInfoExclude()).thenReturn(Arrays.asList("excludedExtraInfo"));
        when(dealStyleConfig.getClientExclude()).thenReturn(Arrays.asList("excludedClient"));

        // act - dealStyle excluded
        boolean result1 = dealStyleStatisticService.hitEventLog(true, "client", "11.0.0", "1.0.0",
                "excludedStyle", "cardStyle", "extraInfo", dealStyleConfig);

        // act - cardStyle excluded
        boolean result2 = dealStyleStatisticService.hitEventLog(true, "client", "11.0.0", "1.0.0",
                "dealStyle", "excludedCardStyle", "extraInfo", dealStyleConfig);

        // act - extraInfo excluded
        boolean result3 = dealStyleStatisticService.hitEventLog(true, "client", "11.0.0", "1.0.0",
                "dealStyle", "cardStyle", "excludedExtraInfo", dealStyleConfig);

        // act - client excluded
        boolean result4 = dealStyleStatisticService.hitEventLog(true, "excludedClient", "11.0.0", "1.0.0",
                "dealStyle", "cardStyle", "extraInfo", dealStyleConfig);

        // assert
        Assert.assertFalse(result1);
        Assert.assertFalse(result2);
        Assert.assertFalse(result3);
        Assert.assertFalse(result4);
    }

    /**
     * Test case for hitEventLog method with blank version configurations.
     */
    @Test
    public void testHitEventLogWithBlankVersionConfigs() {
        // arrange
        when(dealStyleConfig.isLogAll()).thenReturn(false);
        when(dealStyleConfig.getMtAppVersion()).thenReturn("   "); // blank
        when(dealStyleConfig.getMtMrnVersion()).thenReturn(null); // null
        when(dealStyleConfig.getDealStyleExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getCardStyleExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getExtraInfoExclude()).thenReturn(Collections.emptyList());
        when(dealStyleConfig.getClientExclude()).thenReturn(Collections.emptyList());

        // act
        boolean result = dealStyleStatisticService.hitEventLog(true, "client", "11.0.0", "1.0.0",
                "dealStyle", "cardStyle", "extraInfo", dealStyleConfig);

        // assert
        Assert.assertTrue(result); // should pass because blank/null configs are treated as no restriction
    }

    /**
     * Test case for dealDetailStatistic method with null ModuleConfigsModule.
     */
    @Test
    public void testDealDetailStatisticWithNullModuleConfigsModule() {
        // arrange
        when(result.getModuleConfigsModule()).thenReturn(null);

        // act
        dealStyleStatisticService.dealDetailStatistic(request, envCtx, result, true, true);

        // assert
        verify(result, times(1)).getModuleConfigsModule();
    }

    /**
     * Test case for dealDetailStatistic method with hitEventLog true.
     */
    @Test
    public void testDealDetailStatisticWithHitEventLogTrue() {
        // arrange
        when(result.getModuleConfigsModule()).thenReturn(moduleConfigsModule);
        when(moduleConfigsModule.getKey()).thenReturn("dealStyle");
        when(moduleConfigsModule.getGeneralInfo()).thenReturn("cardStyle");
        when(moduleConfigsModule.getExtraInfo()).thenReturn("extraInfo");
        when(result.getCategoryId()).thenReturn(1);
        when(result.getServiceTypeId()).thenReturn(1L);
        when(result.getMtId()).thenReturn(123);
        when(result.getDpId()).thenReturn(456);
        when(envCtx.getVersion()).thenReturn("11.0.0");
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.isMt()).thenReturn(true);
        when(request.getMrnversion()).thenReturn("1.0.0");
        String config = "{\"batch1\": [\"1\", \"2\"]}";
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getMigrationConfig).thenReturn(config);

        lionConfigUtilsMockedStatic.when(LionConfigUtils::getDealStyleConfig).thenReturn(dealStyleConfig);
        when(dealStyleConfig.isLogAll()).thenReturn(true);
        when(dealStyleConfig.getEventName()).thenReturn("CustomEvent");

        // act
        dealStyleStatisticService.dealDetailStatistic(request, envCtx, result, true, true);

        // assert
        verify(result, atLeastOnce()).getModuleConfigsModule();
    }

    /**
     * Test case for logVoucher method with valid voucher.
     */
    @Test
    public void testLogVoucherWithValidVoucher() {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.judgeMainApp()).thenReturn(true);

        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(1L);

        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getName()).thenReturn("sys_deal_universal_type");
        when(attrDTO.getValue()).thenReturn(Arrays.asList("2"));
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));


        



        lionMockedStatic.when(() -> Lion.getInt(anyString(), anyString(), anyInt())).thenReturn(10);
        mdpContextUtilsMockedStatic.when(MdpContextUtils::getAppKey).thenReturn("testApp");

        // act
        dealStyleStatisticService.logMigration(dealCtx, result);

    }

    /**
     * Test case for logVoucher method with non-main app.
     */
    @Test
    public void testLogVoucherWithNonMainApp() {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.judgeMainApp()).thenReturn(false);

        // act
        dealStyleStatisticService.logMigration(dealCtx, result);

        // assert - should return early without logging
        verify(dealCtx, times(2)).getEnvCtx();
    }

    /**
     * Test case for logVoucher method with non-voucher.
     */
    @Test
    public void testLogVoucherWithNonVoucher() {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.judgeMainApp()).thenReturn(true);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(1L);

        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getName()).thenReturn("sys_deal_universal_type");
        when(attrDTO.getValue()).thenReturn(Arrays.asList("1")); // not voucher value
        when(dealGroupDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));

        // act
        dealStyleStatisticService.logMigration(dealCtx, result);

        // assert - should return early without logging voucher metric
        verify(dealCtx, atLeastOnce()).getDealGroupDTO();
    }

    /**
     * Test case for logVoucher method with exception.
     */
    @Test
    public void testLogVoucherWithException() {
        // arrange
        when(dealCtx.getEnvCtx()).thenThrow(new RuntimeException("Test exception"));

        // act
        dealStyleStatisticService.logMigration(dealCtx, result);

        // assert - should not throw exception
        verify(dealCtx, times(1)).getEnvCtx();
    }

    /**
     * Test case for logVoucher method with null dealGroupDTO.
     */
    @Test
    public void testLogVoucherWithNullDealGroupDTO() {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.judgeMainApp()).thenReturn(true);
        when(dealCtx.getDealGroupDTO()).thenReturn(null);

        // act
        dealStyleStatisticService.logMigration(dealCtx, result);

        // assert
        verify(dealCtx, atLeastOnce()).getDealGroupDTO();
    }

    /**
     * Test case for logVoucher method with null category.
     */
    @Test
    public void testLogVoucherWithNullCategory() {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.judgeMainApp()).thenReturn(true);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);

        // act
        dealStyleStatisticService.logMigration(dealCtx, result);

        // assert
        verify(dealCtx, atLeastOnce()).getDealGroupDTO();
    }

    /**
     * Test case for logVoucher method with empty attrs.
     */
    @Test
    public void testLogVoucherWithEmptyAttrs() {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.judgeMainApp()).thenReturn(true);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(1L);
        when(dealGroupDTO.getAttrs()).thenReturn(Collections.emptyList());

        // act
        dealStyleStatisticService.logMigration(dealCtx, result);

        // assert
        verify(dealCtx, atLeastOnce()).getDealGroupDTO();
    }

    /**
     * Test case for dealDetailStatistic with empty dealStyle and logSwitch true.
     */
    @Test
    public void testDealDetailStatisticWithEmptyDealStyleAndLogSwitch() {
        // arrange
        when(result.getModuleConfigsModule()).thenReturn(moduleConfigsModule);
        when(moduleConfigsModule.getKey()).thenReturn(""); // empty dealStyle
        when(moduleConfigsModule.getGeneralInfo()).thenReturn("cardStyle");
        when(moduleConfigsModule.getExtraInfo()).thenReturn("extraInfo");
        when(result.getCategoryId()).thenReturn(1);
        when(result.getServiceTypeId()).thenReturn(1L);
        when(result.getMtId()).thenReturn(123);
        when(result.getDpId()).thenReturn(456);
        when(envCtx.getVersion()).thenReturn("11.0.0");
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.isMt()).thenReturn(true);
        when(request.getMrnversion()).thenReturn("1.0.0");

        lionConfigUtilsMockedStatic.when(LionConfigUtils::getDealStyleConfig).thenReturn(dealStyleConfig);
        when(dealStyleConfig.isLogAll()).thenReturn(false);

        // act
        dealStyleStatisticService.dealDetailStatistic(request, envCtx, result, true, true);

        // assert
        verify(result, atLeastOnce()).getModuleConfigsModule();
    }

    /**
     * Test case for dealDetailStatistic with null dealStyle config.
     */
    @Test
    public void testDealDetailStatisticWithNullDealStyleConfig() {
        // arrange
        when(result.getModuleConfigsModule()).thenReturn(moduleConfigsModule);
        when(moduleConfigsModule.getKey()).thenReturn("dealStyle");
        when(moduleConfigsModule.getGeneralInfo()).thenReturn("cardStyle");
        when(moduleConfigsModule.getExtraInfo()).thenReturn("extraInfo");
        when(result.getCategoryId()).thenReturn(1);
        when(result.getServiceTypeId()).thenReturn(1L);


        when(envCtx.getVersion()).thenReturn("11.0.0");
        when(envCtx.getDztgClientTypeEnum()).thenReturn(DztgClientTypeEnum.MEITUAN_APP);
        when(envCtx.isMt()).thenReturn(true);
        when(request.getMrnversion()).thenReturn("1.0.0");

        lionConfigUtilsMockedStatic.when(LionConfigUtils::getDealStyleConfig).thenReturn(null);

        // act
        dealStyleStatisticService.dealDetailStatistic(request, envCtx, result, true, true);

        // assert
        verify(result, atLeastOnce()).getModuleConfigsModule();
    }

    /**
     * Test case for logMigration method with valid context and new CPV style.
     */
    @Test
    public void testLogMigrationWithValidContextNewCpv() {
        // arrange
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealCtx.getDealBaseReq()).thenReturn(request);

        when(request.getDealgroupid()).thenReturn(789);


        when(envCtx.judgeMainApp()).thenReturn(true);
        when(envCtx.isDp()).thenReturn(false);
        when(result.getCategoryId()).thenReturn(1);
        when(result.getModuleConfigsModule()).thenReturn(moduleConfigsModule);
        when(moduleConfigsModule.getKey()).thenReturn("testKey");
        when(moduleConfigsModule.getExtraInfo()).thenReturn("extraInfo");

        // Mock DealVersionUtils.isOldMetaVersionForLog to return false (isNewCpv = true)
        dealVersionUtilsMockedStatic.when(() -> DealVersionUtils.isOldMetaVersionForLog(eq(dealGroupDTO), anyString())).thenReturn(false);

        // Mock migration config
        String config = "{\"batch1\": [\"1\", \"2\"]}";
        lionConfigUtilsMockedStatic.when(LionConfigUtils::getMigrationConfig).thenReturn(config);

        // act
        dealStyleStatisticService.logMigration(dealCtx, result);

        // assert
        verify(dealCtx, atLeastOnce()).getEnvCtx();
        verify(dealCtx, atLeastOnce()).getDealGroupDTO();
        dealVersionUtilsMockedStatic.verify(() -> DealVersionUtils.isOldMetaVersionForLog(any(DealGroupDTO.class), anyString()), times(1));
    }

}