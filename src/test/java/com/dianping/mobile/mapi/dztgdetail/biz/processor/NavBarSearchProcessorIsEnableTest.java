package com.dianping.mobile.mapi.dztgdetail.biz.processor;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.AbsDealProcessor;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test cases for NavBarSearchProcessor.isEnable() method
 */
@RunWith(MockitoJUnitRunner.class)
public class NavBarSearchProcessorIsEnableTest {

    @Mock
    private DealCtx dealCtx;

    @Mock
    private EnvCtx envCtx;

    @InjectMocks
    private NavBarSearchProcessor processor;

    /**
     * Test case: Both conditions are true
     * Expected: Should return true
     */
    @Test
    public void testIsEnable_WhenMtAndMainApp_ShouldReturnTrue() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.judgeMainApp()).thenReturn(true);
        // act
        boolean result = processor.isEnable(dealCtx);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: isMt() is false but judgeMainApp() is true
     * Expected: Should return false
     */
    @Test
    public void testIsEnable_WhenNotMtButMainApp_ShouldReturnFalse() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(false);
        // act
        boolean result = processor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: isMt() is true but judgeMainApp() is false
     * Expected: Should return false
     */
    @Test
    public void testIsEnable_WhenMtButNotMainApp_ShouldReturnFalse() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(true);
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
        when(envCtx.judgeMainApp()).thenReturn(false);
        // act
        boolean result = processor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Both conditions are false
     * Expected: Should return false
     */
    @Test
    public void testIsEnable_WhenNotMtAndNotMainApp_ShouldReturnFalse() throws Throwable {
        // arrange
        when(dealCtx.isMt()).thenReturn(false);
        // act
        boolean result = processor.isEnable(dealCtx);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: DealCtx is null
     * Expected: Should throw NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testIsEnable_WhenDealCtxIsNull_ShouldThrowException() throws Throwable {
        // act
        processor.isEnable(null);
    }
}
