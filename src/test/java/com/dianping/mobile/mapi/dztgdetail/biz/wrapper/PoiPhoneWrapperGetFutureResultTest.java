package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.generic.entrance.poiphone.dto.PhoneDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class PoiPhoneWrapperGetFutureResultTest {

    private PoiPhoneWrapper poiPhoneWrapper;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private Future<List<PhoneDTO>> poiPhoneFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        poiPhoneWrapper = new PoiPhoneWrapper();
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
    }

    /**
     * Test case for when the poiPhoneFuture is null.
     */
    @Test
    public void testGetFutureResultNullFuture() throws Throwable {
        // arrange
        when(futureCtx.getPoiPhoneFuture()).thenReturn(null);
        // act
        List<PhoneDTO> result = poiPhoneWrapper.getFutureResult(ctx);
        // assert
        assertNull(result);
        verify(futureCtx).getPoiPhoneFuture();
    }

    /**
     * Test case for when the poiPhoneFuture is not null and returns a valid list of PhoneDTO.
     */
    @Test
    public void testGetFutureResultValidFuture() throws Throwable {
        // arrange
        List<PhoneDTO> expectedResult = Collections.singletonList(new PhoneDTO());
        when(futureCtx.getPoiPhoneFuture()).thenReturn(poiPhoneFuture);
        when(poiPhoneFuture.get()).thenReturn(expectedResult);
        // act
        List<PhoneDTO> result = poiPhoneWrapper.getFutureResult(ctx);
        // assert
        assertEquals(expectedResult, result);
        verify(futureCtx).getPoiPhoneFuture();
        verify(poiPhoneFuture).get();
    }

    /**
     * Test case for when the poiPhoneFuture throws an exception.
     */
    @Test
    public void testGetFutureResultException() throws Throwable {
        // arrange
        when(futureCtx.getPoiPhoneFuture()).thenReturn(poiPhoneFuture);
        when(poiPhoneFuture.get()).thenThrow(new RuntimeException("Test Exception"));
        // act
        List<PhoneDTO> result = poiPhoneWrapper.getFutureResult(ctx);
        // assert
        assertNull(result);
        verify(futureCtx).getPoiPhoneFuture();
        verify(poiPhoneFuture).get();
    }

    @Mock
    private DealCtx ctx;
}
