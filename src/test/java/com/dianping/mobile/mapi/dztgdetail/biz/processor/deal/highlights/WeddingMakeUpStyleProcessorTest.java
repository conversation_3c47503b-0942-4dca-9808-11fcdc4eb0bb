package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.dianping.deal.attribute.dto.AttributeDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import com.dianping.tuangou.services.dal.entity.Deal;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WeddingMakeUpStyleProcessorTest {
    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealCtx ctx;


    @InjectMocks
    private WeddingMakeUpStyleProcessor processor;

    private List<AttributeDTO> attrs;


    @Before
    public void setUp(){
        attrs = buildAttrs();
        when(ctx.getAttrs()).thenReturn(attrs);
    }
    private List<AttributeDTO> buildAttrs(){
        List <AttributeDTO> attrs = Lists.newArrayList();
        attrs.add(buildAttr("artistLevel", "初级化妆师"));
        return attrs;

    }
    private AttributeDTO buildAttr(String name, String value){
        AttributeDTO attrDTO = new AttributeDTO();
        attrDTO.setName(name);
        attrDTO.setValue(Lists.newArrayList(value));
        return attrDTO;
    }
    /**
     * 测试 getHighlightsAttrs 方法在 DealCtx 对象及其属性完整，且服务类型为新娘妆时的场景
     */
    @Test
    public void testGetHighlightsAttrsBridalMakeups() throws Throwable {
        // arrange
        String jsonString = "{\"dpDealGroupId\":1033149958,\"mtDealGroupId\":1033149958,\"basic\":{\"categoryId\":908,\"title\":\"新娘妆团购详改版\",\"brandName\":\"彩妆造型测试门店\",\"titleDesc\":\"仅售888元，价值1288元新娘妆团购详改版！\",\"beginSaleDate\":\"2024-11-05 16:16:21\",\"endSaleDate\":\"2025-11-05 16:16:06\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"http://p0.meituan.net/dpmerchantpic/b8eefbc4d316678b5ea8b68713492f6535321.jpg\",\"allPicPaths\":\"http://p0.meituan.net/dpmerchantpic/b8eefbc4d316678b5ea8b68713492f6535321.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/8a9ca247e6f5c6b233bee3a843c7f67a118877.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/832c0f2300db11802f45e431f0b8d5c885660.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/f1edd15535faecaea8d2cbeebc6909ea170981.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/e224ef9f7f85ae94036262c00c0c7d32150450.jpg\"},\"category\":{\"categoryId\":908,\"serviceType\":\"新娘妆\",\"serviceTypeId\":138034},\"serviceProject\":{\"structType\":\"html\"},\"channel\":{\"channelId\":9,\"channelEn\":\"wedding\",\"channelCn\":\"结婚\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"service_type_leaf_id\",\"value\":[\"138034\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"artistLevel\",\"value\":[\"高级化妆师\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"product_name\",\"value\":[\"新娘妆团购详改版\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"新娘妆\"],\"source\":0},{\"name\":\"tag_unifyProduct\",\"value\":[\"0\"],\"source\":0},{\"name\":\"otherService\",\"value\":[\"含修眉\",\"含配饰\",\"含双眼皮贴\",\"含假睫毛\",\"含一次性补妆包\",\"自营服务\",\"自定义2\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"是\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"90\",\"9009\"],\"source\":0}],\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptDateType\":1,\"receiptValidDays\":180,\"receiptBeginDate\":\"2024-11-08 10:43:32\",\"receiptEndDate\":\"2025-05-07 23:59:59\",\"showText\":\"购买后180天内有效\"}},\"refundRule\":{\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"dpDisplayShopIds\":[607129255],\"mtDisplayShopIds\":[607129255]},\"tags\":[{\"id\":100027417,\"tagName\":\"新娘妆\"}],\"customer\":{\"originCustomerId\":292830,\"platformCustomerId\":1004183978},\"regions\":[{\"dpCityId\":1,\"mtCityId\":10},{\"dpCityId\":10,\"mtCityId\":40}],\"deals\":[{\"dealId\":460719237,\"basic\":{\"title\":\"新娘妆团购详改版\",\"originTitle\":\"新娘妆团购详改版\",\"status\":1},\"price\":{\"salePrice\":\"888.00\",\"marketPrice\":\"1288.00\",\"version\":5174222983},\"stock\":{\"dpSales\":0,\"dpTotal\":2000000,\"dpRemain\":2000000,\"mtSales\":0,\"mtTotal\":2000000,\"mtRemain\":2000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":0,\"sharedRemain\":0,\"isSharedSoldOut\":false},\"attrs\":[{\"name\":\"sku_receipt_type\",\"value\":[\"1\"],\"source\":0}]}],\"price\":{\"salePrice\":\"888.00\",\"marketPrice\":\"1288.00\",\"version\":5174222983},\"stock\":{\"dpSales\":0,\"dpTotal\":2000000,\"dpRemain\":2000000,\"mtSales\":0,\"mtTotal\":2000000,\"mtRemain\":2000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"detail\":{\"dealGroupPics\":\"http://p0.meituan.net/dpmerchantpic/b8eefbc4d316678b5ea8b68713492f6535321.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/8a9ca247e6f5c6b233bee3a843c7f67a118877.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/832c0f2300db11802f45e431f0b8d5c885660.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/f1edd15535faecaea8d2cbeebc6909ea170981.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/e224ef9f7f85ae94036262c00c0c7d32150450.jpg\",\"images\":[\"https://p0.meituan.net/dpmerchantpic/b8eefbc4d316678b5ea8b68713492f6535321.jpg%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"https://qcloud.dpfile.com/pc/DQQcD5D0P3spmJnHTR9G5Or7JZ7RrLvUMqSJpP3pBrxPK8nJa0fptxFAFT7z9RYlDkGHbckSnjHySsp3uDLV9w.jpg\",\"https://qcloud.dpfile.com/pc/0WeJTn3zzKhe4Bdq0v9vy1QUs3f8Osa9AzVYPvhF-T5D7mhj8BWf6FOpPP9S9FIMTxq4kKRPxu2Gq1lq24bRpA.jpg\",\"https://qcloud.dpfile.com/pc/4mOPk6K4rqpj8is0XFJyizKGs8xgq1Fmd4F3JoMzzR8ih2INt9E70mt2u2CVpnjNDkGHbckSnjHySsp3uDLV9w.jpg\",\"https://qcloud.dpfile.com/pc/O-7V08dMvY7-gNR3WrMsdXXsOyg_4dnBzPWccsyBLYKM6RM-VEaGVV6cTyi83t5CDkGHbckSnjHySsp3uDLV9w.jpg\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"title\":\"产品介绍\",\"content\":\"\\u003cdiv\\u003e星期个说明\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p0.meituan.net/dpmerchantpic/b8eefbc4d316678b5ea8b68713492f6535321.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p1.meituan.net/dpmerchantimage/8309b4d0-1b8b-48b1-b8b3-c5f9970dfbe4.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p1.meituan.net/dpmerchantimage/084e2513-a683-4268-9084-3472769683de.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p1.meituan.net/dpmerchantimage/a8cd8d74-8b6a-4cfe-a5fe-332826ad4cdf.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p0.meituan.net/dpmerchantimage/892958ce-42de-4a5d-87c8-fbe679091e9a.png%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p0.meituan.net/dpmerchantimage/1716b7ec-8f53-4e3f-b4c4-0283e0f41d95.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p0.meituan.net/dpmerchantimage/7ee7e1f4-0375-47cb-9ff2-c7fa9bacdaef.png%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\n\",\"type\":5,\"blockId\":0},{\"title\":\"团购详情\",\"content\":\"\\u003cdiv\\u003e补充说明\\u003c/div\\u003e\\n\\n\",\"type\":1,\"blockId\":0},{\"title\":\"购买须知\",\"content\":\"\\u003cdiv class\\u003d\\\"detail-box\\\"\\u003e\\n    \\u003cdiv class\\u003d\\\"purchase-notes\\\"\\u003e\\n\\t\\t        \\u003cdl\\u003e\\n            \\u003cdt\\u003e有效期\\u003c/dt\\u003e\\n            \\u003cdd\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e\\n    购买后180天内有效\\n        \\u003c/p\\u003e\\n            \\u003c/dd\\u003e\\n        \\u003c/dl\\u003e\\n        \\u003cdl\\u003e\\n            \\u003cdt\\u003e预约信息\\u003c/dt\\u003e\\n            \\u003cdd\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e请您提前2小时预约\\u003c/p\\u003e\\n            \\u003c/dd\\u003e\\n        \\u003c/dl\\u003e\\n        \\u003cdl\\u003e\\n            \\u003cdt\\u003e规则提醒\\u003c/dt\\u003e\\n            \\u003cdd\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e不再与其他优惠同享\\u003c/p\\u003e\\n            \\u003c/dd\\u003e\\n        \\u003c/dl\\u003e\\n        \\u003cdl\\u003e\\n            \\u003cdt\\u003e温馨提示\\u003c/dt\\u003e\\n            \\u003cdd\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e如需团购券发票，请您在消费时向商户咨询\\u003c/p\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\\u003c/p\\u003e\\n            \\u003c/dd\\u003e\\n        \\u003c/dl\\u003e\\n    \\u003c/div\\u003e\\n\\u003c/div\\u003e\\n\",\"type\":2,\"blockId\":0}]},\"saleChannelAggregation\":{\"allSupport\":true,\"supportChannels\":[],\"notSupportChannels\":[]}}";
        dealGroupDTO = JSON.parseObject(jsonString, DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);

        // act
        List<CommonAttrVO> result = processor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    /**
     * 测试 getHighlightsAttrs 方法在 DealCtx 对象及其属性完整，且服务类型为日常生活妆时的场景
     */
    @Test
    public void testGetHighlightsAttrsNormalMakeups() throws Throwable {
        // arrange
        String jsonString = "{\"dpDealGroupId\":1033164176,\"mtDealGroupId\":1033164176,\"basic\":{\"categoryId\":908,\"title\":\"儿童张团详改版\",\"brandName\":\"彩妆造型测试门店\",\"titleDesc\":\"仅售228元，价值398元儿童张团详改版！\",\"beginSaleDate\":\"2024-11-05 16:12:05\",\"endSaleDate\":\"2025-11-05 16:11:50\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/8a9ca247e6f5c6b233bee3a843c7f67a118877.jpg\",\"allPicPaths\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/8a9ca247e6f5c6b233bee3a843c7f67a118877.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/0ffd0a9ecaed5c56edab76c77aeba55d186805.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/5285d044333b173ca9288717ab10bfd9128723.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/a2f5feb396b2748b532fcb4f5cce9c80110979.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/a2f5feb396b2748b532fcb4f5cce9c80110979.jpg\"},\"category\":{\"categoryId\":908,\"serviceType\":\"儿童妆\",\"serviceTypeId\":137027},\"serviceProject\":{\"structType\":\"html\"},\"channel\":{\"channelId\":9,\"channelEn\":\"wedding\",\"channelCn\":\"结婚\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"service_type_leaf_id\",\"value\":[\"137027\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"artistLevel\",\"value\":[\"总监化妆师\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"product_name\",\"value\":[\"儿童张团详改版\"],\"source\":0},{\"name\":\"hairstyle_service\",\"value\":[\"含造型\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"儿童妆\"],\"source\":0},{\"name\":\"tag_unifyProduct\",\"value\":[\"0\"],\"source\":0},{\"name\":\"otherService\",\"value\":[\"面部装饰（贴纸、贴钻等）\",\"发型装饰（丝带、发卡等）\",\"含修眉\",\"含配饰\",\"含双眼皮贴\",\"含假睫毛\",\"含一次性补妆包\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"是\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"90\",\"9009\"],\"source\":0}],\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptDateType\":1,\"receiptValidDays\":90,\"receiptBeginDate\":\"2024-11-08 10:00:01\",\"receiptEndDate\":\"2025-02-06 23:59:59\",\"showText\":\"购买后90天内有效\"}},\"refundRule\":{\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"dpDisplayShopIds\":[607129255],\"mtDisplayShopIds\":[607129255]},\"customer\":{\"originCustomerId\":292830,\"platformCustomerId\":1004183978},\"regions\":[{\"dpCityId\":1,\"mtCityId\":10},{\"dpCityId\":10,\"mtCityId\":40}],\"deals\":[{\"dealId\":460719091,\"basic\":{\"title\":\"儿童张团详改版\",\"originTitle\":\"儿童张团详改版\",\"status\":1},\"price\":{\"salePrice\":\"228.00\",\"marketPrice\":\"398.00\",\"version\":5174222671},\"stock\":{\"dpSales\":0,\"dpTotal\":2000000,\"dpRemain\":2000000,\"mtSales\":0,\"mtTotal\":2000000,\"mtRemain\":2000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":0,\"sharedRemain\":0,\"isSharedSoldOut\":false},\"attrs\":[{\"name\":\"sku_receipt_type\",\"value\":[\"1\"],\"source\":0}]}],\"price\":{\"salePrice\":\"228.00\",\"marketPrice\":\"398.00\",\"version\":5174222671},\"stock\":{\"dpSales\":0,\"dpTotal\":2000000,\"dpRemain\":2000000,\"mtSales\":0,\"mtTotal\":2000000,\"mtRemain\":2000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"detail\":{\"dealGroupPics\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/8a9ca247e6f5c6b233bee3a843c7f67a118877.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/0ffd0a9ecaed5c56edab76c77aeba55d186805.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/5285d044333b173ca9288717ab10bfd9128723.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/a2f5feb396b2748b532fcb4f5cce9c80110979.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/a2f5feb396b2748b532fcb4f5cce9c80110979.jpg\",\"images\":[\"https://qcloud.dpfile.com/pc/DQQcD5D0P3spmJnHTR9G5Or7JZ7RrLvUMqSJpP3pBrxPK8nJa0fptxFAFT7z9RYlDkGHbckSnjHySsp3uDLV9w.jpg\",\"https://qcloud.dpfile.com/pc/YxV7cGAYCKMygmlL2bm-KacqOdfXFrluguQ56r_BGj64A65wP4zxs0iXcU7pOH0oDkGHbckSnjHySsp3uDLV9w.jpg\",\"https://qcloud.dpfile.com/pc/Yri25pvrvOU8jr80PScoE9nsUgqMwpaRkyN2mY4hhOuA3OMnGMMPHffOEUZX5217DkGHbckSnjHySsp3uDLV9w.jpg\",\"https://qcloud.dpfile.com/pc/BfkLXXe9INw9L5d0Dov0On97-xC4NoTSJ0OZIi8TLvGybJ4wNPWUtYwrKGkV48M0DkGHbckSnjHySsp3uDLV9w.jpg\",\"https://qcloud.dpfile.com/pc/BfkLXXe9INw9L5d0Dov0On97-xC4NoTSJ0OZIi8TLvGybJ4wNPWUtYwrKGkV48M0DkGHbckSnjHySsp3uDLV9w.jpg\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"title\":\"产品介绍\",\"content\":\"\\u003cdiv\\u003e图文详情描述大事发生\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://img.meituan.net/meifaa/d65d532199e3b0e19bfb44b28ba82f432849188.png%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p1.meituan.net/dpmerchantimage/8309b4d0-1b8b-48b1-b8b3-c5f9970dfbe4.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p1.meituan.net/dpmerchantimage/084e2513-a683-4268-9084-3472769683de.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p1.meituan.net/dpmerchantimage/5f7f556c-054d-4e0a-9b33-8e38efe52c59.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p0.meituan.net/dpmerchantimage/d18c5637-b721-4a67-84cc-9909d23d4010.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p1.meituan.net/dpmerchantimage/98d1a815-6fbe-451c-b5ba-d247022e10ab.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\n\",\"type\":5,\"blockId\":0},{\"title\":\"团购详情\",\"content\":\"\\u003cdiv\\u003e补充信息描述\\u003c/div\\u003e\\n\\n\",\"type\":1,\"blockId\":0},{\"title\":\"购买须知\",\"content\":\"\\u003cdiv class\\u003d\\\"detail-box\\\"\\u003e\\n    \\u003cdiv class\\u003d\\\"purchase-notes\\\"\\u003e\\n\\t\\t        \\u003cdl\\u003e\\n            \\u003cdt\\u003e有效期\\u003c/dt\\u003e\\n            \\u003cdd\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e\\n    购买后90天内有效\\n        \\u003c/p\\u003e\\n            \\u003c/dd\\u003e\\n        \\u003c/dl\\u003e\\n        \\u003cdl\\u003e\\n            \\u003cdt\\u003e预约信息\\u003c/dt\\u003e\\n            \\u003cdd\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e请您提前1天预约\\u003c/p\\u003e\\n            \\u003c/dd\\u003e\\n        \\u003c/dl\\u003e\\n        \\u003cdl\\u003e\\n            \\u003cdt\\u003e规则提醒\\u003c/dt\\u003e\\n            \\u003cdd\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e可与其他优惠同享\\u003c/p\\u003e\\n            \\u003c/dd\\u003e\\n        \\u003c/dl\\u003e\\n        \\u003cdl\\u003e\\n            \\u003cdt\\u003e温馨提示\\u003c/dt\\u003e\\n            \\u003cdd\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e如需团购券发票，请您在消费时向商户咨询\\u003c/p\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\\u003c/p\\u003e\\n            \\u003c/dd\\u003e\\n        \\u003c/dl\\u003e\\n    \\u003c/div\\u003e\\n\\u003c/div\\u003e\\n\",\"type\":2,\"blockId\":0}]},\"saleChannelAggregation\":{\"allSupport\":true,\"supportChannels\":[],\"notSupportChannels\":[]}}";
        dealGroupDTO = JSON.parseObject(jsonString, DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);


        // act
        List<CommonAttrVO> result = processor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    /**
     * 测试 getHighlightsAttrs 方法在 DealCtx 对象及其属性完整，且服务类型为彩妆兴趣培训时的场景
     */
    @Test
    public void testGetHighlightsAttrsInterestMakeups() throws Throwable {
        // arrange
        String jsonString = "{\"dpDealGroupId\":1033149958,\"mtDealGroupId\":1033149958,\"basic\":{\"categoryId\":908,\"title\":\"新娘妆团购详改版\",\"brandName\":\"彩妆造型测试门店\",\"titleDesc\":\"仅售888元，价值1288元新娘妆团购详改版！\",\"beginSaleDate\":\"2024-11-05 16:16:21\",\"endSaleDate\":\"2025-11-05 16:16:06\",\"status\":1,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"tradeType\":3},\"image\":{\"defaultPicPath\":\"http://p0.meituan.net/dpmerchantpic/b8eefbc4d316678b5ea8b68713492f6535321.jpg\",\"allPicPaths\":\"http://p0.meituan.net/dpmerchantpic/b8eefbc4d316678b5ea8b68713492f6535321.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/8a9ca247e6f5c6b233bee3a843c7f67a118877.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/832c0f2300db11802f45e431f0b8d5c885660.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/f1edd15535faecaea8d2cbeebc6909ea170981.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/e224ef9f7f85ae94036262c00c0c7d32150450.jpg\"},\"category\":{\"categoryId\":908,\"serviceType\":\"新娘妆\",\"serviceTypeId\":138034},\"serviceProject\":{\"structType\":\"html\"},\"channel\":{\"channelId\":9,\"channelEn\":\"wedding\",\"channelCn\":\"结婚\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"attrs\":[{\"name\":\"product_finish_date\",\"value\":[\"1970-01-01 08:00:00\"],\"source\":0},{\"name\":\"calc_holiday_available\",\"value\":[\"1\"],\"source\":0},{\"name\":\"product_business_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"service_type_leaf_id\",\"value\":[\"138034\"],\"source\":0},{\"name\":\"product_channel_id_allowed\",\"value\":[\"0\"],\"source\":0},{\"name\":\"artistLevel\",\"value\":[\"高级化妆师\"],\"source\":0},{\"name\":\"product_can_use_coupon\",\"value\":[\"true\"],\"source\":0},{\"name\":\"product_name\",\"value\":[\"新娘妆团购详改版\"],\"source\":0},{\"name\":\"preSaleTag\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_third_party_verify\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_block_stock\",\"value\":[\"false\"],\"source\":0},{\"name\":\"product_discount_rule_id\",\"value\":[\"0\"],\"source\":0},{\"name\":\"service_type\",\"value\":[\"新娘妆\"],\"source\":0},{\"name\":\"tag_unifyProduct\",\"value\":[\"0\"],\"source\":0},{\"name\":\"otherService\",\"value\":[\"含修眉\",\"含配饰\",\"含双眼皮贴\",\"含假睫毛\",\"含一次性补妆包\",\"自营服务\",\"自定义2\"],\"source\":0},{\"name\":\"reservation_is_needed_or_not\",\"value\":[\"是\"],\"source\":0},{\"name\":\"sys_deal_universal_type\",\"value\":[\"1\"],\"source\":0},{\"name\":\"category\",\"value\":[\"90\",\"9009\"],\"source\":0}],\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"useRule\":{\"receiptEffectiveDate\":{\"receiptDateType\":1,\"receiptValidDays\":180,\"receiptBeginDate\":\"2024-11-08 10:43:32\",\"receiptEndDate\":\"2025-05-07 23:59:59\",\"showText\":\"购买后180天内有效\"}},\"refundRule\":{\"supportRefundType\":1,\"supportOverdueAutoRefund\":true}},\"displayShopInfo\":{\"dpDisplayShopIds\":[607129255],\"mtDisplayShopIds\":[607129255]},\"tags\":[{\"id\":100027417,\"tagName\":\"新娘妆\"}],\"customer\":{\"originCustomerId\":292830,\"platformCustomerId\":1004183978},\"regions\":[{\"dpCityId\":1,\"mtCityId\":10},{\"dpCityId\":10,\"mtCityId\":40}],\"deals\":[{\"dealId\":460719237,\"basic\":{\"title\":\"新娘妆团购详改版\",\"originTitle\":\"新娘妆团购详改版\",\"status\":1},\"price\":{\"salePrice\":\"888.00\",\"marketPrice\":\"1288.00\",\"version\":5174222983},\"stock\":{\"dpSales\":0,\"dpTotal\":2000000,\"dpRemain\":2000000,\"mtSales\":0,\"mtTotal\":2000000,\"mtRemain\":2000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1,\"sharedSales\":0,\"sharedTotal\":0,\"sharedRemain\":0,\"isSharedSoldOut\":false},\"attrs\":[{\"name\":\"sku_receipt_type\",\"value\":[\"1\"],\"source\":0}]}],\"price\":{\"salePrice\":\"888.00\",\"marketPrice\":\"1288.00\",\"version\":5174222983},\"stock\":{\"dpSales\":0,\"dpTotal\":2000000,\"dpRemain\":2000000,\"mtSales\":0,\"mtTotal\":2000000,\"mtRemain\":2000000,\"isDpSoldOut\":false,\"isMtSoldOut\":false,\"status\":1},\"detail\":{\"dealGroupPics\":\"http://p0.meituan.net/dpmerchantpic/b8eefbc4d316678b5ea8b68713492f6535321.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/8a9ca247e6f5c6b233bee3a843c7f67a118877.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/832c0f2300db11802f45e431f0b8d5c885660.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/f1edd15535faecaea8d2cbeebc6909ea170981.jpg|https://p0.inf.test.sankuai.com/dpmerchantpic/e224ef9f7f85ae94036262c00c0c7d32150450.jpg\",\"images\":[\"https://p0.meituan.net/dpmerchantpic/b8eefbc4d316678b5ea8b68713492f6535321.jpg%40450w_280h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"https://qcloud.dpfile.com/pc/DQQcD5D0P3spmJnHTR9G5Or7JZ7RrLvUMqSJpP3pBrxPK8nJa0fptxFAFT7z9RYlDkGHbckSnjHySsp3uDLV9w.jpg\",\"https://qcloud.dpfile.com/pc/0WeJTn3zzKhe4Bdq0v9vy1QUs3f8Osa9AzVYPvhF-T5D7mhj8BWf6FOpPP9S9FIMTxq4kKRPxu2Gq1lq24bRpA.jpg\",\"https://qcloud.dpfile.com/pc/4mOPk6K4rqpj8is0XFJyizKGs8xgq1Fmd4F3JoMzzR8ih2INt9E70mt2u2CVpnjNDkGHbckSnjHySsp3uDLV9w.jpg\",\"https://qcloud.dpfile.com/pc/O-7V08dMvY7-gNR3WrMsdXXsOyg_4dnBzPWccsyBLYKM6RM-VEaGVV6cTyi83t5CDkGHbckSnjHySsp3uDLV9w.jpg\"],\"importantPoint\":\"\",\"templateDetailDTOs\":[{\"title\":\"产品介绍\",\"content\":\"\\u003cdiv\\u003e星期个说明\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p0.meituan.net/dpmerchantpic/b8eefbc4d316678b5ea8b68713492f6535321.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p1.meituan.net/dpmerchantimage/8309b4d0-1b8b-48b1-b8b3-c5f9970dfbe4.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p1.meituan.net/dpmerchantimage/084e2513-a683-4268-9084-3472769683de.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p1.meituan.net/dpmerchantimage/a8cd8d74-8b6a-4cfe-a5fe-332826ad4cdf.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p0.meituan.net/dpmerchantimage/892958ce-42de-4a5d-87c8-fbe679091e9a.png%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p0.meituan.net/dpmerchantimage/1716b7ec-8f53-4e3f-b4c4-0283e0f41d95.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\u003cp class\\u003d\\\"listitem\\\"\\u003e\\u003c/p\\u003e\\n\\u003cp class\\u003d\\\"explain\\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class\\u003d\\\"img\\\"\\u003e\\u003cimg src\\u003d\\\"https://p0.meituan.net/dpmerchantimage/7ee7e1f4-0375-47cb-9ff2-c7fa9bacdaef.png%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\\\" /\\u003e\\u003c/div\\u003e\\n\\n\",\"type\":5,\"blockId\":0},{\"title\":\"团购详情\",\"content\":\"\\u003cdiv\\u003e补充说明\\u003c/div\\u003e\\n\\n\",\"type\":1,\"blockId\":0},{\"title\":\"购买须知\",\"content\":\"\\u003cdiv class\\u003d\\\"detail-box\\\"\\u003e\\n    \\u003cdiv class\\u003d\\\"purchase-notes\\\"\\u003e\\n\\t\\t        \\u003cdl\\u003e\\n            \\u003cdt\\u003e有效期\\u003c/dt\\u003e\\n            \\u003cdd\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e\\n    购买后180天内有效\\n        \\u003c/p\\u003e\\n            \\u003c/dd\\u003e\\n        \\u003c/dl\\u003e\\n        \\u003cdl\\u003e\\n            \\u003cdt\\u003e预约信息\\u003c/dt\\u003e\\n            \\u003cdd\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e请您提前2小时预约\\u003c/p\\u003e\\n            \\u003c/dd\\u003e\\n        \\u003c/dl\\u003e\\n        \\u003cdl\\u003e\\n            \\u003cdt\\u003e规则提醒\\u003c/dt\\u003e\\n            \\u003cdd\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e不再与其他优惠同享\\u003c/p\\u003e\\n            \\u003c/dd\\u003e\\n        \\u003c/dl\\u003e\\n        \\u003cdl\\u003e\\n            \\u003cdt\\u003e温馨提示\\u003c/dt\\u003e\\n            \\u003cdd\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e如需团购券发票，请您在消费时向商户咨询\\u003c/p\\u003e\\n                \\u003cp class\\u003d\\\"listitem\\\"\\u003e为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！\\u003c/p\\u003e\\n            \\u003c/dd\\u003e\\n        \\u003c/dl\\u003e\\n    \\u003c/div\\u003e\\n\\u003c/div\\u003e\\n\",\"type\":2,\"blockId\":0}]},\"saleChannelAggregation\":{\"allSupport\":true,\"supportChannels\":[],\"notSupportChannels\":[]}}";
        dealGroupDTO = JSON.parseObject(jsonString, DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);

        // act
        List<CommonAttrVO> result = processor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    /**
     * 测试 getHighlightsAttrs 方法在服务类型不匹配任何已定义类型的场景
     */
    @Test
    public void testGetHighlightsAttrsServiceTypeMismatch() throws Throwable {
        // arrange
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        when(category.getServiceType()).thenReturn("未知类型");

        // act
        List<CommonAttrVO> result = processor.getHighlightsAttrs(ctx);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void  testGetHighlightsIdentify(){
        String result = processor.getHighlightsIdentify(ctx);
        assertNull(result);
    }

    @Test
    public  void getHighlightsStyleStruct(){
        try(MockedStatic<GreyUtils> greyUtilsMockedStatic = Mockito.mockStatic(GreyUtils.class);) {
            DealGroupCategoryDTO category = new DealGroupCategoryDTO();
            category.setCategoryId(908L);
            category.setServiceType("彩妆兴趣培训");
            HashMap maps = new HashMap<>();
            maps.put("908.彩妆兴趣培训", "struct");
            when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
            greyUtilsMockedStatic.when(GreyUtils::getHighlightsStyleType).thenReturn(maps);
            when(dealGroupDTO.getCategory()).thenReturn(category);
            String result = processor.getHighlightsStyle(ctx);
            Assert.assertEquals("struct", result);
        }
    }

    @Test
    public  void getHighlightsStyleAbnormal(){
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(908L);
        category.setServiceType("位置服务类型");
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(category);
        String result = processor.getHighlightsStyle(ctx);
        Assert.assertNull(result);
    }

}
