package com.dianping.mobile.mapi.dztgdetail.button.mtlive;

import com.dianping.mobile.mapi.dztgdetail.button.ButtonBuilderChain;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.MtLiveSaleStatusEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtLiveCanNotBuyButtonBuilderTest {

    @InjectMocks
    private MtLiveCanNotBuyButtonBuilder mtLiveCanNotBuyButtonBuilder;

    @Mock
    private ButtonBuilderChain chain;
    @Mock
    private PriceDisplayDTO mtLivePrice;

    private MockedStatic<PriceHelper> priceHelperMocked;

    @Before
    public void setUp() {
        priceHelperMocked = mockStatic(PriceHelper.class);
    }

    @After
    public void tearDown() {
        priceHelperMocked.close();
    }

    /**
     * 测试doBuild方法，当context.isMtLiveMinApp()为true且mtLiveSaleStatusEnum不等于canBuyStatus时，应该设置context为不可购买
     */
    @Test
    public void testDoBuildWhenIsMtLiveMinAppAndSaleStatusNotCanBuy() throws Throwable {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_LIVE_WEIXIN_MINIAPP);
        DealCtx dealCtx = new DealCtx(envCtx);
        // arrange
        dealCtx.setMtLiveSaleStatusEnum(MtLiveSaleStatusEnum.END_OF_SALE);
        priceHelperMocked.when(() -> PriceHelper.getNormalPrice(dealCtx)).thenReturn(mtLivePrice);
        when(mtLivePrice.getPrice()).thenReturn(new BigDecimal("100"));

        // act
        mtLiveCanNotBuyButtonBuilder.doBuild(dealCtx, chain);

        // assert
        assertTrue(dealCtx.isCanNotBuy());
    }

    /**
     * 测试doBuild方法，当context.isMtLiveMinApp()为false时，应该调用chain.build(context)
     */
    @Test
    public void testDoBuildWhenIsNotMtLiveMinApp() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        // act
        mtLiveCanNotBuyButtonBuilder.doBuild(dealCtx, chain);
        // assert
        assertEquals(dealCtx.getBuyBar().getBuyBtns().size(), 0);
    }

    /**
     * 测试doBuild方法，当context.isMtLiveMinApp()为true且mtLiveSaleStatusEnum等于canBuyStatus时，应该调用chain.build(context)
     */
    @Test
    public void testDoBuildWhenIsMtLiveMinAppAndSaleStatusCanBuy() throws Throwable {
        // arrange
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.DIANPING_APP);
        DealCtx dealCtx = new DealCtx(envCtx);
        dealCtx.setMtLiveSaleStatusEnum(MtLiveSaleStatusEnum.SNAP_UP_NOW);
        // act
        mtLiveCanNotBuyButtonBuilder.doBuild(dealCtx, chain);
        // assert
        assertEquals(dealCtx.getBuyBar().getBuyBtns().size(), 0);
    }
}
