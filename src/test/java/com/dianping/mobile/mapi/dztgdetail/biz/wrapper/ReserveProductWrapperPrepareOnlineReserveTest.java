package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.trade.general.reserve.service.ShopAndProductReserveStatusService;
import java.util.concurrent.Future;
import org.junit.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

public class ReserveProductWrapperPrepareOnlineReserveTest {

    @InjectMocks
    private ReserveProductWrapper reserveProductWrapper;

    @Mock
    private ShopAndProductReserveStatusService shopAndProductReserveStatusServiceFuture;

    @Mock
    private Logger logger;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    public Future prepareOnlineReserve(DealCtx ctx) {
        if (ctx == null) {
            return null;
        }
        try {
            int dpGroupId = ctx.getDpId();
            shopAndProductReserveStatusServiceFuture.judgeProductOneOfShopReserve(dpGroupId);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            logger.error("ShopAndProductReserveStatusService.judgeProductOneOfShopReserve ERR", e);
        }
        return null;
    }

    /**
     * Test case where ctx is null.
     * Expected result: The method should return null.
     */
    @Test
    public void testPrepareOnlineReserveCtxIsNull() throws Throwable {
        // arrange
        DealCtx ctx = null;
        // act
        Future<?> result = reserveProductWrapper.prepareOnlineReserve(ctx);
        // assert
        assertNull(result);
    }

}
