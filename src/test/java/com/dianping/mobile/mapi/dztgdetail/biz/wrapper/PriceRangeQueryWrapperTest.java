package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.tpfun.skuoperationapi.price.dto.request.BatchPriceRangeInfoRequest;
import com.sankuai.tpfun.skuoperationapi.price.dto.SubjectDTO;
import com.sankuai.tpfun.skuoperationapi.price.service.PriceRangeQueryService;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.concurrent.Future;
import com.sankuai.tpfun.skuoperationapi.price.dto.enums.pricerange.PriceRangeSceneTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PriceRangeQueryWrapperTest {

    @InjectMocks
    private PriceRangeQueryWrapper priceRangeQueryWrapper;

    @Mock
    private PriceRangeQueryService priceRangeQueryService;

    /**
     * 测试 preQueryPriceRange 方法在正常情况下的行为
     */
    @Test
    public void testPreQueryPriceRangeNormal() throws Throwable {
        // arrange
        Integer dpDealId = 1;
        Long dpShopId = 1L;
        // act
        Future result = priceRangeQueryWrapper.preQueryPriceRange(dpDealId, dpShopId);
        // assert
        ArgumentCaptor<BatchPriceRangeInfoRequest> captor = ArgumentCaptor.forClass(BatchPriceRangeInfoRequest.class);
        verify(priceRangeQueryService, times(1)).batchGetPriceRangeInfo(captor.capture());
        BatchPriceRangeInfoRequest capturedRequest = captor.getValue();
        assertNotNull(capturedRequest);
        assertEquals(1, capturedRequest.getSubjectDTOs().size());
        SubjectDTO subjectDTO = capturedRequest.getSubjectDTOs().get(0);
        assertEquals(dpShopId, subjectDTO.getDpShopId());
        assertEquals(dpDealId.longValue(), subjectDTO.getProductId().longValue());
        assertEquals((Integer) PriceRangeSceneTypeEnum.PRODUCT_SHOP_TYPE.getCode(), capturedRequest.getPriceRangeSceneType());
        assertEquals((Integer) ProductTypeEnum.DEAL.getType(), capturedRequest.getProductType());
    }

    /**
     * 测试 preQueryPriceRange 方法在发生异常时的行为
     */
    @Test
    public void testPreQueryPriceRangeException() throws Throwable {
        // arrange
        Integer dpDealId = 1;
        Long dpShopId = 1L;
        doThrow(new RuntimeException()).when(priceRangeQueryService).batchGetPriceRangeInfo(any(BatchPriceRangeInfoRequest.class));
        // act
        Future result = priceRangeQueryWrapper.preQueryPriceRange(dpDealId, dpShopId);
        // assert
        ArgumentCaptor<BatchPriceRangeInfoRequest> captor = ArgumentCaptor.forClass(BatchPriceRangeInfoRequest.class);
        verify(priceRangeQueryService, times(1)).batchGetPriceRangeInfo(captor.capture());
        BatchPriceRangeInfoRequest capturedRequest = captor.getValue();
        assertNotNull(capturedRequest);
        assertEquals(1, capturedRequest.getSubjectDTOs().size());
        SubjectDTO subjectDTO = capturedRequest.getSubjectDTOs().get(0);
        assertEquals(dpShopId, subjectDTO.getDpShopId());
        assertEquals(dpDealId.longValue(), subjectDTO.getProductId().longValue());
        assertEquals((Integer) PriceRangeSceneTypeEnum.PRODUCT_SHOP_TYPE.getCode(), capturedRequest.getPriceRangeSceneType());
        assertEquals((Integer) ProductTypeEnum.DEAL.getType(), capturedRequest.getProductType());
    }
}
