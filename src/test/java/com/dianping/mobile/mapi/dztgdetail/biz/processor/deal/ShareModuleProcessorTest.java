package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.UserWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.DpDztgShareModule;
import com.dianping.userremote.dto.collection.FavorDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShareModuleProcessorTest {

    @InjectMocks
    private ShareModuleProcessor shareModuleProcessor;

    @Mock
    private DealCtx ctx;

    @Mock
    private DealGroupBaseDTO dealGroupBaseDTO;
    @Mock
    private UserWrapper userWrapper;



    /**
     * 测试 ctx 不是美团直播小程序的情况
     */
    @Test
    public void testConvertWidthHeight_NotMtLiveMinApp() {
        // arrange
        when(ctx.isMtLiveMinApp()).thenReturn(false);

        // act
        int[] result = shareModuleProcessor.convertWidthHeight(ctx, 100, 80);

        // assert
        assertNull(result);
    }

    /**
     * 测试 width 或 height 小于等于 0 的情况
     */
    @Test
    public void testConvertWidthHeight_WidthOrHeightLessThanOrEqualToZero() {
        // arrange
        when(ctx.isMtLiveMinApp()).thenReturn(true);

        // act
        int[] result = shareModuleProcessor.convertWidthHeight(ctx, 0, 80);

        // assert
        assertNull(result);
    }

    /**
     * 测试 width 和 height 都大于 0，且宽高比为 5:4 的情况
     */
    @Test
    public void testConvertWidthHeight_WidthHeightRatioIsFiveToFour() {
        // arrange
        when(ctx.isMtLiveMinApp()).thenReturn(true);

        // act
        int[] result = shareModuleProcessor.convertWidthHeight(ctx, 100, 80);

        // assert
        assertNotNull(result);
        assertEquals(100, result[0]);
        assertEquals(80, result[1]);
    }

    /**
     * 测试 width 和 height 都大于 0，且宽高比为 4:5 的情况
     */
    @Test
    public void testConvertWidthHeight_WidthHeightRatioIsFourToFive() {
        // arrange
        when(ctx.isMtLiveMinApp()).thenReturn(true);

        // act
        int[] result = shareModuleProcessor.convertWidthHeight(ctx, 80, 100);

        // assert
        assertNotNull(result);
        assertEquals(80, result[0]);
        assertEquals(64, result[1]);
    }
    /**
     * 测试 width 和 height 都大于 0，且宽高比既不是 5:4，也不是 4:5 的情况
     */
    @Test
    public void testConvertWidthHeight_WidthHeightRatioIsNotFiveToFourOrFourToFive() {
        // arrange
        when(ctx.isMtLiveMinApp()).thenReturn(true);

        // act
        int[] result = shareModuleProcessor.convertWidthHeight(ctx, 120, 80);

        // assert
        assertNotNull(result);
        assertEquals(100, result[0]);
        assertEquals(80, result[1]);
    }

    /**
     * 测试 ctx.isMtLiveMinApp() 返回 true 的情况
     */
    @Test
    public void testBuildDpDztgShareModuleIsMtLiveMinAppTrue() {
        when(ctx.getDealGroupBase()).thenReturn(dealGroupBaseDTO);
        when(ctx.isMtLiveMinApp()).thenReturn(true);

        DpDztgShareModule result = shareModuleProcessor.buildDpDztgShareModule(ctx);

        assertNotNull(result);
    }

    /**
     * 测试 ctx.isMtLiveMinApp() 返回 false 的情况
     */
    @Test
    public void testBuildDpDztgShareModuleIsMtLiveMinAppFalse() {
        when(ctx.getDealGroupBase()).thenReturn(dealGroupBaseDTO);
        when(ctx.isMtLiveMinApp()).thenReturn(false);

        DpDztgShareModule result = shareModuleProcessor.buildDpDztgShareModule(ctx);

        assertNotNull(result);
    }

    @Test
    public void testProcess() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMtId(1);
        FutureCtx futureCtx = new FutureCtx();
        Future favorFuture = Mockito.mock(Future.class);
        futureCtx.setFavorFuture(favorFuture);
        ctx.setFutureCtx(futureCtx);
        FavorDTO favorDTO = new FavorDTO();
        Mockito.when(userWrapper.getFutureResult(favorFuture)).thenReturn(favorDTO);
        shareModuleProcessor.process(ctx);
        Assert.assertTrue(StringUtils.isNotBlank(ctx.getDztgShareModule().getShareId()));
    }
}
