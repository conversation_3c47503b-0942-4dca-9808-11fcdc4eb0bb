package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.dianping.deal.struct.common.dto.Resp;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupWrapper_GetBatchQueryDealDetailInfoResultTest {

    @InjectMocks
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private Future future;

    @Mock
    private Resp<Map<Integer, DealDetailDto>> resp;

    @Before
    public void setUp() {
    }

    @Test
    public void testGetBatchQueryDealDetailInfoResultWithEmptyFutures() throws Exception {
        Map<Integer, DealDetailDto> result = dealGroupWrapper.getBatchQueryDealDetailInfoResult(Collections.emptyList());
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetBatchQueryDealDetailInfoResultWithNullFutures() throws Exception {
        Map<Integer, DealDetailDto> result = dealGroupWrapper.getBatchQueryDealDetailInfoResult(Arrays.asList(null, null));
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetBatchQueryDealDetailInfoResultWithFailedFutures() throws Exception {
        when(future.get()).thenReturn(resp);
        when(resp.isSuccess()).thenReturn(false);
        Map<Integer, DealDetailDto> result = dealGroupWrapper.getBatchQueryDealDetailInfoResult(Collections.singletonList(future));
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetBatchQueryDealDetailInfoResultWithEmptyContentFutures() throws Exception {
        when(future.get()).thenReturn(resp);
        when(resp.isSuccess()).thenReturn(true);
        when(resp.getContent()).thenReturn(Collections.emptyMap());
        Map<Integer, DealDetailDto> result = dealGroupWrapper.getBatchQueryDealDetailInfoResult(Collections.singletonList(future));
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetBatchQueryDealDetailInfoResultWithSuccessFutures() throws Exception {
        Map<Integer, DealDetailDto> content = new HashMap<>();
        content.put(1, new DealDetailDto());
        when(future.get()).thenReturn(resp);
        when(resp.isSuccess()).thenReturn(true);
        when(resp.getContent()).thenReturn(content);
        Map<Integer, DealDetailDto> result = dealGroupWrapper.getBatchQueryDealDetailInfoResult(Collections.singletonList(future));
        assertEquals(content, result);
    }
}
