package com.dianping.mobile.mapi.dztgdetail.datatype.context;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class DealCtx_IsApolloTest {

    private DealCtx dealCtx;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        envCtx = Mockito.mock(EnvCtx.class);
        dealCtx = new DealCtx(envCtx);
    }

    /**
     * 测试 isApollo 方法，当 envCtx.isApollo 返回 true 时，应返回 true
     */
    @Test
    public void testIsApolloWhenEnvCtxIsApolloReturnsTrue() {
        // arrange
        when(envCtx.isApollo()).thenReturn(true);
        // act
        boolean result = dealCtx.isApollo();
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isApollo 方法，当 envCtx.isApollo 返回 false 时，应返回 false
     */
    @Test
    public void testIsApolloWhenEnvCtxIsApolloReturnsFalse() {
        // arrange
        when(envCtx.isApollo()).thenReturn(false);
        // act
        boolean result = dealCtx.isApollo();
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isApollo 方法，当 envCtx 为 null 时，应返回 false
     */
    @Test
    public void testIsApolloWhenEnvCtxIsNull() {
        // arrange
        dealCtx = new DealCtx(null);
        // act
        boolean result = dealCtx.isApollo();
        // assert
        assertFalse(result);
    }
}
