package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.dianping.mobile.mapi.dztgdetail.util.GreyUtils;
import java.lang.reflect.Field;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class DealGroupCustomerProcessorTest {

    private DealGroupCustomerProcessor processor;

    private DealGroupWrapper dealGroupWrapper;

    private DealCtx ctx;

    private FutureCtx futureCtx;

    private Future<?> customerFuture;

    @Before
    public void setUp() throws Exception {
        dealGroupWrapper = Mockito.mock(DealGroupWrapper.class);
        ctx = Mockito.mock(DealCtx.class);
        futureCtx = Mockito.mock(FutureCtx.class);
        customerFuture = Mockito.mock(Future.class);
        processor = new DealGroupCustomerProcessor();
        // Use reflection to set the private field
        Field field = DealGroupCustomerProcessor.class.getDeclaredField("dealGroupWrapper");
        field.setAccessible(true);
        field.set(processor, dealGroupWrapper);
        when(ctx.getFutureCtx()).thenReturn(futureCtx);
    }

    /**
     * 测试场景：GreyUtils.enableQueryCenterForMainApi(ctx) 返回 true
     */
    @Test
    public void testPrepareEnableQueryCenter() throws Throwable {
        // arrange
        when(GreyUtils.enableQueryCenterForMainApi(ctx)).thenReturn(true);
        // act
        processor.prepare(ctx);
        // assert
        verify(dealGroupWrapper, never()).preCustomerDTO(anyInt());
        verify(futureCtx, never()).setCustomerFuture(any());
    }

    /**
     * 测试场景：GreyUtils.enableQueryCenterForMainApi(ctx) 返回 false
     */
    @Test
    public void testPrepareDisableQueryCenter() throws Throwable {
        // arrange
        when(GreyUtils.enableQueryCenterForMainApi(ctx)).thenReturn(false);
        when(ctx.getDpId()).thenReturn(123);
        when(dealGroupWrapper.preCustomerDTO(123)).thenReturn(customerFuture);
        // act
        processor.prepare(ctx);
        // assert
        verify(dealGroupWrapper).preCustomerDTO(123);
        verify(futureCtx).setCustomerFuture(customerFuture);
    }

    /**
     * 测试场景：GreyUtils.enableQueryCenterForMainApi(ctx) 抛出异常
     */
    @Test
    public void testPrepareEnableQueryCenterThrowsException() throws Throwable {
        // arrange
        when(GreyUtils.enableQueryCenterForMainApi(ctx)).thenThrow(new RuntimeException("Test Exception"));
        when(ctx.getDpId()).thenReturn(123);
        when(dealGroupWrapper.preCustomerDTO(123)).thenReturn(customerFuture);
        // act
        processor.prepare(ctx);
        // assert
        verify(dealGroupWrapper).preCustomerDTO(123);
        verify(futureCtx).setCustomerFuture(customerFuture);
    }
}
