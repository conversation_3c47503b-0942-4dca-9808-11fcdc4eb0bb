package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.common.dto.Resp;
import com.dianping.deal.tag.dto.TbTagDTO;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler.HealthCertificateExaminerHandler;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailSpecificModuleVO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class SpecificModuleHandler_401BuildHealthCertificateModuleTest {

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Mock
    private HealthCertificateExaminerHandler healthCertificateExaminerHandler;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private DealDetailSpecificModuleVO invokePrivateMethod(SpecificModuleCtx context, String attrName) throws Exception {
        Method method = SpecificModuleHandler_401.class.getDeclaredMethod("buildHealthCertificateModule", SpecificModuleCtx.class, String.class);
        method.setAccessible(true);
        return (DealDetailSpecificModuleVO) method.invoke(specificModuleHandler_401, context, attrName);
    }

    /**
     * Test case when DealGroupDTO is null.
     */
    @Test
    public void testBuildHealthCertificateModule_DealGroupDTONull() throws Throwable {
        // arrange
        SpecificModuleCtx context = new SpecificModuleCtx();
        context.setDpDealGroupId(12345);
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(null);
        // act
        DealDetailSpecificModuleVO result = invokePrivateMethod(context, "出证类型");
        // assert
        assertNull(result);
    }

    /**
     * Test case when attrName is unrecognized.
     */
    @Test
    public void testBuildHealthCertificateModule_UnrecognizedAttrName() throws Throwable {
        // arrange
        SpecificModuleCtx context = new SpecificModuleCtx();
        context.setDpDealGroupId(12345);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        when(queryCenterWrapper.getDealGroupDTO(any(Future.class))).thenReturn(dealGroupDTO);
        // act
        DealDetailSpecificModuleVO result = invokePrivateMethod(context, "Unknown");
        // assert
        assertNull(result);
    }

    @InjectMocks
    private SpecificModuleHandler_401 specificModuleHandler_401;
}
