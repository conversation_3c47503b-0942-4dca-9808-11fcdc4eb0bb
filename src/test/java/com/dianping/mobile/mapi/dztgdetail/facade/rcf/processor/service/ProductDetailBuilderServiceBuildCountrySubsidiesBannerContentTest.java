package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.RichContentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.TextRichContentVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.common.rich.content.enums.RichContentTypeEnum;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductDetailBuilderServiceBuildCountrySubsidiesBannerContentTest {

    private ProductDetailBuilderService productDetailBuilderService = new ProductDetailBuilderService();

    @Mock
    private TextRichContentVO picContent;

    @Mock
    private TextRichContentVO textContent1;

    @Mock
    private TextRichContentVO textContent2;

    @Test
    public void testBuildCountrySubsidiesBannerContentWhenBannerDataIsNull() throws Throwable {
        String result = productDetailBuilderService.buildCountrySubsidiesBannerContent(null);
        assertNull(result);
    }

    @Test
    public void testBuildCountrySubsidiesBannerContentWhenAllRichContentVOIsPic() throws Throwable {
        when(picContent.getType()).thenReturn(RichContentTypeEnum.PIC.getCode());
        String result = productDetailBuilderService.buildCountrySubsidiesBannerContent(Collections.singletonList(picContent));
        assertEquals("[]", result);
    }

    @Test
    public void testBuildCountrySubsidiesBannerContentWhenSomeRichContentVOIsPic() throws Throwable {
        when(textContent1.getType()).thenReturn(RichContentTypeEnum.TEXT.getCode());
        when(textContent1.getText()).thenReturn("text1");
        when(picContent.getType()).thenReturn(RichContentTypeEnum.PIC.getCode());
        String result = productDetailBuilderService.buildCountrySubsidiesBannerContent(Arrays.asList(textContent1, picContent));
        assertTrue(result.contains("text1"));
    }

    @Test
    public void testBuildCountrySubsidiesBannerContentWhenNoRichContentVOIsPic() throws Throwable {
        when(textContent1.getType()).thenReturn(RichContentTypeEnum.TEXT.getCode());
        when(textContent1.getText()).thenReturn("text1");
        when(textContent2.getType()).thenReturn(RichContentTypeEnum.TEXT.getCode());
        when(textContent2.getText()).thenReturn("text2");
        String result = productDetailBuilderService.buildCountrySubsidiesBannerContent(Arrays.asList(textContent1, textContent2));
        assertTrue(result.contains("text1text2"));
    }
}
