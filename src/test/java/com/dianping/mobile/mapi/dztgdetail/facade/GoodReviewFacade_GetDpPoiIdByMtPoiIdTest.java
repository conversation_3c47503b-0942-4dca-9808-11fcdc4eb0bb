package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.MapperWrapper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class GoodReviewFacade_GetDpPoiIdByMtPoiIdTest {

    @InjectMocks
    private GoodReviewFacade goodReviewFacade;

    @Mock
    private MapperWrapper mapperWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 mtPoiId 为 null 的情况
     */
    @Test
    public void testGetDpPoiIdByMtPoiIdNull() {
        String result = goodReviewFacade.getDpPoiIdByMtPoiId(null);
        assertEquals("", result);
    }

    /**
     * 测试 mtPoiId 为非数字字符串的情况
     */
    @Test
    public void testGetDpPoiIdByMtPoiIdNonNumeric() {
        String result = goodReviewFacade.getDpPoiIdByMtPoiId("abc");
        assertEquals("", result);
    }

    /**
     * 测试 mtPoiId 为数字字符串，但 mapperWrapper.getDpByMtShopId 返回 null 的情况
     */
    @Test
    public void testGetDpPoiIdByMtPoiIdNumericButMapperWrapperReturnsNull() {
        when(mapperWrapper.getDpByMtShopId(123L)).thenReturn(null);
        String result = goodReviewFacade.getDpPoiIdByMtPoiId("123");
        assertEquals("", result);
    }

    /**
     * 测试 mtPoiId 为数字字符串，mapperWrapper.getDpByMtShopId 返回非 null 的情况
     */
    @Test
    public void testGetDpPoiIdByMtPoiIdNumericAndMapperWrapperReturnsNonNull() {
        when(mapperWrapper.getDpByMtShopId(123L)).thenReturn(456L);
        String result = goodReviewFacade.getDpPoiIdByMtPoiId("123");
        assertEquals("456", result);
    }
}
