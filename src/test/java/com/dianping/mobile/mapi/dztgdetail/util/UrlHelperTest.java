package com.dianping.mobile.mapi.dztgdetail.util;

import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.entity.FreeDealConfig;
import com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UrlHelperTest {

    @Test
    public void testGetFreeDealUrl() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setClientType(1);
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMtLongShopId(111);
        ctx.setMtId(111);
        FreeDealConfig freeDealConfig = new FreeDealConfig();
        freeDealConfig.setMtAppSchema("imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customersubmit&mtShopId={shopId}&mtDealGroupId={dealId}");
        String url = UrlHelper.getFreeDealUrl(ctx, freeDealConfig);
        Assert.assertEquals(url, "imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customersubmit&mtShopId=111&mtDealGroupId=111");
    }

    @Test
    public void testGetWxCommonBuyUrl() {
        DealCtx ctx = new DealCtx(new EnvCtx());
        ctx.setRequestSource(RequestSourceEnum.LIVE_STREAM.getSource());
        String mtStr = UrlHelper.getWxCommonBuyUrl(ctx, 1, 1, true, 1);
        Assert.assertTrue(mtStr.contains("/gnc/pages/ordering/index"));

        String otherStr = UrlHelper.getWxCommonBuyUrl(ctx, 1, 1, false, 1);
        Assert.assertTrue(otherStr.contains("/packages/tuan/pages/ordersubmit/ordersubmit?type=1"));

        ctx.setRequestSource(RequestSourceEnum.COST_EFFECTIVE.getSource());
        String mtPageSourceStr = UrlHelper.getWxCommonBuyUrl(ctx, 1, 1, true, 1);
        Assert.assertTrue(mtPageSourceStr.contains("&source=cost_effective"));
    }

    @Test
    public void testGetOrderUrlDealGroupBaseIsNull() {
        DealCtx ctx = mock(DealCtx.class);
        DealGroupBaseDTO dealGroupBase = mock(DealGroupBaseDTO.class);
        when(ctx.getDealGroupBase()).thenReturn(null);

        // act
        String result = UrlHelper.getOrderUrl(ctx);

        // assert
        assertNull(result);
    }

    /**
     * 测试 getOrderUrl 方法，当 ctx 为美团时
     */
    @Test
    public void testGetOrderUrlIsMt() {
        DealCtx ctx = mock(DealCtx.class);
        DealGroupBaseDTO dealGroupBase = mock(DealGroupBaseDTO.class);
        when(ctx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(ctx.getMtId()).thenReturn(1);
        when(ctx.isMt()).thenReturn(true);
        when(ctx.getMtLongShopId()).thenReturn(1000L);

        // act
        String result = UrlHelper.getOrderUrl(ctx);

        // assert
        assertEquals("imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customersubmit&entranceCode=11&mtShopId=1000&mtDealGroupId=1", result);
    }

    /**
     * 测试 getOrderUrl 方法，当 ctx 为大众点评时
     */
    @Test
    public void testGetOrderUrlIsDp() {
        DealCtx ctx = mock(DealCtx.class);
        DealGroupBaseDTO dealGroupBase = mock(DealGroupBaseDTO.class);
        when(ctx.getDealGroupBase()).thenReturn(dealGroupBase);
        when(ctx.getDpId()).thenReturn(1);
        when(ctx.isMt()).thenReturn(false);
        when(ctx.getDpLongShopId()).thenReturn(2000L);

        // act
        String result = UrlHelper.getOrderUrl(ctx);

        // assert
        assertEquals("dianping://mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customersubmit&entranceCode=11&dpShopId=2000&dpDealGroupId=1", result);
    }
}
