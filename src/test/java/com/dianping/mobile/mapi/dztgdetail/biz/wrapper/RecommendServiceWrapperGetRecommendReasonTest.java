package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RecommendServiceWrapperGetRecommendReasonTest {

    @Mock
    private Future future;

    @Mock
    private Response<RecommendResult<RecommendDTO>> response;

    @Mock
    private RecommendResult<RecommendDTO> recommendResult;

    @Mock
    private RecommendDTO recommendDTO;

    private RecommendServiceWrapper recommendServiceWrapper;

    @Before
    public void setUp() {
        recommendServiceWrapper = new RecommendServiceWrapper();
    }

    @Test
    public void testGetRecommendReasonFutureIsNull() throws Throwable {
        Map<Long, String> result = recommendServiceWrapper.getRecommendReason(null);
        assertTrue("Result should be empty when future is null", result.isEmpty());
    }

    @Test
    public void testGetRecommendReasonFutureResultIsNull() throws Throwable {
        when(future.get()).thenReturn(null);
        Map<Long, String> result = recommendServiceWrapper.getRecommendReason(future);
        assertTrue("Result should be empty when future result is null", result.isEmpty());
    }

    @Test
    public void testGetRecommendReasonResponseIsNull() throws Throwable {
        when(future.get()).thenReturn(response);
        when(response.getResult()).thenReturn(null);
        Map<Long, String> result = recommendServiceWrapper.getRecommendReason(future);
        assertTrue("Result should be empty when response is null", result.isEmpty());
    }

    @Test
    public void testGetRecommendReasonResultIsNull() throws Throwable {
        when(future.get()).thenReturn(response);
        when(response.getResult()).thenReturn(recommendResult);
        Map<Long, String> result = recommendServiceWrapper.getRecommendReason(future);
        assertTrue("Result should be empty when result is null", result.isEmpty());
    }

    @Test
    public void testGetRecommendReasonSortedResultIsEmpty() throws Throwable {
        when(future.get()).thenReturn(response);
        when(response.getResult()).thenReturn(recommendResult);
        when(recommendResult.getSortedResult()).thenReturn(Collections.emptyList());
        Map<Long, String> result = recommendServiceWrapper.getRecommendReason(future);
        assertTrue("Result should be empty when sortedResult is empty", result.isEmpty());
    }

    @Test
    public void testGetRecommendReasonSortedResultIsNotEmpty() throws Throwable {
        when(future.get()).thenReturn(response);
        when(response.getResult()).thenReturn(recommendResult);
        when(recommendResult.getSortedResult()).thenReturn(Collections.singletonList(recommendDTO));
        // Ensure the item is set to "1" to match the key 1L
        when(recommendDTO.getItem()).thenReturn("1");
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("reason", "item");
        when(recommendDTO.getBizData()).thenReturn(bizData);
        Map<Long, String> result = recommendServiceWrapper.getRecommendReason(future);
        assertEquals("Reason should match the item", "item", result.get(1L));
    }
}
