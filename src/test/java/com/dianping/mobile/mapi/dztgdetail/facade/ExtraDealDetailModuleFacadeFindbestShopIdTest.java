package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;
import com.dianping.deal.common.enums.GpsType;
import com.dianping.deal.shop.dto.BestShopDTO;
import com.dianping.deal.shop.dto.BestShopReq;
import com.dianping.lion.client.util.StringUtils;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DealGroupWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.ExtraDealDetailModuleReq;
import com.dianping.mobile.mapi.dztgdetail.util.DealProductUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopCheckDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupShopRelationCheckResultDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ExtraDealDetailModuleFacadeFindbestShopIdTest {

    @Mock
    private DealGroupWrapper dealGroupWrapper;

    @InjectMocks
    private ExtraDealDetailModuleFacade extraDealDetailModuleFacade;

    /**
     * Test case for when shopId is provided but not bound to the deal group,
     * and bestShop is null
     */
    @Test
    public void testFindbestShopIdWithShopIdNotBoundAndBestShopNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setDpDisplayShopIds(Arrays.asList(1L, 2L, 3L));
        dealGroupDTO.setDisplayShopInfo(displayShopInfo);
        DealGroupDisplayShopCheckDTO displayShopCheckResult = new DealGroupDisplayShopCheckDTO();
        List<DealGroupShopRelationCheckResultDTO> dpCheckResults = new ArrayList<>();
        DealGroupShopRelationCheckResultDTO checkResult = new DealGroupShopRelationCheckResultDTO();
        checkResult.setShopId(5L);
        checkResult.setRelationCheckSuccess(true);
        dpCheckResults.add(checkResult);
        displayShopCheckResult.setDpDisplayShopCheckResult(dpCheckResults);
        dealGroupDTO.setDisplayShopCheckResult(displayShopCheckResult);
        ExtraDealDetailModuleReq req = new ExtraDealDetailModuleReq();
        req.setShopId("4");
        req.setDealGroupId("123");
        req.setCityId(1);
        req.setUserLat(31.2304);
        req.setUserLng(121.4737);
        EnvCtx ctx = new EnvCtx();
        // Mock the buildBestShopReqWithoutShopId method
        BestShopReq bestShopReq = new BestShopReq();
        bestShopReq.setDealGroupId(123L);
        bestShopReq.setCityId(1);
        bestShopReq.setLat(31.2304);
        bestShopReq.setLng(121.4737);
        bestShopReq.setGpsType(GpsType.GCJ02.getType());
        Future mockFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(mockFuture);
        when(dealGroupWrapper.getFutureResult(mockFuture)).thenReturn(null);
        // act
        Long result = extraDealDetailModuleFacade.findbestShopId(dealGroupDTO, req, ctx);
        // assert
        assertNull(result);
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(mockFuture);
    }

    /**
     * Test case for when shopId is provided but not bound to the deal group,
     * and bestShop returns a valid shop
     */
    @Test
    public void testFindbestShopIdWithShopIdNotBoundAndBestShopValid() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setDpDisplayShopIds(Arrays.asList(1L, 2L, 3L));
        dealGroupDTO.setDisplayShopInfo(displayShopInfo);
        DealGroupDisplayShopCheckDTO displayShopCheckResult = new DealGroupDisplayShopCheckDTO();
        List<DealGroupShopRelationCheckResultDTO> dpCheckResults = new ArrayList<>();
        DealGroupShopRelationCheckResultDTO checkResult = new DealGroupShopRelationCheckResultDTO();
        checkResult.setShopId(5L);
        checkResult.setRelationCheckSuccess(true);
        dpCheckResults.add(checkResult);
        displayShopCheckResult.setDpDisplayShopCheckResult(dpCheckResults);
        dealGroupDTO.setDisplayShopCheckResult(displayShopCheckResult);
        ExtraDealDetailModuleReq req = new ExtraDealDetailModuleReq();
        req.setShopId("4");
        req.setDealGroupId("123");
        req.setCityId(1);
        req.setUserLat(31.2304);
        req.setUserLng(121.4737);
        EnvCtx ctx = new EnvCtx();
        Future mockFuture = mock(Future.class);
        BestShopDTO bestShop = new BestShopDTO();
        bestShop.setDpShopId(5L);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(mockFuture);
        when(dealGroupWrapper.getFutureResult(mockFuture)).thenReturn(bestShop);
        // act
        Long result = extraDealDetailModuleFacade.findbestShopId(dealGroupDTO, req, ctx);
        // assert
        assertEquals(Long.valueOf(5L), result);
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(mockFuture);
    }

    /**
     * Test case for when shopId is provided and bound to the deal group
     */
    @Test
    public void testFindbestShopIdWithShopIdBound() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setDpDisplayShopIds(Arrays.asList(1L, 2L, 3L));
        dealGroupDTO.setDisplayShopInfo(displayShopInfo);
        DealGroupDisplayShopCheckDTO displayShopCheckResult = new DealGroupDisplayShopCheckDTO();
        List<DealGroupShopRelationCheckResultDTO> dpCheckResults = new ArrayList<>();
        DealGroupShopRelationCheckResultDTO checkResult = new DealGroupShopRelationCheckResultDTO();
        checkResult.setShopId(2L);
        checkResult.setRelationCheckSuccess(true);
        dpCheckResults.add(checkResult);
        displayShopCheckResult.setDpDisplayShopCheckResult(dpCheckResults);
        dealGroupDTO.setDisplayShopCheckResult(displayShopCheckResult);
        ExtraDealDetailModuleReq req = new ExtraDealDetailModuleReq();
        req.setShopId("2");
        req.setDealGroupId("123");
        req.setCityId(1);
        req.setUserLat(31.2304);
        req.setUserLng(121.4737);
        EnvCtx ctx = new EnvCtx();
        Future mockFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(mockFuture);
        // act
        Long result = extraDealDetailModuleFacade.findbestShopId(dealGroupDTO, req, ctx);
        // assert
        assertEquals(Long.valueOf(2L), result);
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper, never()).getFutureResult(mockFuture);
    }

    /**
     * Test case for when no shopId is provided and bestShop is null
     */
    @Test
    public void testFindbestShopIdWithNoShopIdAndBestShopNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setDpDisplayShopIds(Arrays.asList(1L, 2L, 3L));
        dealGroupDTO.setDisplayShopInfo(displayShopInfo);
        ExtraDealDetailModuleReq req = new ExtraDealDetailModuleReq();
        req.setShopId("");
        req.setDealGroupId("123");
        req.setCityId(1);
        req.setUserLat(31.2304);
        req.setUserLng(121.4737);
        EnvCtx ctx = new EnvCtx();
        Future mockFuture = mock(Future.class);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(mockFuture);
        when(dealGroupWrapper.getFutureResult(mockFuture)).thenReturn(null);
        // act
        Long result = extraDealDetailModuleFacade.findbestShopId(dealGroupDTO, req, ctx);
        // assert
        assertNull(result);
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(mockFuture);
    }

    /**
     * Test case for when no shopId is provided and bestShop returns a valid shop
     */
    @Test
    public void testFindbestShopIdWithNoShopIdAndBestShopValid() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setDpDisplayShopIds(Arrays.asList(1L, 2L, 3L));
        dealGroupDTO.setDisplayShopInfo(displayShopInfo);
        ExtraDealDetailModuleReq req = new ExtraDealDetailModuleReq();
        req.setShopId("");
        req.setDealGroupId("123");
        req.setCityId(1);
        req.setUserLat(31.2304);
        req.setUserLng(121.4737);
        EnvCtx ctx = new EnvCtx();
        Future mockFuture = mock(Future.class);
        BestShopDTO bestShop = new BestShopDTO();
        bestShop.setDpShopId(5L);
        when(dealGroupWrapper.preDealGroupBestShop(any(BestShopReq.class))).thenReturn(mockFuture);
        when(dealGroupWrapper.getFutureResult(mockFuture)).thenReturn(bestShop);
        // act
        Long result = extraDealDetailModuleFacade.findbestShopId(dealGroupDTO, req, ctx);
        // assert
        assertEquals(Long.valueOf(5L), result);
        verify(dealGroupWrapper).preDealGroupBestShop(any(BestShopReq.class));
        verify(dealGroupWrapper).getFutureResult(mockFuture);
    }
}
