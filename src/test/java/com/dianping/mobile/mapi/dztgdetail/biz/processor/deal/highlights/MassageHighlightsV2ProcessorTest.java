package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.factory.MassageFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.QueryCenterWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.DealRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.ReadjustPriceRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.standardPriceRule.StandardPriceRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.standardPriceRule.StandardPriceRuleItemDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import junit.framework.TestCase;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

/**
 * <AUTHOR>
 * @since 2024/2/27 20:21
 */
@RunWith(MockitoJUnitRunner.class)
public class MassageHighlightsV2ProcessorTest extends TestCase {

    @InjectMocks
    private MassageHighlightsV2Processor processor;

    @Mock
    private MassageFactory massageToolFactory;

    @Mock
    private QueryCenterWrapper queryCenterWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private ServiceProjectDTO invokePrivateMethod(DealGroupServiceProjectDTO input) throws Exception {
        Method method = MassageHighlightsV2Processor.class.getDeclaredMethod("getServiceProject", DealGroupServiceProjectDTO.class);
        method.setAccessible(true);
        return (ServiceProjectDTO) method.invoke(processor, input);
    }

    private String invokePrivateGetOverNightValue(List<DealGroupDealDTO> deals) throws Exception {
        Method method = MassageHighlightsV2Processor.class.getDeclaredMethod("getOverNightValue", List.class);
        method.setAccessible(true);
        return (String) method.invoke(processor, deals);
    }

    private String invokePrivateMethod(String methodName, String key, DealGroupDTO dealGroupDTO) throws Throwable {
        try {
            Method method = MassageHighlightsV2Processor.class.getDeclaredMethod(methodName, String.class, DealGroupDTO.class);
            method.setAccessible(true);
            return (String) method.invoke(processor, key, dealGroupDTO);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    @Test
    public void testGetNewFoodDataByServiceProjectAttr_EmptyList() {
        assertNull(MassageHighlightsV2Processor.getNewFoodDataByServiceProjectAttr(new ArrayList<>()));
    }

    @Test
    public void testGetNewFoodDataByServiceProjectAttr_NoFreeFood() {
        ServiceProjectAttrDTO attrDTO = new ServiceProjectAttrDTO();
        attrDTO.setAttrName("other");
        attrDTO.setAttrValue("otherValue");
        assertNull(MassageHighlightsV2Processor.getNewFoodDataByServiceProjectAttr(Arrays.asList(attrDTO)));
    }

    @Test
    public void testGetNewFoodDataByServiceProjectAttr_FreeFoodNotInNewData() {
        ServiceProjectAttrDTO attrDTO = new ServiceProjectAttrDTO();
        attrDTO.setAttrName("freeFood");
        attrDTO.setAttrValue("otherValue");
        assertNull(MassageHighlightsV2Processor.getNewFoodDataByServiceProjectAttr(Arrays.asList(attrDTO)));
    }

    @Test
    public void testGetNewFoodDataByServiceProjectAttr_FreeFoodIsTeaAndFruit() {
        ServiceProjectAttrDTO attrDTO1 = new ServiceProjectAttrDTO();
        attrDTO1.setAttrName("freeFood");
        attrDTO1.setAttrValue("茶点水果");
        ServiceProjectAttrDTO attrDTO2 = new ServiceProjectAttrDTO();
        attrDTO2.setAttrName("Fruit");
        attrDTO2.setAttrValue("FruitValue");
        assertEquals("茶点水果", MassageHighlightsV2Processor.getNewFoodDataByServiceProjectAttr(Arrays.asList(attrDTO1, attrDTO2)));
    }

    @Test
    public void testGetNewFoodDataByServiceProjectAttr_FreeFoodIsTeaAndNoFruit() {
        ServiceProjectAttrDTO attrDTO = new ServiceProjectAttrDTO();
        attrDTO.setAttrName("freeFood");
        attrDTO.setAttrValue("茶点水果");
        assertEquals("茶点", MassageHighlightsV2Processor.getNewFoodDataByServiceProjectAttr(Arrays.asList(attrDTO)));
    }

    @Test
    public void testGetNewFoodDataByServiceProjectAttr_FreeFoodNotTea() {
        ServiceProjectAttrDTO attrDTO = new ServiceProjectAttrDTO();
        attrDTO.setAttrName("freeFood");
        attrDTO.setAttrValue("自助餐畅吃");
        assertEquals("自助餐畅吃", MassageHighlightsV2Processor.getNewFoodDataByServiceProjectAttr(Arrays.asList(attrDTO)));
    }

    /**
     * Test case: input parameter is null
     * Expected: should return null
     */
    @Test
    public void testGetServiceProject_WhenInputNull() throws Throwable {
        // arrange
        DealGroupServiceProjectDTO input = null;
        // act
        ServiceProjectDTO result = invokePrivateMethod(input);
        // assert
        assertNull(result);
    }

    /**
     * Test case: must groups list is null
     * Expected: should return null
     */
    @Test
    public void testGetServiceProject_WhenMustGroupsNull() throws Throwable {
        // arrange
        DealGroupServiceProjectDTO input = mock(DealGroupServiceProjectDTO.class);
        when(input.getMustGroups()).thenReturn(null);
        // act
        ServiceProjectDTO result = invokePrivateMethod(input);
        // assert
        assertNull(result);
    }

    /**
     * Test case: must groups list is empty
     * Expected: should return null
     */
    @Test
    public void testGetServiceProject_WhenMustGroupsEmpty() throws Throwable {
        // arrange
        DealGroupServiceProjectDTO input = mock(DealGroupServiceProjectDTO.class);
        when(input.getMustGroups()).thenReturn(Collections.emptyList());
        // act
        ServiceProjectDTO result = invokePrivateMethod(input);
        // assert
        assertNull(result);
    }

    /**
     * Test case: groups in must service project group is null
     * Expected: should return null
     */
    @Test
    public void testGetServiceProject_WhenGroupsNull() throws Throwable {
        // arrange
        DealGroupServiceProjectDTO input = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        List<MustServiceProjectGroupDTO> mustGroups = Collections.singletonList(mustGroup);
        when(input.getMustGroups()).thenReturn(mustGroups);
        when(mustGroup.getGroups()).thenReturn(null);
        // act
        ServiceProjectDTO result = invokePrivateMethod(input);
        // assert
        assertNull(result);
    }

    /**
     * Test case: groups in must service project group is empty
     * Expected: should return null
     */
    @Test
    public void testGetServiceProject_WhenGroupsEmpty() throws Throwable {
        // arrange
        DealGroupServiceProjectDTO input = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        List<MustServiceProjectGroupDTO> mustGroups = Collections.singletonList(mustGroup);
        when(input.getMustGroups()).thenReturn(mustGroups);
        when(mustGroup.getGroups()).thenReturn(Collections.emptyList());
        // act
        ServiceProjectDTO result = invokePrivateMethod(input);
        // assert
        assertNull(result);
    }

    /**
     * Test case: valid input with complete data
     * Expected: should return first service project from groups
     */
    @Test
    public void testGetServiceProject_WhenValidInput() throws Throwable {
        // arrange
        DealGroupServiceProjectDTO input = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        ServiceProjectDTO expectedServiceProject = mock(ServiceProjectDTO.class);
        List<MustServiceProjectGroupDTO> mustGroups = Collections.singletonList(mustGroup);
        List<ServiceProjectDTO> groups = Collections.singletonList(expectedServiceProject);
        when(input.getMustGroups()).thenReturn(mustGroups);
        when(mustGroup.getGroups()).thenReturn(groups);
        // act
        ServiceProjectDTO result = invokePrivateMethod(input);
        // assert
        assertEquals(expectedServiceProject, result);
    }

    /**
     * Test case: multiple groups in must service project group
     * Expected: should return first service project from groups
     */
    @Test
    public void testGetServiceProject_WhenMultipleGroups() throws Throwable {
        // arrange
        DealGroupServiceProjectDTO input = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        ServiceProjectDTO expectedServiceProject = mock(ServiceProjectDTO.class);
        ServiceProjectDTO secondServiceProject = mock(ServiceProjectDTO.class);
        List<MustServiceProjectGroupDTO> mustGroups = Collections.singletonList(mustGroup);
        List<ServiceProjectDTO> groups = new ArrayList<>();
        groups.add(expectedServiceProject);
        groups.add(secondServiceProject);
        when(input.getMustGroups()).thenReturn(mustGroups);
        when(mustGroup.getGroups()).thenReturn(groups);
        // act
        ServiceProjectDTO result = invokePrivateMethod(input);
        // assert
        assertEquals(expectedServiceProject, result);
    }

    /**
     * Test case for null standardPriceRule
     */
    @Test
    public void testGetOverNightValue_NullStandardPriceRule() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = new ArrayList<>();
        DealGroupDealDTO deal = new DealGroupDealDTO();
        DealRuleDTO rule = new DealRuleDTO();
        List<ReadjustPriceRuleDTO> readjustPriceRules = new ArrayList<>();
        ReadjustPriceRuleDTO readjustPriceRule = new ReadjustPriceRuleDTO();
        readjustPriceRule.setStandardPriceRule(null);
        readjustPriceRules.add(readjustPriceRule);
        rule.setReadjustPriceRules(readjustPriceRules);
        deal.setRule(rule);
        deals.add(deal);
        // act
        String result = invokePrivateGetOverNightValue(deals);
        // assert
        assertNull(result);
    }

    /**
     * Test case for no overNightService item
     */
    @Test
    public void testGetOverNightValue_NoOverNightServiceItem() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = new ArrayList<>();
        DealGroupDealDTO deal = new DealGroupDealDTO();
        DealRuleDTO rule = new DealRuleDTO();
        List<ReadjustPriceRuleDTO> readjustPriceRules = new ArrayList<>();
        ReadjustPriceRuleDTO readjustPriceRule = new ReadjustPriceRuleDTO();
        StandardPriceRuleDTO standardPriceRule = new StandardPriceRuleDTO();
        List<StandardPriceRuleItemDTO> items = new ArrayList<>();
        StandardPriceRuleItemDTO item = new StandardPriceRuleItemDTO();
        item.setIdentityKey("otherKey");
        items.add(item);
        standardPriceRule.setStandardPriceRuleItems(items);
        readjustPriceRule.setStandardPriceRule(standardPriceRule);
        readjustPriceRules.add(readjustPriceRule);
        rule.setReadjustPriceRules(readjustPriceRules);
        deal.setRule(rule);
        deals.add(deal);
        // act
        String result = invokePrivateGetOverNightValue(deals);
        // assert
        assertNull(result);
    }

    /**
     * Test case for having overNightService item
     */
    @Test
    public void testGetOverNightValue_HasOverNightServiceItem() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = new ArrayList<>();
        DealGroupDealDTO deal = new DealGroupDealDTO();
        DealRuleDTO rule = new DealRuleDTO();
        List<ReadjustPriceRuleDTO> readjustPriceRules = new ArrayList<>();
        ReadjustPriceRuleDTO readjustPriceRule = new ReadjustPriceRuleDTO();
        StandardPriceRuleDTO standardPriceRule = new StandardPriceRuleDTO();
        List<StandardPriceRuleItemDTO> items = new ArrayList<>();
        StandardPriceRuleItemDTO item = new StandardPriceRuleItemDTO();
        item.setIdentityKey("overNightService");
        items.add(item);
        standardPriceRule.setStandardPriceRuleItems(items);
        readjustPriceRule.setStandardPriceRule(standardPriceRule);
        readjustPriceRules.add(readjustPriceRule);
        rule.setReadjustPriceRules(readjustPriceRules);
        deal.setRule(rule);
        deals.add(deal);
        // act
        String result = invokePrivateGetOverNightValue(deals);
        // assert
        assertEquals("可过夜", result);
    }

    /**
     * Test case for multiple rules with overNightService in second rule
     */
    @Test
    public void testGetOverNightValue_MultipleRules_SecondHasOverNightService() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = new ArrayList<>();
        DealGroupDealDTO deal = new DealGroupDealDTO();
        DealRuleDTO rule = new DealRuleDTO();
        List<ReadjustPriceRuleDTO> readjustPriceRules = new ArrayList<>();
        // First rule without overNightService
        ReadjustPriceRuleDTO rule1 = new ReadjustPriceRuleDTO();
        StandardPriceRuleDTO standardPriceRule1 = new StandardPriceRuleDTO();
        List<StandardPriceRuleItemDTO> items1 = new ArrayList<>();
        StandardPriceRuleItemDTO item1 = new StandardPriceRuleItemDTO();
        item1.setIdentityKey("otherKey");
        items1.add(item1);
        standardPriceRule1.setStandardPriceRuleItems(items1);
        rule1.setStandardPriceRule(standardPriceRule1);
        // Second rule with overNightService
        ReadjustPriceRuleDTO rule2 = new ReadjustPriceRuleDTO();
        StandardPriceRuleDTO standardPriceRule2 = new StandardPriceRuleDTO();
        List<StandardPriceRuleItemDTO> items2 = new ArrayList<>();
        StandardPriceRuleItemDTO item2 = new StandardPriceRuleItemDTO();
        item2.setIdentityKey("overNightService");
        items2.add(item2);
        standardPriceRule2.setStandardPriceRuleItems(items2);
        rule2.setStandardPriceRule(standardPriceRule2);
        readjustPriceRules.add(rule1);
        readjustPriceRules.add(rule2);
        rule.setReadjustPriceRules(readjustPriceRules);
        deal.setRule(rule);
        deals.add(deal);
        // act
        String result = invokePrivateGetOverNightValue(deals);
        // assert
        assertEquals("可过夜", result);
    }

    /**
     * Test case for getting special service value when title contains special service
     */
    @Test
    public void testGetAttrValue_SpecialService_WhenTitleContainsService() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        basicDTO.setTitle("头皮检测服务");
        dealGroupDTO.setBasic(basicDTO);
        ServiceProjectDTO serviceProject = new ServiceProjectDTO();
        serviceProject.setAttrs(new ArrayList<>());
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Collections.singletonList(serviceProject));
        serviceProjectDTO.setMustGroups(Collections.singletonList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        String result = invokePrivateMethod("getAttrValue", "特色服务", dealGroupDTO);
        // assert
        assertEquals("头皮检测", result);
    }

    /**
     * Test case for getting free food value when food content array exists
     */
    @Test
    public void testGetAttrValue_FreeFoodValue_WithFoodContentArray() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ServiceProjectDTO serviceProject = new ServiceProjectDTO();
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("foodContentArray");
        attr.setAttrValue("[{\"foodType\":\"自助餐\"}]");
        attrs.add(attr);
        serviceProject.setAttrs(attrs);
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Collections.singletonList(serviceProject));
        serviceProjectDTO.setMustGroups(Collections.singletonList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        String result = invokePrivateMethod("getAttrValue", "免费餐食", dealGroupDTO);
        // assert
        assertEquals("自助餐", result);
    }

    /**
     * Test case for getting tool value from massage factory
     */
    @Test
    public void testGetAttrValue_ToolValue() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ServiceProjectDTO serviceProject = new ServiceProjectDTO();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Collections.singletonList(serviceProject));
        serviceProjectDTO.setMustGroups(Collections.singletonList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        when(massageToolFactory.getToolValue(serviceProject)).thenReturn("精油");
        // act
        String result = invokePrivateMethod("getAttrValue", "材料工具", dealGroupDTO);
        // assert
        assertEquals("精油", result);
    }

    /**
     * Test case for getting joy facility value when service facility exists
     */
    @Test
    public void testGetAttrValue_JoyFacilityValue() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        ServiceProjectDTO serviceProject = new ServiceProjectDTO();
        List<ServiceProjectAttrDTO> attrs = new ArrayList<>();
        ServiceProjectAttrDTO attr = new ServiceProjectAttrDTO();
        attr.setAttrName("serviceFacility");
        attr.setAttrValue("按摩椅");
        attrs.add(attr);
        serviceProject.setAttrs(attrs);
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Collections.singletonList(serviceProject));
        serviceProjectDTO.setMustGroups(Collections.singletonList(mustGroup));
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        // act
        String result = invokePrivateMethod("getAttrValue", "玩乐设施", dealGroupDTO);
        // assert
        assertEquals("按摩椅", result);
    }

    /**
     * Test case for invalid key
     */
    @Test
    public void testGetAttrValue_InvalidKey_ReturnsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // act
        String result = invokePrivateMethod("getAttrValue", "无效key", dealGroupDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test case for null DealGroupDTO
     */
    @Test(expected = NullPointerException.class)
    public void testGetAttrValue_NullDealGroupDTO_ReturnsNull() throws Throwable {
        // act
        String result = invokePrivateMethod("getAttrValue", "特色服务", null);
        // assert
        assertNull(result);
    }

    /**
     * Test case for null DealGroupDTO
     */
    @Test
    public void testBuildMassageHighlightsNullDealGroupDTO() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = null;
        DealCtx ctx = new DealCtx(new EnvCtx());
        // act
        processor.buildMassageHighlights(dealGroupDTO, ctx);
        // assert
        assertNull(ctx.getHighlightsModule());
    }

    /**
     * Test case for null ServiceProject in DealGroupDTO
     */
    @Test
    public void testBuildMassageHighlightsNullServiceProject() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setServiceProject(null);
        DealCtx ctx = new DealCtx(new EnvCtx());
        // act
        processor.buildMassageHighlights(dealGroupDTO, ctx);
        // assert
        assertNull(ctx.getHighlightsModule());
    }

    /**
     * Test case for invalid CategoryId in ServiceProjectDTO
     */
    @Test
    public void testBuildMassageHighlightsInvalidCategoryId() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupServiceProjectDTO serviceProject = new DealGroupServiceProjectDTO();
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        // Invalid categoryId
        serviceProjectDTO.setCategoryId(999L);
        serviceProject.setMustGroups(Lists.newArrayList(new MustServiceProjectGroupDTO()));
        serviceProject.getMustGroups().get(0).setGroups(Lists.newArrayList(serviceProjectDTO));
        dealGroupDTO.setServiceProject(serviceProject);
        DealCtx ctx = new DealCtx(new EnvCtx());
        // No need to mock the behavior of MassageFactory, use the static field directly
        Set<Long> invalidCategoryIds = new HashSet<>();
        // act
        processor.buildMassageHighlights(dealGroupDTO, ctx);
        // assert
        assertNull(ctx.getHighlightsModule());
    }
}
