package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights;

import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.BeautyTechGroupService;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.CommonAttrVO;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.OptionalServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * 测试BeautyHighlightsV2Processor的getWearableNailAttrs方法
 */
@RunWith(MockitoJUnitRunner.class)
public class BeautyHighlightsV2ProcessorGetHighlightsContentTest {

    @InjectMocks
    private BeautyHighlightsV2Processor processor;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @Mock
    private DealGroupCategoryDTO categoryDTO;

    @Mock
    private BeautyTechGroupService beautyTechGroupService;

    private List<ServiceProjectDTO> invokePrivateGetServiceProject(DealGroupDTO groupDTO) throws Exception {
        Method method = BeautyHighlightsV2Processor.class.getDeclaredMethod("getServiceProject", DealGroupDTO.class);
        method.setAccessible(true);
        return (List<ServiceProjectDTO>) method.invoke(processor, groupDTO);
    }

    private String getHighlightsConfig(DealCtx ctx, Map<String, String> config) {
        DealGroupCategoryDTO categoryDTO = dealCtx.getDealGroupDTO().getCategory();
        if (Objects.isNull(categoryDTO)) {
            return StringUtils.EMPTY;
        }
        String categoryIdKey = String.valueOf(categoryDTO.getCategoryId());
        String serviceTypeKey = Joiner.on(getDot()).join(categoryIdKey, categoryDTO.getServiceType());
        String value = config.get(serviceTypeKey);
        return StringUtils.isEmpty(value) ? config.get(categoryIdKey) : value;
    }

    protected String getHighlightsStyle(DealCtx ctx) {
        // Replace with actual logic if needed
        Map<String, String> highlightStyle = new HashMap<>();
        return getHighlightsConfig(dealCtx, highlightStyle);
    }

    private String getDot() {
        try {
            Field dotField = BeautyHighlightsV2Processor.class.getDeclaredField("DOT");
            dotField.setAccessible(true);
            return (String) dotField.get(null);
        } catch (Exception e) {
            throw new RuntimeException("Failed to access DOT field", e);
        }
    }

    private List<CommonAttrVO> invokePrivateMethod(BeautyHighlightsV2Processor processor, String methodName, DealCtx ctx) throws Throwable {
        Method method = BeautyHighlightsV2Processor.class.getDeclaredMethod(methodName, DealCtx.class);
        method.setAccessible(true);
        return (List<CommonAttrVO>) method.invoke(processor, ctx);
    }

    /**
     * Helper method to create DealGroupTagDTO
     */
    private DealGroupTagDTO createDealGroupTagDTO(Long id) {
        DealGroupTagDTO tag = new DealGroupTagDTO();
        tag.setId(id);
        return tag;
    }

    /**
     * Helper method to invoke the private getDealTags method using reflection
     */
    private List<Long> invokePrivateGetDealTags(DealGroupDTO dealGroup) throws Exception {
        Method method = BeautyHighlightsV2Processor.class.getDeclaredMethod("getDealTags", DealGroupDTO.class);
        method.setAccessible(true);
        return (List<Long>) method.invoke(processor, dealGroup);
    }

    /**
     * 测试当dealGroupDTO中的isFreeWearingAtStore属性为true时
     */
    @Test
    public void testGetWearableNailAttrs_WhenIsFreeWearingAtStoreIsTrue() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("isFreeWearingAtStore");
        attr1.setValue(Lists.newArrayList("true"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("wearingNailsType");
        attr2.setValue(Lists.newArrayList("机器穿戴甲"));
        attrs.add(attr2);
        AttrDTO attr3 = new AttrDTO();
        attr3.setName("nail_additional_item");
        attr3.setValue(Lists.newArrayList("[\"赠佩戴工具包\"]"));
        attrs.add(attr3);
        dealGroupDTO.setAttrs(attrs);
        EnvCtx envCtx = new EnvCtx();
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setDealGroupDTO(dealGroupDTO);
        List<CommonAttrVO> result = processor.getWearableNailAttrs(ctx);
        assertNotNull(result);
        assertEquals(3, result.size());
    }

    /**
     * Test when ServiceProject is null
     */
    @Test
    public void testGetServiceProject_WhenServiceProjectIsNull() throws Throwable {
        // arrange
        DealGroupDTO groupDTO = mock(DealGroupDTO.class);
        when(groupDTO.getServiceProject()).thenReturn(null);
        // act
        List<ServiceProjectDTO> result = invokePrivateGetServiceProject(groupDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when both must groups and option groups are null
     */
    @Test
    public void testGetServiceProject_WhenBothGroupsAreNull() throws Throwable {
        // arrange
        DealGroupDTO groupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        when(groupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(null);
        when(serviceProject.getOptionGroups()).thenReturn(null);
        // act
        List<ServiceProjectDTO> result = invokePrivateGetServiceProject(groupDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when must groups contains valid data but option groups is empty
     */
    @Test
    public void testGetServiceProject_WhenOnlyMustGroupsHasData() throws Throwable {
        // arrange
        DealGroupDTO groupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        ServiceProjectDTO serviceProjectDTO = mock(ServiceProjectDTO.class);
        when(groupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(Lists.newArrayList(mustGroup));
        when(serviceProject.getOptionGroups()).thenReturn(null);
        when(mustGroup.getGroups()).thenReturn(Lists.newArrayList(serviceProjectDTO));
        // act
        List<ServiceProjectDTO> result = invokePrivateGetServiceProject(groupDTO);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(serviceProjectDTO, result.get(0));
    }

    /**
     * Test when option groups contains valid data but must groups is empty
     */
    @Test
    public void testGetServiceProject_WhenOnlyOptionGroupsHasData() throws Throwable {
        // arrange
        DealGroupDTO groupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        OptionalServiceProjectGroupDTO optionGroup = mock(OptionalServiceProjectGroupDTO.class);
        ServiceProjectDTO serviceProjectDTO = mock(ServiceProjectDTO.class);
        when(groupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(null);
        when(serviceProject.getOptionGroups()).thenReturn(Lists.newArrayList(optionGroup));
        when(optionGroup.getGroups()).thenReturn(Lists.newArrayList(serviceProjectDTO));
        // act
        List<ServiceProjectDTO> result = invokePrivateGetServiceProject(groupDTO);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(serviceProjectDTO, result.get(0));
    }

    /**
     * Test when both groups contain valid data
     */
    @Test
    public void testGetServiceProject_WhenBothGroupsHaveData() throws Throwable {
        // arrange
        DealGroupDTO groupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        OptionalServiceProjectGroupDTO optionGroup = mock(OptionalServiceProjectGroupDTO.class);
        ServiceProjectDTO mustServiceProjectDTO = mock(ServiceProjectDTO.class);
        ServiceProjectDTO optionServiceProjectDTO = mock(ServiceProjectDTO.class);
        when(groupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(Lists.newArrayList(mustGroup));
        when(serviceProject.getOptionGroups()).thenReturn(Lists.newArrayList(optionGroup));
        when(mustGroup.getGroups()).thenReturn(Lists.newArrayList(mustServiceProjectDTO));
        when(optionGroup.getGroups()).thenReturn(Lists.newArrayList(optionServiceProjectDTO));
        // act
        List<ServiceProjectDTO> result = invokePrivateGetServiceProject(groupDTO);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(mustServiceProjectDTO));
        assertTrue(result.contains(optionServiceProjectDTO));
    }

    /**
     * Test when groups contain null elements
     */
    @Test
    public void testGetServiceProject_WhenGroupsContainNullElements() throws Throwable {
        // arrange
        DealGroupDTO groupDTO = mock(DealGroupDTO.class);
        DealGroupServiceProjectDTO serviceProject = mock(DealGroupServiceProjectDTO.class);
        MustServiceProjectGroupDTO mustGroup = mock(MustServiceProjectGroupDTO.class);
        OptionalServiceProjectGroupDTO optionGroup = mock(OptionalServiceProjectGroupDTO.class);
        when(groupDTO.getServiceProject()).thenReturn(serviceProject);
        when(serviceProject.getMustGroups()).thenReturn(Lists.newArrayList(null, mustGroup));
        when(serviceProject.getOptionGroups()).thenReturn(Lists.newArrayList(null, optionGroup));
        when(mustGroup.getGroups()).thenReturn(null);
        when(optionGroup.getGroups()).thenReturn(null);
        // act
        List<ServiceProjectDTO> result = invokePrivateGetServiceProject(groupDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case: When category is null
     * Expected: Should return empty string
     */
    @Test
    public void testGetHighlightsStyle_WhenCategoryIsNull() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        // act
        String result = processor.getHighlightsStyle(dealCtx);
        // assert
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * Test case for null category
     */
    @Test
    public void testGetHighlightsContent_NullCategory() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(null);
        // act
        String result = processor.getHighlightsContent(dealCtx);
        // assert
        assertNull(result);
    }

    /**
     * Test case for hair care service
     */
    @Test
    public void testGetHighlightsContent_HairCare() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(501L);
        when(categoryDTO.getServiceType()).thenReturn("护理");
        // act
        String result = processor.getHighlightsContent(dealCtx);
        // assert
        // Since getNursingContent returns null by default
        assertNull(result);
    }

    /**
     * Test case for nail service with tag 100075023
     */
    @Test
    public void testGetHighlightsContent_NailServiceWithTag100075023() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(502L);
        when(categoryDTO.getServiceType()).thenReturn("美甲");
        DealGroupTagDTO tag = new DealGroupTagDTO();
        tag.setId(100075023L);
        // act
        String result = processor.getHighlightsContent(dealCtx);
        // assert
        // Since getNailContent1 returns null by default
        assertNull(result);
    }

    /**
     * Test case for nail service with tag 100069628
     */
    @Test
    public void testGetHighlightsContent_NailServiceWithTag100069628() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(502L);
        when(categoryDTO.getServiceType()).thenReturn("美甲");
        DealGroupTagDTO tag = new DealGroupTagDTO();
        tag.setId(100069628L);
        // act
        String result = processor.getHighlightsContent(dealCtx);
        // assert
        // Since getNailContent2 returns null by default
        assertNull(result);
    }

    /**
     * Test case for eyelash service
     */
    @Test
    public void testGetHighlightsContent_EyelashService() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(502L);
        when(categoryDTO.getServiceType()).thenReturn("美睫");
        // act
        String result = processor.getHighlightsContent(dealCtx);
        // assert
        // Since getEyelashesContent returns null by default
        assertNull(result);
    }

    /**
     * Test case for beauty SPA service
     */
    @Test
    public void testGetHighlightsContent_BeautySpaService() throws Throwable {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(503L);
        when(categoryDTO.getServiceType()).thenReturn("SPA");
        // act
        String result = processor.getHighlightsContent(dealCtx);
        // assert
        // Since getOtherContent returns null by default
        assertNull(result);
    }

    /**
     * Test case: No attributes exist
     * Expected: Returns empty list
     */
    @Test
    public void testGetHotDyeingAttrs_NoAttributesExist() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        DealGroupDTO dealGroup = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroup);
        // Mock empty data
        // act
        List<CommonAttrVO> result = invokePrivateMethod(processor, "getHotDyeingAttrs", ctx);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case: Only one attribute exists
     * Expected: Returns empty list (due to size >= 2 requirement)
     */
    @Test
    public void testGetHotDyeingAttrs_OnlyOneAttributeExists() throws Throwable {
        // arrange
        DealCtx ctx = new DealCtx(null);
        DealGroupDTO dealGroup = new DealGroupDTO();
        ctx.setDealGroupDTO(dealGroup);
        // Mock only product owner exists
        // act
        List<CommonAttrVO> result = invokePrivateMethod(processor, "getHotDyeingAttrs", ctx);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when all 3 attributes are present and valid
     */
    @Test
    public void testGetWearableNailAttrs_AllAttributesPresent() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("isFreeWearingAtStore");
        attr1.setValue(Arrays.asList("true"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("wearingNailsType");
        attr2.setValue(Arrays.asList("Type1"));
        attrs.add(attr2);
        AttrDTO attr3 = new AttrDTO();
        attr3.setName("nail_additional_item");
        attr3.setValue(Arrays.asList("Item1"));
        attrs.add(attr3);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        // act
        List<CommonAttrVO> result = processor.getWearableNailAttrs(dealCtx);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("增值服务", result.get(0).getName());
        assertEquals("到店免费佩戴", result.get(0).getValue());
        assertEquals("穿戴甲类型", result.get(1).getName());
        assertEquals("Type1", result.get(1).getValue());
        assertEquals("附赠项目", result.get(2).getName());
        assertEquals("Item1", result.get(2).getValue());
    }

    /**
     * 测试当dealGroupDTO中的isFreeWearingAtStore属性为true时
     */
    /**
     * Test when only 2 attributes are present
     */
    @Test
    public void testGetWearableNailAttrs_TwoAttributesPresent() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("isFreeWearingAtStore");
        attr1.setValue(Arrays.asList("true"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("wearingNailsType");
        attr2.setValue(Arrays.asList("Type1"));
        attrs.add(attr2);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        // act
        List<CommonAttrVO> result = processor.getWearableNailAttrs(dealCtx);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    /**
     * Test when only 1 attribute is present
     */
    @Test
    public void testGetWearableNailAttrs_OneAttributePresent() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("isFreeWearingAtStore");
        attr1.setValue(Arrays.asList("true"));
        attrs.add(attr1);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        // act
        List<CommonAttrVO> result = processor.getWearableNailAttrs(dealCtx);
        // assert
        assertNull(result);
    }

    /**
     * Test when nail_additional_item is in JSON array format
     */
    @Test
    public void testGetWearableNailAttrs_JsonArrayAdditionalItem() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("isFreeWearingAtStore");
        attr1.setValue(Arrays.asList("true"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("wearingNailsType");
        attr2.setValue(Arrays.asList("Type1"));
        attrs.add(attr2);
        AttrDTO attr3 = new AttrDTO();
        attr3.setName("nail_additional_item");
        attr3.setValue(Arrays.asList("[\"Item1\",\"Item2\"]"));
        attrs.add(attr3);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        // act
        List<CommonAttrVO> result = processor.getWearableNailAttrs(dealCtx);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("附赠项目", result.get(2).getName());
        assertEquals("Item1", result.get(2).getValue());
    }

    /**
     * Test when no attributes are present
     */
    @Test
    public void testGetWearableNailAttrs_NoAttributes() {
        // arrange
        when(dealCtx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(new ArrayList<>());
        // act
        List<CommonAttrVO> result = processor.getWearableNailAttrs(dealCtx);
        // assert
        assertNull(result);
    }

    /**
     * Test case for null dealGroup input
     */
    @Test
    public void testGetDealTags_NullDealGroup() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = null;
        // act
        List<Long> result = invokePrivateGetDealTags(dealGroup);
        // assert
        assertTrue("Should return empty list for null dealGroup", result.isEmpty());
    }

    /**
     * Test case for dealGroup with null tags list
     */
    @Test
    public void testGetDealTags_NullTagsList() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setTags(null);
        // act
        List<Long> result = invokePrivateGetDealTags(dealGroup);
        // assert
        assertTrue("Should return empty list for null tags", result.isEmpty());
    }

    /**
     * Test case for dealGroup with empty tags list
     */
    @Test
    public void testGetDealTags_EmptyTagsList() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setTags(new ArrayList<>());
        // act
        List<Long> result = invokePrivateGetDealTags(dealGroup);
        // assert
        assertTrue("Should return empty list for empty tags", result.isEmpty());
    }

    /**
     * Test case for dealGroup with non-duplicate tags
     */
    @Test
    public void testGetDealTags_NonDuplicateTags() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        List<DealGroupTagDTO> tags = Arrays.asList(createDealGroupTagDTO(1L), createDealGroupTagDTO(2L), createDealGroupTagDTO(3L));
        dealGroup.setTags(tags);
        // act
        List<Long> result = invokePrivateGetDealTags(dealGroup);
        // assert
        assertEquals("Should return list with all tag IDs", Arrays.asList(1L, 2L, 3L), result);
    }

    /**
     * Test case for dealGroup with duplicate tags
     */
    @Test
    public void testGetDealTags_DuplicateTags() throws Throwable {
        // arrange
        DealGroupDTO dealGroup = new DealGroupDTO();
        List<DealGroupTagDTO> tags = Arrays.asList(createDealGroupTagDTO(1L), createDealGroupTagDTO(2L), createDealGroupTagDTO(1L), createDealGroupTagDTO(2L));
        dealGroup.setTags(tags);
        // act
        List<Long> result = invokePrivateGetDealTags(dealGroup);
        // assert
        assertEquals("Should return list with distinct tag IDs", Arrays.asList(1L, 2L), result);
    }
}
