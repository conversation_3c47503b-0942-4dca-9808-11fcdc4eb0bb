package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ReserveProductWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.RequestSourceEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.PriceContext;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealGroupPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.SubTitleVO;
import com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.DealRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.ReadjustPriceRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.standardPriceRule.StandardPriceRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.standardPriceRule.StandardPriceRuleItemDTO;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
// Added import
import java.util.Objects;
import org.junit.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 * @since 2023/9/26 14:20
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ Lion.class })
public class DealBuilderProcessorTest {

    @InjectMocks
    private DealBuilderProcessor dealBuilderProcessor;

    private DealCtx ctx;

    private PriceContext priceContext;

    private DealBuyBtn onlyDealBuyBtn;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ctx = Mockito.mock(DealCtx.class);
        priceContext = Mockito.mock(PriceContext.class);
        onlyDealBuyBtn = new DealBuyBtn(true, "测试按钮");
        PowerMockito.mockStatic(Lion.class);
    }



    private String invokeAppendFeatureTag(String featureTag, String value) throws Exception {
        // Use reflection to access the private method
        Method method = DealBuilderProcessor.class.getDeclaredMethod("appendFeatureTag", String.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(null, featureTag, value);
    }

    // Helper method to invoke private method using reflection
    private boolean invokePrivateMethod(DealBuilderProcessor processor, String methodName, DealGroupDealDTO dealDTO) throws Exception {
        Method method = DealBuilderProcessor.class.getDeclaredMethod(methodName, DealGroupDealDTO.class);
        method.setAccessible(true);
        return (boolean) method.invoke(processor, dealDTO);
    }

    private static void putDealName(DealCtx ctx, DealGroupPBO result) {
        if (Objects.isNull(ctx.getDealGroupDTO()) || Objects.isNull(ctx.getDealGroupDTO().getBasic())) {
            return;
        }
        result.setDealName(ctx.getDealGroupDTO().getBasic().getTitle());
    }

    /**
     * Helper method to invoke private method using reflection
     */
    private Boolean invokePrivateMethod(String methodName, String requestSource) throws Exception {
        Method method = DealBuilderProcessor.class.getDeclaredMethod(methodName, String.class);
        method.setAccessible(true);
        return (Boolean) method.invoke(dealBuilderProcessor, requestSource);
    }

    /**
     * 测试 setWuyoutongPromo 方法
     */
    @Test
    public void testSetWuyoutongPromo() {
        // arrange
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModule.setShowMarketPrice(true);
        promoDetailModule.setMarketPricePromo("test");
        promoDetailModule.setShowBestPromoDetails(true);
        promoDetailModule.setMarketPrice("test");
        promoDetailModule.setMarketPromoDiscount("test");
        // act
        dealBuilderProcessor.setWuyoutongPromo(promoDetailModule);
        // assert
        assertFalse(promoDetailModule.isShowMarketPrice());
        assertEquals("", promoDetailModule.getMarketPricePromo());
        assertFalse(promoDetailModule.isShowBestPromoDetails());
        assertNull(promoDetailModule.getBestPromoDetails());
        assertEquals("", promoDetailModule.getMarketPrice());
        assertEquals("", promoDetailModule.getMarketPromoDiscount());
    }

    @Test
    public void testReserveAfterPurchase_CategoriesIsEmptyAndCategoryIdNotInCategories() throws Throwable {
        int categoryId = 1;
        when(Lion.getList(eq("availableCategoryIds"), eq(Integer.class), anyList())).thenAnswer((Answer<List<Integer>>) invocation -> Collections.emptyList());
        boolean result = ReserveProductWrapper.reserveAfterPurchase(categoryId);
        assertFalse("The result should be false when categories are empty", result);
    }

    @Test
    public void testReserveAfterPurchase_CategoriesIsNotEmptyAndCategoryIdNotInCategories() throws Throwable {
        int categoryId = 1;
        when(Lion.getList(eq("availableCategoryIds"), eq(Integer.class), anyList())).thenAnswer((Answer<List<Integer>>) invocation -> Arrays.asList(2));
        boolean result = ReserveProductWrapper.reserveAfterPurchase(categoryId);
        assertFalse("The result should be false when the category ID is not in the list", result);
    }

    @Test
    public void testBuildSubTitleList() throws InvocationTargetException, IllegalAccessException {
        Method method = PowerMockito.method(DealBuilderProcessor.class, "buildSubTitleList");
        when(ctx.getCategoryId()).thenReturn(300);
        when(ctx.getAutoOpenTable()).thenReturn(true);
        List<SubTitleVO> result = (List<SubTitleVO>) method.invoke(dealBuilderProcessor, ctx);
        assert !result.isEmpty();
    }



    /**
     * 测试场景：featureTag 不为空，追加 " | " 和 value
     */
    @Test
    public void testAppendFeatureTagFeatureTagNotEmpty() throws Throwable {
        // arrange
        String featureTag = "tag";
        String value = "value";
        // act
        String result = invokeAppendFeatureTag(featureTag, value);
        // assert
        assertEquals("tag | value", result);
    }

    /**
     * 测试场景：value 为空，featureTag 为空，返回空字符串
     */
    @Test
    public void testAppendFeatureTagValueEmptyFeatureTagEmpty() throws Throwable {
        // arrange
        String featureTag = "";
        String value = "";
        // act
        String result = invokeAppendFeatureTag(featureTag, value);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试场景：value 为空，featureTag 不为空，返回 featureTag 加上分隔符
     */
    @Test
    public void testAppendFeatureTagValueEmptyFeatureTagNotEmpty() throws Throwable {
        // arrange
        String featureTag = "tag";
        String value = "";
        // act
        String result = invokeAppendFeatureTag(featureTag, value);
        // assert
        assertEquals("tag | ", result);
    }

    /**
     * 测试场景：featureTag 和 value 都为空，返回空字符串
     */
    @Test
    public void testAppendFeatureTagBothEmpty() throws Throwable {
        // arrange
        String featureTag = "";
        String value = "";
        // act
        String result = invokeAppendFeatureTag(featureTag, value);
        // assert
        assertEquals("", result);
    }
    /**
     * 测试场景：featureTag 为空，直接返回 value
     */
    @Test
    public void testAppendFeatureTagFeatureTagEmpty() throws Throwable {
        // arrange
        String featureTag = "";
        String value = "value";
        // act
        String result = invokeAppendFeatureTag(featureTag, value);
        // assert
        assertEquals("value", result);
    }


    /**
     * Test case: dealDTO is null
     * Expected: should return false
     */
    @Test
    public void testHasNotFreeOverNightServiceInDeal_WhenDealDTOIsNull() throws Throwable {
        // arrange
        DealGroupDealDTO dealDTO = null;
        // act
        boolean result = invokePrivateMethod(dealBuilderProcessor, "hasNotFreeOverNightServiceInDeal", dealDTO);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: dealDTO.getRule() is null
     * Expected: should return false
     */
    @Test
    public void testHasNotFreeOverNightServiceInDeal_WhenRuleIsNull() throws Throwable {
        // arrange
        DealGroupDealDTO dealDTO = new DealGroupDealDTO();
        // act
        boolean result = invokePrivateMethod(dealBuilderProcessor, "hasNotFreeOverNightServiceInDeal", dealDTO);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: deal has empty readjustPriceRules
     * Expected: should return false
     */
    @Test
    public void testHasNotFreeOverNightServiceInDeal_WhenReadjustPriceRulesIsEmpty() throws Throwable {
        // arrange
        DealGroupDealDTO dealDTO = new DealGroupDealDTO();
        DealRuleDTO ruleDTO = new DealRuleDTO();
        ruleDTO.setReadjustPriceRules(Collections.emptyList());
        dealDTO.setRule(ruleDTO);
        // act
        boolean result = invokePrivateMethod(dealBuilderProcessor, "hasNotFreeOverNightServiceInDeal", dealDTO);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: deal has multiple readjustPriceRules but none have not-free overnight service
     * Expected: should return false
     */
    @Test
    public void testHasNotFreeOverNightServiceInDeal_WhenNoNotFreeOverNightService() throws Throwable {
        // arrange
        DealGroupDealDTO dealDTO = new DealGroupDealDTO();
        DealRuleDTO ruleDTO = new DealRuleDTO();
        List<ReadjustPriceRuleDTO> readjustPriceRules = new ArrayList<>();
        // Create first rule
        ReadjustPriceRuleDTO rule1 = new ReadjustPriceRuleDTO();
        StandardPriceRuleDTO standardPriceRule1 = new StandardPriceRuleDTO();
        StandardPriceRuleItemDTO item1 = new StandardPriceRuleItemDTO();
        // Not overnight service
        // Assuming the service is not free
        standardPriceRule1.setStandardPriceRuleItems(Collections.singletonList(item1));
        rule1.setStandardPriceRule(standardPriceRule1);
        // Create second rule
        ReadjustPriceRuleDTO rule2 = new ReadjustPriceRuleDTO();
        StandardPriceRuleDTO standardPriceRule2 = new StandardPriceRuleDTO();
        StandardPriceRuleItemDTO item2 = new StandardPriceRuleItemDTO();
        // Overnight service
        // But it's free
        standardPriceRule2.setStandardPriceRuleItems(Collections.singletonList(item2));
        rule2.setStandardPriceRule(standardPriceRule2);
        readjustPriceRules.add(rule1);
        readjustPriceRules.add(rule2);
        ruleDTO.setReadjustPriceRules(readjustPriceRules);
        dealDTO.setRule(ruleDTO);
        // act
        boolean result = invokePrivateMethod(dealBuilderProcessor, "hasNotFreeOverNightServiceInDeal", dealDTO);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for successful deal name setting
     */
    @Test
    public void testPutDealName_WithValidData() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DealGroupPBO result = new DealGroupPBO();
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupBasicDTO basicDTO = mock(DealGroupBasicDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getBasic()).thenReturn(basicDTO);
        when(basicDTO.getTitle()).thenReturn("Test Deal");
        // act
        Method method = DealBuilderProcessor.class.getDeclaredMethod("putDealName", DealCtx.class, DealGroupPBO.class);
        method.setAccessible(true);
        method.invoke(null, ctx, result);
        // assert
        assertEquals("Test Deal", result.getDealName());
    }

    /**
     * Test case for null DealGroupDTO
     */
    @Test
    public void testPutDealName_WithNullDealGroupDTO() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DealGroupPBO result = new DealGroupPBO();
        when(ctx.getDealGroupDTO()).thenReturn(null);
        // act
        Method method = DealBuilderProcessor.class.getDeclaredMethod("putDealName", DealCtx.class, DealGroupPBO.class);
        method.setAccessible(true);
        method.invoke(null, ctx, result);
        // assert
        assertNull(result.getDealName());
    }

    /**
     * Test case for DealGroupDTO with null Basic
     */
    @Test
    public void testPutDealName_WithNullBasic() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DealGroupPBO result = new DealGroupPBO();
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getBasic()).thenReturn(null);
        // act
        Method method = DealBuilderProcessor.class.getDeclaredMethod("putDealName", DealCtx.class, DealGroupPBO.class);
        method.setAccessible(true);
        method.invoke(null, ctx, result);
        // assert
        assertNull(result.getDealName());
    }

    /**
     * Test case for DealGroupDTO with null title in Basic
     */
    @Test
    public void testPutDealName_WithNullTitle() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        DealGroupPBO result = new DealGroupPBO();
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupBasicDTO basicDTO = mock(DealGroupBasicDTO.class);
        when(ctx.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getBasic()).thenReturn(basicDTO);
        when(basicDTO.getTitle()).thenReturn(null);
        // act
        Method method = DealBuilderProcessor.class.getDeclaredMethod("putDealName", DealCtx.class, DealGroupPBO.class);
        method.setAccessible(true);
        method.invoke(null, ctx, result);
        // assert
        assertNull(result.getDealName());
    }

    /**
     * Test when requestSource is "caixi"
     */
    @Test
    public void testFromCAIXI_WhenRequestSourceIsCaiXi() throws Throwable {
        // arrange
        String requestSource = "caixi";
        // act
        Boolean result = invokePrivateMethod("fromCAIXI", requestSource);
        // assert
        assertTrue("Should return true for caixi source", result);
    }

    /**
     * Test when requestSource is "homepage"
     */
    @Test
    public void testFromCAIXI_WhenRequestSourceIsHomePage() throws Throwable {
        // arrange
        String requestSource = "homepage";
        // act
        Boolean result = invokePrivateMethod("fromCAIXI", requestSource);
        // assert
        assertTrue("Should return true for homepage source", result);
    }

    /**
     * Test when requestSource is null
     */
    @Test
    public void testFromCAIXI_WhenRequestSourceIsNull() throws Throwable {
        // arrange
        String requestSource = null;
        // act
        Boolean result = invokePrivateMethod("fromCAIXI", requestSource);
        // assert
        assertFalse("Should return false for null source", result);
    }

    /**
     * Test when requestSource is empty string
     */
    @Test
    public void testFromCAIXI_WhenRequestSourceIsEmpty() throws Throwable {
        // arrange
        String requestSource = "";
        // act
        Boolean result = invokePrivateMethod("fromCAIXI", requestSource);
        // assert
        assertFalse("Should return false for empty source", result);
    }

    /**
     * Test when requestSource is other valid string
     */
    @Test
    public void testFromCAIXI_WhenRequestSourceIsOtherString() throws Throwable {
        // arrange
        String requestSource = "other_source";
        // act
        Boolean result = invokePrivateMethod("fromCAIXI", requestSource);
        // assert
        assertFalse("Should return false for other source", result);
    }
}
