package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.datatype.req.UnifiedModuleExtraReq;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.UnifiedCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleExtraDTO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ModuleAbConfig;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.dianping.mobile.mapi.dztgdetail.biz.service.DouHuService;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.FreeDealUtils;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedModuleExtraFacade_BuildModuleExtraDTOTest {

    @InjectMocks
    private UnifiedModuleExtraFacade unifiedModuleExtraFacade;

    @Mock
    private DouHuService douHuService;

    @Mock
    private LionConfigUtils lionConfigUtils;

    @Mock
    private FreeDealUtils freeDealUtils;

    private UnifiedModuleExtraReq request;

    private UnifiedCtx unifiedCtx;

    private int publishCategoryId;

    private boolean isNewPhysicalExerciseModule;

    private boolean isStandardEyeDealGroup;

    private boolean isZeroResvDealGroup;

    private boolean isWearableNail;

    @Before
    public void setUp() {
        request = new UnifiedModuleExtraReq();
        unifiedCtx = new UnifiedCtx(new EnvCtx());
        publishCategoryId = 1;
        isNewPhysicalExerciseModule = false;
        isStandardEyeDealGroup = false;
        isZeroResvDealGroup = false;
        isWearableNail = false;
    }

    @Test
    public void testBuildModuleExtraDTOWithNegativePublishCategoryId() throws Throwable {
        publishCategoryId = -1;
        ModuleExtraDTO result = unifiedModuleExtraFacade.buildModuleExtraDTO(request, unifiedCtx, publishCategoryId, isNewPhysicalExerciseModule, isStandardEyeDealGroup, isZeroResvDealGroup, isWearableNail);
        assertNull(result);
    }
}
