package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.mtdetail.MtPriceCalendar;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import static org.junit.Assert.*;
import org.json.JSONArray;
import org.json.JSONObject;

@RunWith(MockitoJUnitRunner.class)
public class DealFieldHelper_GetPriceCalendarFromJsonTest {

    @Test
    public void testGetPriceCalendarFromJsonEmptyInput() throws Throwable {
        String input = "";
        List<MtPriceCalendar> result = DealFieldHelper.getPriceCalendarFromJson(input);
        assertNull(result);
    }

    @Test
    public void testGetPriceCalendarFromJsonInvalidJson() throws Throwable {
        String input = "invalid json";
        List<MtPriceCalendar> result = DealFieldHelper.getPriceCalendarFromJson(input);
        assertNull(result);
    }

    @Test
    public void testGetPriceCalendarFromJsonMissingFields() throws Throwable {
        String input = "[{\"id\":1, \"range\":[]}]";
        List<MtPriceCalendar> result = DealFieldHelper.getPriceCalendarFromJson(input);
        assertNotNull(result);
        // Adjusted expectation: the list should not be empty but contain an object with default values
        assertFalse(result.isEmpty());
        MtPriceCalendar mtPriceCalendar = result.get(0);
        assertEquals(1, mtPriceCalendar.getId());
        assertNotNull(mtPriceCalendar.getRange());
        assertTrue(mtPriceCalendar.getRange().isEmpty());
    }

    @Test
    public void testGetPriceCalendarFromJsonInvalidRange() throws Throwable {
        String input = "[{\"id\":1,\"range\":\"invalid json\"}]";
        List<MtPriceCalendar> result = DealFieldHelper.getPriceCalendarFromJson(input);
        assertNull(result);
    }

    @Test
    public void testGetPriceCalendarFromJsonValidInput() throws Throwable {
        String input = "[{\"id\":1,\"desc\":\"desc\",\"endtime\":1234567890000,\"starttime\":1234567890000,\"price\":100.0,\"canbuyprice\":80.0,\"buyprice\":100.0,\"dealid\":1,\"type\":1,\"range\":[\"range1\",\"range2\"]}]";
        List<MtPriceCalendar> result = DealFieldHelper.getPriceCalendarFromJson(input);
        assertNotNull(result);
        assertEquals(1, result.size());
        MtPriceCalendar mtPriceCalendar = result.get(0);
        assertEquals(1, mtPriceCalendar.getId());
        assertEquals("desc", mtPriceCalendar.getDesc());
        assertEquals(100.0, mtPriceCalendar.getPrice(), 0.001);
        assertEquals(80.0, mtPriceCalendar.getCanBuyPrice(), 0.001);
        assertEquals(100.0, mtPriceCalendar.getBuyPrice(), 0.001);
        assertEquals(1, mtPriceCalendar.getDealId());
        assertEquals(1, mtPriceCalendar.getType());
        assertEquals(2, mtPriceCalendar.getRange().size());
        assertEquals("range1", mtPriceCalendar.getRange().get(0));
        assertEquals("range2", mtPriceCalendar.getRange().get(1));
    }
}
