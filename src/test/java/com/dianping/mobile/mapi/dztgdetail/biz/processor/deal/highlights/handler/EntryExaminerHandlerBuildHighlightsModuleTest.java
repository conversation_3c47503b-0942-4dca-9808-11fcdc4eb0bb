package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.handler;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.highlights.model.ExaminerSuitableCrowd;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.highlights.DztgHighlightsModule;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class EntryExaminerHandlerBuildHighlightsModuleTest {

    @InjectMocks
    private EntryExaminerHandler entryExaminerHandler;

    @Mock
    private DealCtx ctx;

    @Test
    public void testBuildHighlightsModuleNormal() throws Throwable {
        double turnaroundTime = 1.5;
        ExaminerSuitableCrowd suitableCrowd = new ExaminerSuitableCrowd(Arrays.asList("青年", "中年"), Arrays.asList("18-25", "26-35"), Arrays.asList("特殊人群"), Arrays.asList("服务亮点1", "服务亮点2"));
        Method method = EntryExaminerHandler.class.getDeclaredMethod("buildHighlightsModule", double.class, ExaminerSuitableCrowd.class);
        method.setAccessible(true);
        DztgHighlightsModule result = (DztgHighlightsModule) method.invoke(entryExaminerHandler, turnaroundTime, suitableCrowd);
        assertNotNull(result);
        assertEquals("struct", result.getStyle());
        assertEquals(3, result.getAttrs().size());
    }

    @Test
    public void testBuildHighlightsModuleBoundary() throws Throwable {
        double turnaroundTime = 0;
        ExaminerSuitableCrowd suitableCrowd = new ExaminerSuitableCrowd(Collections.emptyList(), Collections.emptyList(), Collections.emptyList(), Collections.emptyList());
        Method method = EntryExaminerHandler.class.getDeclaredMethod("buildHighlightsModule", double.class, ExaminerSuitableCrowd.class);
        method.setAccessible(true);
        DztgHighlightsModule result = (DztgHighlightsModule) method.invoke(entryExaminerHandler, turnaroundTime, suitableCrowd);
        assertNotNull(result);
        assertEquals("struct", result.getStyle());
        assertFalse(result.getAttrs().isEmpty());
    }

    @Test
    public void testBuildHighlightsModuleException() throws Throwable {
        double turnaroundTime = -1;
        // Adjusted to not null to correctly trigger the expected exception
        ExaminerSuitableCrowd suitableCrowd = new ExaminerSuitableCrowd(null, null, null, null);
        Method method = EntryExaminerHandler.class.getDeclaredMethod("buildHighlightsModule", double.class, ExaminerSuitableCrowd.class);
        method.setAccessible(true);
        DztgHighlightsModule result = (DztgHighlightsModule) method.invoke(entryExaminerHandler, turnaroundTime, suitableCrowd);
        assertNotNull(result);
        // Additional assertions can be added here to verify the behavior when inputs are null
    }
}
