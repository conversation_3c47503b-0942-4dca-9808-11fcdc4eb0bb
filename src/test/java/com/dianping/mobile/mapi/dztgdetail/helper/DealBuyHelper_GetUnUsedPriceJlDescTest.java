package com.dianping.mobile.mapi.dztgdetail.helper;

import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.math.BigDecimal;
import java.util.List;
import com.dianping.json.facade.JsonFacade;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealBuyHelper_GetUnUsedPriceJlDescTest {

    @Test
    public void testGetUnUsedPriceJlDescPriceIsNull() throws Throwable {
        String desc = "test";
        BigDecimal price = null;
        boolean isApp = true;
        boolean strikeThrough = true;
        String result = DealBuyHelper.getUnUsedPriceJlDesc(desc, price, isApp, strikeThrough);
        assertEquals("[{\"text\":\"test\",\"textsize\":12,\"textcolor\":\"#FF999999\",\"backgroundcolor\":\"#00FFFFFF\",\"textstyle\":\"Default\",\"strikethrough\":false,\"underline\":false}]", result);
    }

    @Test
    public void testGetUnUsedPriceJlDescPriceIsNotNull() throws Throwable {
        String desc = "test";
        BigDecimal price = new BigDecimal("100.00");
        boolean isApp = true;
        boolean strikeThrough = true;
        String result = DealBuyHelper.getUnUsedPriceJlDesc(desc, price, isApp, strikeThrough);
        assertEquals("[{\"text\":\"test\",\"textsize\":12,\"textcolor\":\"#FF999999\",\"backgroundcolor\":\"#00FFFFFF\",\"textstyle\":\"Default\",\"strikethrough\":false,\"underline\":false},{\"text\":\"100\",\"textsize\":12,\"textcolor\":\"#FF999999\",\"backgroundcolor\":\"#00FFFFFF\",\"textstyle\":\"Default\",\"strikethrough\":false,\"underline\":false}]", result);
    }

    @Test
    public void testGetUnUsedPriceJlDescIsAppIsTrue() throws Throwable {
        String desc = "test";
        BigDecimal price = new BigDecimal("100.00");
        boolean isApp = true;
        boolean strikeThrough = true;
        String result = DealBuyHelper.getUnUsedPriceJlDesc(desc, price, isApp, strikeThrough);
        assertEquals("[{\"text\":\"test\",\"textsize\":12,\"textcolor\":\"#FF999999\",\"backgroundcolor\":\"#00FFFFFF\",\"textstyle\":\"Default\",\"strikethrough\":false,\"underline\":false},{\"text\":\"100\",\"textsize\":12,\"textcolor\":\"#FF999999\",\"backgroundcolor\":\"#00FFFFFF\",\"textstyle\":\"Default\",\"strikethrough\":false,\"underline\":false}]", result);
    }

    @Test
    public void testGetUnUsedPriceJlDescIsAppIsFalse() throws Throwable {
        String desc = "test";
        BigDecimal price = new BigDecimal("100.00");
        boolean isApp = false;
        boolean strikeThrough = true;
        String result = DealBuyHelper.getUnUsedPriceJlDesc(desc, price, isApp, strikeThrough);
        assertEquals("[{\"text\":\"test\",\"textsize\":12,\"textcolor\":\"#999999\",\"backgroundcolor\":\"#FFFFFF\",\"textstyle\":\"Default\",\"strikethrough\":false,\"underline\":false},{\"text\":\"100\",\"textsize\":12,\"textcolor\":\"#999999\",\"backgroundcolor\":\"#FFFFFF\",\"textstyle\":\"Default\",\"strikethrough\":false,\"underline\":false}]", result);
    }

    @Test
    public void testGetUnUsedPriceJlDescStrikeThroughIsTrue() throws Throwable {
        String desc = "test";
        BigDecimal price = new BigDecimal("100.00");
        boolean isApp = true;
        boolean strikeThrough = true;
        String result = DealBuyHelper.getUnUsedPriceJlDesc(desc, price, isApp, strikeThrough);
        assertEquals("[{\"text\":\"test\",\"textsize\":12,\"textcolor\":\"#FF999999\",\"backgroundcolor\":\"#00FFFFFF\",\"textstyle\":\"Default\",\"strikethrough\":false,\"underline\":false},{\"text\":\"100\",\"textsize\":12,\"textcolor\":\"#FF999999\",\"backgroundcolor\":\"#00FFFFFF\",\"textstyle\":\"Default\",\"strikethrough\":false,\"underline\":false}]", result);
    }

    @Test
    public void testGetUnUsedPriceJlDescStrikeThroughIsFalse() throws Throwable {
        String desc = "test";
        BigDecimal price = new BigDecimal("100.00");
        boolean isApp = true;
        boolean strikeThrough = false;
        String result = DealBuyHelper.getUnUsedPriceJlDesc(desc, price, isApp, strikeThrough);
        assertEquals("[{\"text\":\"test\",\"textsize\":12,\"textcolor\":\"#FF999999\",\"backgroundcolor\":\"#00FFFFFF\",\"textstyle\":\"Default\",\"strikethrough\":false,\"underline\":false},{\"text\":\"100\",\"textsize\":12,\"textcolor\":\"#FF999999\",\"backgroundcolor\":\"#00FFFFFF\",\"textstyle\":\"Default\",\"strikethrough\":false,\"underline\":false}]", result);
    }
}
