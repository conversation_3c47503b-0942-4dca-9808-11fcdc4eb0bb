//package com.dianping.mobile.mapi.dztgdetail;
//
//import com.dianping.deal.base.dto.DealGroupBaseDTO;
//import com.dianping.deal.common.enums.ClientTypeEnum;
//import com.dianping.deal.publishcategory.dto.ChannelDTO;
//import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
//import com.dianping.deal.stock.dto.ProductGroupStock;
//import com.dianping.gm.marketing.member.card.api.dto.membercard.UserMemberCardInfoDTO;
//import com.dianping.mobile.mapi.dztgdetail.button.*;
//import com.dianping.mobile.mapi.dztgdetail.button.beauty.BeautyMemberCardButtonBuilder;
//import com.dianping.mobile.mapi.dztgdetail.button.joy.NewIdlePromoBannerBuilder;
//import com.dianping.mobile.mapi.dztgdetail.button.joy.NewIdlePromoButtonBuilder;
//import com.dianping.mobile.mapi.dztgdetail.common.enums.BuyBtnTypeEnum;
//import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
//import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
//import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBar;
//import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealBuyBtn;
//import com.dianping.mobile.mapi.dztgdetail.helper.DealBuyHelper;
//import com.dianping.pay.promo.common.bean.PromoTime;
//import com.dianping.pay.promo.common.enums.promo.PromoTimeFormatType;
//import com.dianping.pay.promo.common.enums.promo.SpecialPromoType;
//import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
//import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Sets;
//import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
//import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
//import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
//import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
//import com.sankuai.dealuser.price.display.api.model.PromoDTO;
//import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
//import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
//import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
//import org.apache.commons.lang.time.DateUtils;
//import org.junit.Test;
//
//import java.math.BigDecimal;
//import java.util.Date;
//import java.util.List;
//import java.util.Objects;
//
///**
// * <AUTHOR>
// * 2020/3/16 8:15 下午
// */
//public class BuyBarTests {
//
//    @Test
//    public void oneNormalButtonTest() {
//
//        DealGroupBaseDTO dealBase = buildDeal();
//
//        EnvCtx env = new EnvCtx();
//        env.setClientType(ClientTypeEnum.dp_mainApp_ios.getType());
//
//        DealCtx ctx = buildDealCtx(dealBase, null);
//
//        DealBuyBar buyBar = DealBuyHelper.buildBuyBar(ctx);
//
//        assert buyBar != null;
//        assert buyBar.getBuyBtns().size() == 1;
//        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("66");
//    }
//
//    private DealGroupBaseDTO buildDeal() {
//        DealGroupBaseDTO dealBase = new DealGroupBaseDTO();
//        dealBase.setMarketPrice(BigDecimal.valueOf(100));
//        dealBase.setDealGroupPrice(BigDecimal.valueOf(66));
//        dealBase.setBeginDate(new Date());
//        dealBase.setEndDate(DateUtils.addDays(new Date(), 100));
//        dealBase.setStatus(1);
//        return dealBase;
//    }
//
//    @Test
//    public void idleButtonTest() {
//
//        DealGroupBaseDTO dealBase = buildDeal();
//
//        PromoDisplayDTO displayDTO = buildIdleHoursPromo();
//
//        DealCtx ctx = buildDealCtx(dealBase, displayDTO);
//
//        DealBuyBar buyBar = DealBuyHelper.buildBuyBar(ctx);
//
//        assert buyBar != null;
//        assert buyBar.getBuyBtns().size() == 2;
//        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("55");
//        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("周二至周四可用");
//        assert buyBar.getBuyBtns().get(0).getRedirectUrl().contains("promosource=1");
//
//        displayDTO.getCanConsumeTime().setTimes(Lists.newArrayList("1"));
//
//        buyBar = DealBuyHelper.buildBuyBar(ctx);
//
//        assert buyBar != null;
//        assert buyBar.getBuyBtns().size() == 2;
//        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("55");
//        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("周一可用");
//        assert buyBar.getBuyBtns().get(0).getRedirectUrl().contains("promosource=1");
//    }
//
//    private DealCtx buildDealCtx(DealGroupBaseDTO dealBase, PromoDisplayDTO displayDTO) {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(dealBase);
//        ctx.getPromoList().add(displayDTO);
//        return ctx;
//    }
//
//    private DealCtx buildCtx() {
//        ProductGroupStock dealGroupStock = new ProductGroupStock();
//
//        EnvCtx env = new EnvCtx();
//        env.setVersion("10.28.0");
//        env.setClientType(ClientTypeEnum.dp_mainApp_ios.getType());
//
//        DealCtx ctx = new DealCtx(env);
//        ctx.setDealGroupStock(dealGroupStock);
//        ctx.setPromoList(Lists.newArrayList());
//        ctx.setMtId(122);
//        ctx.setDpId(122);
//        ctx.setMtShopId(123);
//        ctx.setDpShopId(123);
//        ctx.setPoiBackCategoryIds(Sets.newHashSet());
//        return ctx;
//    }
//
//    private PromoDisplayDTO buildIdleHoursPromo() {
//        PromoTime promoTime = new PromoTime();
//        promoTime.setType(PromoTimeFormatType.DAY_OF_WEEK.getCode());
//        promoTime.setTimes(Lists.newArrayList("2", "3", "4"));
//
//        PromoDisplayDTO displayDTO = new PromoDisplayDTO();
//        displayDTO.setDescription("限时优惠周二至周四");
//        displayDTO.setTag("已减11");
//        displayDTO.setEnable(true);
//        displayDTO.setPromoAmount(BigDecimal.valueOf(11));
//        displayDTO.setSpecialPromoType(SpecialPromoType.IDLETIMES_PROMO.getCode());
//        displayDTO.setCanConsumeTime(promoTime);
//        return displayDTO;
//    }
//
//    @Test
//    public void idleBannerWithTimesCardTest() {
//
//        DealGroupBaseDTO dealBase = buildDeal();
//
//        PromoDisplayDTO displayDTO = buildIdleHoursPromo();
//
//        DealCtx ctx = buildDealCtx(dealBase, displayDTO);
//        ctx.setTimesCard(buildTimesCard());
//
//        DealBuyBar buyBar = DealBuyHelper.buildBuyBar(ctx);
//
//        assert buyBar.getBuyType() == DealBuyBar.BuyType.TIMESCARD.type;
//        assert buyBar.getBuyBtns().size() == 2;
//        assert buyBar.getBuyBanner() != null;
//
//        assert buyBar.getBuyBtns().get(0).getBtnTitle().equals("购买3次");
//        assert buyBar.getBuyBtns().get(0).getPriceStr().equals("55");
//
//        assert buyBar.getBuyBanner().getContent().contains("周二至周四");
//    }
//
//    private CardSummaryBarDTO buildTimesCard() {
//        CardSummaryBarDTO card = new CardSummaryBarDTO();
//        card.setBtnTag("随时退");
//        card.setPrice(BigDecimal.valueOf(55));
//        card.setProductId(99999);
//        card.setTimes(3);
//        card.setChannelSource(1);
//
//        return card;
//    }
//
//    private PinProductBrief buildAssembleDeal() {
//        PinProductBrief result = new PinProductBrief();
//        result.setProductId(7777);
//        result.setPrice(new BigDecimal(44));
//        result.setUrl("http://pintuan");
//        result.setPinPersonNum(3);
//
//        return result;
//    }
//
//    private UserMemberCardInfoDTO buildMemberCard(boolean hold) {
//        UserMemberCardInfoDTO result = new UserMemberCardInfoDTO();
//        result.setDiscount(new BigDecimal("0.5"));
//        result.setUserHasMemberCard(hold);
//        return result;
//    }
//
//    private DealGroupChannelDTO buildDealChannel() {
//
//        ChannelDTO channelDTO = new ChannelDTO();
//        channelDTO.setChannelId(5);
//
//        DealGroupChannelDTO channel = new DealGroupChannelDTO();
//        channel.setCategoryId(501);
//        channel.setChannelDTO(channelDTO);
//
//        return channel;
//    }
//
//    private BuilderChainConfig buildChainConfig() {
//        BuilderChainConfig config = new BuilderChainConfig();
//        config.setMaxButtonSize(2);
//        config.setBuilderConfigs(Lists.newArrayList());
//
//        // 不可购买
//        config.getBuilderConfigs().add(buildButtonConfig(CanNotBuyButtonBuilder.class.getName(), null));
//
////        // 商户有卡，用户没卡
////        BuilderConfig a = buildButtonConfig(BeautyMemberCardButtonBuilder.class.getName(), null);
////        a.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
////        config.getBuilderConfigs().add(a);
//
//        // 拼团
//        config.getBuilderConfigs().add(buildButtonConfig(AssembleDealButtonBuilder.class.getName(), null));
//        // 次卡
//        config.getBuilderConfigs().add(buildButtonConfig(TimesCardButtonBuilder.class.getName(), null));
//
//        // 用户已经持有会员卡
//        BuilderConfig c = buildButtonConfig(BeautyMemberCardButtonBuilder.class.getName(),
//                Lists.newArrayList(BuyBtnTypeEnum.PINTUAN, BuyBtnTypeEnum.TIMES_CARD));
//        c.setBuildOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
//        config.getBuilderConfigs().add(c);
//
//        // 用户没有有会员卡
//        BuilderConfig g = buildButtonConfig(BeautyMemberCardButtonBuilder.class.getName(), null);
//        g.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
//        config.getBuilderConfigs().add(g);
//
//        // 闲时优惠
//        BuilderConfig f = buildButtonConfig(NewIdlePromoButtonBuilder.class.getName(), null);
//        config.getBuilderConfigs().add(f);
//
//        // 团购价, 用户不是会员
//        BuilderConfig d = buildButtonConfig(GeneralButtonBuilder.class.getName(), Lists.newArrayList());
//        ButtonStateConfig ds = new ButtonStateConfig();
//        ds.setButtonType(BuyBtnTypeEnum.MEMBER_CARD);
//        ds.setButtonStates(Lists.newArrayList(ButtonStateEnum.HOLD));
//        d.setExclusiveType(Lists.newArrayList(ds));
//        config.getBuilderConfigs().add(d);
//
//        // 闲时优惠banner
//        BuilderConfig e = buildButtonConfig(NewIdlePromoBannerBuilder.class.getName(), Lists.newArrayList());
//        config.getBuilderConfigs().add(e);
//
//        return config;
//    }
//
//    private BuilderConfig buildButtonConfig(String builderName, List<BuyBtnTypeEnum> inclusiveTypes) {
//        BuilderConfig config = new BuilderConfig();
//        config.setBuilderName(builderName);
//        if (inclusiveTypes == null) {
//            config.setExclusiveAll(true);
//        } else if (inclusiveTypes.isEmpty()) {
//            config.setInclusiveAll(true);
//        } else {
//            List<ButtonStateConfig> stateConfigs = Lists.newArrayList();
//            for (BuyBtnTypeEnum inclusiveType : inclusiveTypes) {
//                ButtonStateConfig buttonStateConfig = new ButtonStateConfig();
//                buttonStateConfig.setButtonType(inclusiveType);
//                stateConfigs.add(buttonStateConfig);
//            }
//            config.setInclusiveType(stateConfigs);
//        }
//
//        return config;
//    }
//
//    //-----------------拼团-------------------//
//
//    @Test
//    public void assembleDealRefactorTest() {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(buildDeal());
//        ctx.setPinProductBrief(buildAssembleDeal());
//        ctx.setChannelDTO(buildDealChannel());
//
//        testRefactor(ctx);
//    }
//
//    private void testRefactor(DealCtx ctx) {
//        BuilderChainConfig config;
//        if (ctx.getChannelDTO().getCategoryId() == 501) {
//            config = buildHairChainConfig();
//        } else {
//            config = buildChainConfig();
//        }
//        ButtonBuilderChain chain = ButtonBuilderFactory.newButtonBuilderChain(config);
//        chain.build(ctx);
//
//        DealBuyBar originBar = DealBuyHelper.buildBuyBar(ctx);
//        DealBuyBar newBar = ctx.getBuyBar();
//
//        assert originBar.getBuyType() == newBar.getBuyType();
//        assert originBar.getBuyBtns().size() == newBar.getBuyBtns().size();
//        assert Objects.equals(originBar.getBuyBanner(), newBar.getBuyBanner());
//
//        DealBuyBtn originBtn1 = originBar.getBuyBtns().get(0);
//        DealBuyBtn newBtn1 = newBar.getBuyBtns().get(0);
//        testButton(originBtn1, newBtn1);
//
//        if (originBar.getBuyBtns().size() > 1) {
//            DealBuyBtn originBtn2 = originBar.getBuyBtns().get(1);
//            DealBuyBtn newBtn2 = newBar.getBuyBtns().get(1);
//            testButton(originBtn2, newBtn2);
//        }
//    }
//
//    private void testButton(DealBuyBtn originBtn, DealBuyBtn newBtn) {
//        assert originBtn.getDetailBuyType() == newBtn.getDetailBuyType();
//        assert originBtn.getPriceStr().equals(newBtn.getPriceStr());
//        assert originBtn.getBtnTitle().equals(newBtn.getBtnTitle());
////        assert Objects.equals(originBtn.getBtnDesc(), newBtn.getBtnDesc());
//        assert Objects.equals(originBtn.getPricePrefix(), newBtn.getPricePrefix());
//        assert Objects.equals(originBtn.getPricePostfix(), newBtn.getPricePostfix());
////        assert Objects.equals(originBtn.getBtnTag(), newBtn.getBtnTag());
////        assert Objects.equals(originBtn.getBtnIcons(), newBtn.getBtnIcons());
//    }
//
//    @Test
//    public void assembleDealWithMemberCardRefactorTest() {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(buildDeal());
//        ctx.setPinProductBrief(buildAssembleDeal());
//        ctx.setChannelDTO(buildDealChannel());
//        ctx.setUserMemberCard(buildMemberCard(false));
//
//        testRefactor(ctx);
//    }
//
//    @Test
//    public void assembleDealWithMemberCardHoldRefactorTest() {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(buildDeal());
//        ctx.setPinProductBrief(buildAssembleDeal());
//        ctx.setChannelDTO(buildDealChannel());
//        ctx.setUserMemberCard(buildMemberCard(true));
//
//        ctx.getPriceContext().setMemberCard(buildNewMemberCard(true));
//        ctx.getPriceContext().setMemberCardPrice(buildNewMemberCardPrice(ctx.getDealGroupBase()));
//
//
//        testRefactor(ctx);
//    }
//
//    // ----------------次卡------------------//
//
//    @Test
//    public void timesCardRefactorTest() {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(buildDeal());
//        ctx.setChannelDTO(buildDealChannel());
//        ctx.setTimesCard(buildTimesCard());
//
//        testRefactor(ctx);
//    }
//
//    @Test
//    public void timesCardWithMemberCardRefactorTest() {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(buildDeal());
//        ctx.setChannelDTO(buildDealChannel());
//        ctx.setTimesCard(buildTimesCard());
//        ctx.setUserMemberCard(buildMemberCard(false));
//
//        testRefactor(ctx);
//    }
//
//    @Test
//    public void timesCardWithMemberCardHoldRefactorTest() {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(buildDeal());
//        ctx.setChannelDTO(buildDealChannel());
//        ctx.setTimesCard(buildTimesCard());
//        ctx.setUserMemberCard(buildMemberCard(true));
//
//        ctx.getPriceContext().setMemberCard(buildNewMemberCard(true));
//        ctx.getPriceContext().setMemberCardPrice(buildNewMemberCardPrice(ctx.getDealGroupBase()));
//
//        testRefactor(ctx);
//    }
//
//    //------------------会员卡-----------------//
//
//    @Test
//    public void memberCardRefactorTest() {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(buildDeal());
//        ctx.setChannelDTO(buildDealChannel());
//        ctx.setUserMemberCard(buildMemberCard(false));
//
//        ctx.getPriceContext().setMemberCard(buildNewMemberCard(false));
//        ctx.getPriceContext().setMemberCardPrice(buildNewMemberCardPrice(ctx.getDealGroupBase()));
//
//        testRefactor(ctx);
//    }
//
//    @Test
//    public void memberCardWithIdlePromoRefactorTest() {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(buildDeal());
//        ctx.setChannelDTO(buildDealChannel());
//        ctx.setUserMemberCard(buildMemberCard(false));
//        ctx.setPromoList(Lists.newArrayList(buildIdleHoursPromo()));
//
//        ctx.getPriceContext().setMemberCard(buildNewMemberCard(false));
//        ctx.getPriceContext().setMemberCardPrice(buildNewMemberCardPrice(ctx.getDealGroupBase()));
//
//        testRefactor(ctx);
//    }
//
//    @Test
//    public void memberCardHoldRefactorTest() {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(buildDeal());
//        ctx.setChannelDTO(buildDealChannel());
//        ctx.setUserMemberCard(buildMemberCard(true));
//
//        ctx.getPriceContext().setMemberCard(buildNewMemberCard(true));
//        ctx.getPriceContext().setMemberCardPrice(buildNewMemberCardPrice(ctx.getDealGroupBase()));
//
//        testRefactor(ctx);
//    }
//
//    @Test
//    public void memberCardHoldWithIdlePromoRefactorTest() {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(buildDeal());
//        ctx.setChannelDTO(buildDealChannel());
//        ctx.setUserMemberCard(buildMemberCard(true));
//        ctx.setPromoList(Lists.newArrayList(buildIdleHoursPromo()));
//
//        ctx.getPriceContext().setMemberCard(buildNewMemberCard(true));
//        ctx.getPriceContext().setMemberCardPrice(buildNewMemberCardPrice(ctx.getDealGroupBase()));
//
//        testRefactor(ctx);
//    }
//
//    //-----------------普通团购------------------//
//
//    @Test
//    public void normalRefactorTest() {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(buildDeal());
//        ctx.setChannelDTO(buildDealChannel());
//
//        testRefactor(ctx);
//    }
//
//    //-----------------不可购买团购------------------//
//
//    @Test
//    public void canNotBuyRefactorTest() {
//        DealCtx ctx = buildCtx();
//        ctx.setDealGroupBase(buildDeal());
//        ctx.setChannelDTO(buildDealChannel());
//
//        ctx.getDealGroupBase().setStatus(2);
//
//        testRefactor(ctx);
//    }
//
//    private BuilderChainConfig buildHairChainConfig() {
//        BuilderChainConfig config = new BuilderChainConfig();
//        config.setMaxButtonSize(2);
//        config.setBuilderConfigs(Lists.newArrayList());
//
//        // 不可购买
//        config.getBuilderConfigs().add(buildButtonConfig(CanNotBuyButtonBuilder.class.getName(), null));
//
//        // 商户有卡，用户没卡
////        BuilderConfig a = buildButtonConfig(MemberCardButtonBuilder.class.getName(), null);
////        a.setSkipOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
////        config.getBuilderConfigs().add(a);
//
//        // 拼团
//        config.getBuilderConfigs().add(buildButtonConfig(AssembleDealButtonBuilder.class.getName(), null));
//        // 次卡
//        config.getBuilderConfigs().add(buildButtonConfig(TimesCardButtonBuilder.class.getName(), null));
//
//        // 用户已经持有会员卡
////        BuilderConfig c = buildButtonConfig(MemberCardButtonBuilder.class.getName(),
////                Lists.newArrayList(BuyBtnTypeEnum.PINTUAN, BuyBtnTypeEnum.TIMES_CARD));
////        c.setBuildOnStatues(Lists.newArrayList(ButtonStateEnum.HOLD));
////        config.getBuilderConfigs().add(c);
//
//        // 闲时优惠
//        BuilderConfig f = buildButtonConfig(IdlePromoButtonBuilder.class.getName(), null);
//        config.getBuilderConfigs().add(f);
//
//        // 团购价, 用户不是会员
//        BuilderConfig d = buildButtonConfig(GeneralButtonBuilder.class.getName(), Lists.newArrayList());
//        ButtonStateConfig ds = new ButtonStateConfig();
//        ds.setButtonType(BuyBtnTypeEnum.MEMBER_CARD);
//        ds.setButtonStates(Lists.newArrayList(ButtonStateEnum.HOLD));
//        d.setExclusiveType(Lists.newArrayList(ds));
//        config.getBuilderConfigs().add(d);
//
//        // 闲时优惠banner
//        BuilderConfig e = buildButtonConfig(IdlePromoBannerBuilder.class.getName(), Lists.newArrayList());
//        config.getBuilderConfigs().add(e);
//
//        return config;
//    }
//
//    private PriceDisplayDTO buildNewMemberCardPrice(DealGroupBaseDTO dealGroupBaseDTO) {
//        PromoDTO promoDTO = new PromoDTO();
//        promoDTO.setIdentity(new PromoIdentity(0, PromoTypeEnum.DISCOUNT_CARD.getType()));
//        promoDTO.setAmount(new BigDecimal(33));
//        promoDTO.setDescription("会员卡");
//        promoDTO.setTag("已减33");
//
//        return buildPrice(dealGroupBaseDTO, promoDTO);
//    }
//
//    private PriceDisplayDTO buildPrice(DealGroupBaseDTO dealGroupBaseDTO, PromoDTO promoDTO) {
//        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
//        priceDisplayDTO.setIdentity(new ProductIdentity(123, ProductTypeEnum.DEAL.getType()));
//        priceDisplayDTO.setBasePrice(dealGroupBaseDTO.getDealGroupPrice());
//        priceDisplayDTO.setMarketPrice(dealGroupBaseDTO.getMarketPrice());
//        priceDisplayDTO.setPrice(dealGroupBaseDTO.getDealGroupPrice());
//        priceDisplayDTO.setMaxPrice(dealGroupBaseDTO.getDealGroupPrice());
//        priceDisplayDTO.setPromoAmount(BigDecimal.ZERO);
//        if (promoDTO != null) {
//            priceDisplayDTO.setPrice(dealGroupBaseDTO.getDealGroupPrice().subtract(promoDTO.getAmount()));
//            priceDisplayDTO.setPromoAmount(promoDTO.getAmount());
//            priceDisplayDTO.setUsedPromos(Lists.newArrayList(promoDTO));
//        }
//        return priceDisplayDTO;
//    }
//
//    private CardQualifyEventIdDTO buildNewMemberCard(boolean hold) {
//        CardQualifyEventIdDTO card = new CardQualifyEventIdDTO();
//        card.setQualifyEventId(1);
//        card.setQualifyEventType(1);
//        card.setUserQualifyStatus(hold ? 1 : 0);
//        card.setDiscount(new BigDecimal("0.5"));
//        return card;
//    }
//}
