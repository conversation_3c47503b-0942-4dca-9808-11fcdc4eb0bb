package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.detail.DealGroupDetailService;
import com.dianping.deal.detail.DealGroupStructedDetailService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailWrapperTest {

    @InjectMocks
    private DealDetailWrapper dealDetailWrapper;

    @Mock
    private DealGroupStructedDetailService dealGroupStructedDetailServiceFuture;

    @Mock
    private Future mockFuture;

    @Mock
    private DealGroupDetailService dealGroupDetailServiceFuture;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testPreHasStructedDetailDpDealGroupIdLessThanOrEqualToZero() throws Throwable {
        int dpDealGroupId = 0;
        Future result = dealDetailWrapper.preHasStructedDetail(dpDealGroupId);
        assertNull(result);
    }

    @Test
    public void testPreHasStructedDetailDpDealGroupIdGreaterThanZeroAndMethodCallSuccess() throws Throwable {
        int dpDealGroupId = 1;
        when(dealGroupStructedDetailServiceFuture.hasStructedDetailDesc(dpDealGroupId)).thenReturn(true);
        try (MockedStatic<FutureFactory> mockedStatic = Mockito.mockStatic(FutureFactory.class)) {
            mockedStatic.when(FutureFactory::getFuture).thenReturn(mockFuture);
            Future result = dealDetailWrapper.preHasStructedDetail(dpDealGroupId);
            assertNotNull(result);
            verify(dealGroupStructedDetailServiceFuture, times(1)).hasStructedDetailDesc(dpDealGroupId);
        }
    }

    @Test
    public void testPreHasStructedDetailDpDealGroupIdGreaterThanZeroAndMethodCallThrowException() throws Throwable {
        int dpDealGroupId = 1;
        doThrow(new RuntimeException()).when(dealGroupStructedDetailServiceFuture).hasStructedDetailDesc(dpDealGroupId);
        Future result = dealDetailWrapper.preHasStructedDetail(dpDealGroupId);
        assertNull(result);
        verify(dealGroupStructedDetailServiceFuture, times(1)).hasStructedDetailDesc(dpDealGroupId);
    }

    /**
     * 测试dpDealGroupId小于等于0的情况
     */
    @Test
    public void testPreDealGroupDetailDpDealGroupIdLessThanOrEqualToZero() throws Throwable {
        int dpDealGroupId = 0;
        Future result = dealDetailWrapper.preDealGroupDetail(dpDealGroupId);
        assertNull(result);
    }

    /**
     * 测试dpDealGroupId大于0且getDealGroupDetail方法抛出异常的情况
     */
    @Test
    public void testPreDealGroupDetailDpDealGroupIdGreaterThanZeroAndGetDealGroupDetailThrowsException() throws Throwable {
        int dpDealGroupId = 1;
        doThrow(new RuntimeException()).when(dealGroupDetailServiceFuture).getDealGroupDetail(dpDealGroupId);
        Future result = dealDetailWrapper.preDealGroupDetail(dpDealGroupId);
        assertNull(result);
    }
}
