package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.specificMoudule;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.BaseDisplayItemVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealDetailDisplayUnitVO;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SpecificModuleHandler_401GetCheckItemDescByTagIdTest {

    private static Method getDealDetailDisplayUnitVOMethod;

    private SpecificModuleHandler_401 specificModuleHandler_401 = new SpecificModuleHandler_401();

    @Before
    public void setUp() throws Exception {
        // Use reflection to access the private method
        getDealDetailDisplayUnitVOMethod = SpecificModuleHandler_401.class.getDeclaredMethod("getDealDetailDisplayUnitVO", String.class, List.class);
        getDealDetailDisplayUnitVOMethod.setAccessible(true);
    }

    private List<Long> invokePrivateMethod(String methodName, List<Long> arg1, List<Long> arg2, int arg3) throws Exception {
        Method method = SpecificModuleHandler_401.class.getDeclaredMethod(methodName, List.class, List.class, int.class);
        method.setAccessible(true);
        return (List<Long>) method.invoke(specificModuleHandler_401, arg1, arg2, arg3);
    }

    private String invokePrivateMethod(String methodName, String extraInfo) throws Exception {
        Method method = SpecificModuleHandler_401.class.getDeclaredMethod(methodName, String.class);
        method.setAccessible(true);
        return (String) method.invoke(specificModuleHandler_401, extraInfo);
    }

    /**
     * 测试getDealDetailDisplayUnitVO方法，attrName和displayItemList都是有效的
     */
    @Test
    public void testGetDealDetailDisplayUnitVO_ValidInput() throws Throwable {
        // arrange
        String attrName = "testAttrName";
        BaseDisplayItemVO displayItem = new BaseDisplayItemVO();
        displayItem.setName("testName");
        displayItem.setValues(Arrays.asList("testValue"));
        // act
        DealDetailDisplayUnitVO result = (DealDetailDisplayUnitVO) getDealDetailDisplayUnitVOMethod.invoke(null, attrName, Arrays.asList(displayItem));
        // assert
        assertNotNull(result);
        assertEquals(attrName, result.getTitle());
        assertEquals(Arrays.asList(displayItem), result.getDisplayItems());
    }

    /**
     * 测试getDealDetailDisplayUnitVO方法，attrName是null
     */
    @Test
    public void testGetDealDetailDisplayUnitVO_NullAttrName() throws Throwable {
        // arrange
        String attrName = null;
        BaseDisplayItemVO displayItem = new BaseDisplayItemVO();
        displayItem.setName("testName");
        displayItem.setValues(Arrays.asList("testValue"));
        // act
        DealDetailDisplayUnitVO result = (DealDetailDisplayUnitVO) getDealDetailDisplayUnitVOMethod.invoke(null, attrName, Arrays.asList(displayItem));
        // assert
        assertNotNull(result);
        assertEquals(attrName, result.getTitle());
        assertEquals(Arrays.asList(displayItem), result.getDisplayItems());
    }

    /**
     * 测试getDealDetailDisplayUnitVO方法，displayItemList是null
     */
    @Test
    public void testGetDealDetailDisplayUnitVO_NullDisplayItemList() throws Throwable {
        // arrange
        String attrName = "testAttrName";
        // act
        DealDetailDisplayUnitVO result = (DealDetailDisplayUnitVO) getDealDetailDisplayUnitVOMethod.invoke(null, attrName, null);
        // assert
        assertNotNull(result);
        assertEquals(attrName, result.getTitle());
        assertEquals(null, result.getDisplayItems());
    }

    /**
     * 测试 buildDisplayItem 方法，输入不同的 name 和 detail 参数
     */
    @Test
    public void testBuildDisplayItem() {
        // arrange
        String name = "testName";
        String detail = "testDetail";
        // act
        BaseDisplayItemVO result = specificModuleHandler_401.buildDisplayItem(name, detail);
        // assert
        assertEquals(name, result.getName());
        assertEquals(detail, result.getDetail());
    }

    /**
     * 测试 buildDisplayItem 方法，输入不同的 name 和 detail 参数
     */
    @Test
    public void testBuildDisplayItemWithDifferentParameters() {
        // arrange
        String name = "testName";
        String detail = "testDetail";
        // act
        BaseDisplayItemVO result = specificModuleHandler_401.buildDisplayItem(name, detail);
        // assert
        assertEquals(name, result.getName());
        assertEquals(detail, result.getDetail());
    }

    /**
     * 测试 buildDisplayItem 方法，输入不同的 name 和 detail 参数
     */
    @Test
    public void testBuildDisplayItemWithNullParameters() {
        // arrange
        String name = null;
        String detail = null;
        // act
        BaseDisplayItemVO result = specificModuleHandler_401.buildDisplayItem(name, detail);
        // assert
        assertEquals(name, result.getName());
        assertEquals(detail, result.getDetail());
    }

    @Test
    public void testPickTopCheckItemTagIdsBothEmpty() throws Throwable {
        List<Long> result = invokePrivateMethod("pickTopCheckItemTagIds", Collections.emptyList(), Collections.emptyList(), 10);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testPickTopCheckItemTagIdsCheckItemTagIdsEmpty() throws Throwable {
        List<Long> result = invokePrivateMethod("pickTopCheckItemTagIds", Collections.emptyList(), Arrays.asList(1L, 2L, 3L), 10);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testPickTopCheckItemTagIdsCurrentDealGroupTagListEmpty() throws Throwable {
        List<Long> result = invokePrivateMethod("pickTopCheckItemTagIds", Arrays.asList(1L, 2L, 3L), Collections.emptyList(), 10);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testPickTopCheckItemTagIdsMaxNumZero() throws Throwable {
        List<Long> result = invokePrivateMethod("pickTopCheckItemTagIds", Arrays.asList(1L, 2L, 3L), Arrays.asList(1L, 2L, 3L), 0);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testPickTopCheckItemTagIdsNoCommonElement() throws Throwable {
        List<Long> result = invokePrivateMethod("pickTopCheckItemTagIds", Arrays.asList(1L, 2L, 3L), Arrays.asList(4L, 5L, 6L), 10);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testPickTopCheckItemTagIdsCommonElement() throws Throwable {
        List<Long> result = invokePrivateMethod("pickTopCheckItemTagIds", Arrays.asList(1L, 2L, 3L), Arrays.asList(1L, 2L, 3L), 10);
        assertEquals(Arrays.asList(1L, 2L, 3L), result);
    }

    @Test
    public void testPickTopCheckItemTagIdsLessThanMaxNum() throws Throwable {
        List<Long> result = invokePrivateMethod("pickTopCheckItemTagIds", Arrays.asList(1L, 2L, 3L), Arrays.asList(1L, 2L, 3L), 5);
        assertEquals(Arrays.asList(1L, 2L, 3L), result);
    }

    @Test
    public void testPickTopCheckItemTagIdsExactlyMaxNum() throws Throwable {
        List<Long> result = invokePrivateMethod("pickTopCheckItemTagIds", Arrays.asList(1L, 2L, 3L), Arrays.asList(1L, 2L, 3L), 3);
        assertEquals(Arrays.asList(1L, 2L, 3L), result);
    }

    @Test
    public void testPickTopCheckItemTagIdsMoreThanMaxNum() throws Throwable {
        List<Long> result = invokePrivateMethod("pickTopCheckItemTagIds", Arrays.asList(1L, 2L, 3L), Arrays.asList(1L, 2L, 3L), 2);
        assertEquals(Arrays.asList(1L, 2L), result);
    }

    /**
     * 测试getCheckItemDescByTagId方法，当输入的列表为空时
     */
    @Test
    public void testGetCheckItemDescByTagIdWhenValuesIsEmpty() throws Throwable {
        // arrange
        String expected = "";
        // Using reflection to access the private method
        Method method = SpecificModuleHandler_401.class.getDeclaredMethod("getCheckItemDescByTagId", java.util.List.class);
        method.setAccessible(true);
        // act
        String actual = (String) method.invoke(specificModuleHandler_401, Collections.emptyList());
        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试getCheckItemDescByTagId方法，当输入的列表不为空时
     */
    @Test
    public void testGetCheckItemDescByTagIdWhenValuesIsNotEmpty() throws Throwable {
        // arrange
        String expected = "3项";
        // Using reflection to access the private method
        Method method = SpecificModuleHandler_401.class.getDeclaredMethod("getCheckItemDescByTagId", java.util.List.class);
        method.setAccessible(true);
        // act
        String actual = (String) method.invoke(specificModuleHandler_401, Arrays.asList("1", "2", "3"));
        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试 extraInfo 为空的情况
     */
    @Test
    public void testGetAttrNameExtraInfoIsNull() throws Throwable {
        // arrange
        String extraInfo = null;
        // act
        String result = invokePrivateMethod("getAttrName", extraInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 extraInfo 不为空，且可以正常转换为 JSONObject 对象，且 examinationAttrName 的值存在的情况
     */
    @Test
    public void testGetAttrNameExtraInfoIsValid() throws Throwable {
        // arrange
        String extraInfo = "{\"examinationAttrName\":\"test\"}";
        // act
        String result = invokePrivateMethod("getAttrName", extraInfo);
        // assert
        assertEquals("test", result);
    }

    /**
     * 测试 extraInfo 不为空，但无法转换为 JSONObject 对象的情况
     */
    @Test
    public void testGetAttrNameExtraInfoIsInvalid() throws Throwable {
        // arrange
        String extraInfo = "invalid";
        // act
        String result = invokePrivateMethod("getAttrName", extraInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 extraInfo 不为空，可以转换为 JSONObject 对象，但 examinationAttrName 的值不存在的情况
     */
    @Test
    public void testGetAttrNameExaminationAttrNameNotExists() throws Throwable {
        // arrange
        String extraInfo = "{}";
        // act
        String result = invokePrivateMethod("getAttrName", extraInfo);
        // assert
        assertEquals("", result);
    }
}
