package com.dianping.mobile.mapi.dztgdetail.helper;

import com.dianping.mobile.mapi.dztgdetail.button.BuilderChainConfig;
import com.dianping.mobile.mapi.dztgdetail.button.BuilderConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * 测试 NewBuyBarHelper 的 build 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class NewBuyBarHelperTest {

    /**
     * Tests the buildJoyMarketChainConfig method under normal conditions.
     * This test verifies that the method successfully returns a non-null instance of BuilderChainConfig.
     */
    @Test
    public void testBuildJoyMarketChainConfigNormal() throws Throwable {
        // Arrange
        // No data preparation needed as the method does not depend on external input
        // Act
        BuilderChainConfig result = NewBuyBarHelper.buildJoyMarketChainConfig();
        // Assert
        Assert.assertNotNull("The result of buildJoyMarketChainConfig should not be null.", result);
    }

    @Test
    public void testBuildBeautyChainConfigInclusiveTypesIsNull() throws Throwable {
        BuilderChainConfig result = NewBuyBarHelper.buildBeautyChainConfig();
        assertNotNull(result);
        boolean foundExclusiveAll = false;
        for (BuilderConfig config : result.getBuilderConfigs()) {
            if (config.getInclusiveType() == null) {
                foundExclusiveAll = config.isExclusiveAll();
                break;
            }
        }
        assertTrue("Expected to find a config with exclusiveAll=true when inclusiveType is null", foundExclusiveAll);
    }

    @Test
    public void testBuildBeautyChainConfigInclusiveTypesIsNotEmpty() throws Throwable {
        BuilderChainConfig result = NewBuyBarHelper.buildBeautyChainConfig();
        assertNotNull(result);
        for (BuilderConfig config : result.getBuilderConfigs()) {
            if (config.getInclusiveType() != null && !config.getInclusiveType().isEmpty()) {
                assertFalse(config.isInclusiveAll());
                assertFalse(config.isExclusiveAll());
            }
        }
    }
    // 更多测试用例可以按照上述模式继续添加，以覆盖所有可能的场景和分支
}
