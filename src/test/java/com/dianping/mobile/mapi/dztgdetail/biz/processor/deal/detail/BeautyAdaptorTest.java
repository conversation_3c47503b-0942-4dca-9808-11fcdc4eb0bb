package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.lang.reflect.Method;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BeautyAdaptorTest {

    private BeautyAdaptor beautyAdaptor = new BeautyAdaptor();

    // Helper method to invoke the private isBeauty method using reflection
    private boolean invokePrivateIsBeautyMethod(String category, int publishCategoryId) throws Throwable {
        try {
            Method method = BeautyAdaptor.class.getDeclaredMethod("isBeauty", String.class, int.class);
            method.setAccessible(true);
            return (boolean) method.invoke(beautyAdaptor, category, publishCategoryId);
        } catch (Exception e) {
            throw e.getCause();
        }
    }

    /**
     * 测试 category 为空的情况
     */
    @Test
    public void testIsBeautyCategoryIsEmpty() throws Throwable {
        // arrange
        String category = "";
        int publishCategoryId = 502;
        // act
        boolean result = invokePrivateIsBeautyMethod(category, publishCategoryId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 category 为 null 的情况
     */
    @Test
    public void testIsBeautyCategoryIsNull() throws Throwable {
        // arrange
        String category = null;
        int publishCategoryId = 502;
        // act
        boolean result = invokePrivateIsBeautyMethod(category, publishCategoryId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 category 包含非数字字符的情况
     */
    @Test(expected = NumberFormatException.class)
    public void testIsBeautyCategoryContainsNonNumeric() throws Throwable {
        // arrange
        String category = "abc,502";
        int publishCategoryId = 502;
        // act
        invokePrivateIsBeautyMethod(category, publishCategoryId);
        // assert
        // 预期抛出 NumberFormatException
    }

    /**
     * 测试 category 包含多个 categoryID，且有一个匹配的情况
     */
    @Test
    public void testIsBeautyCategoryContainsMultipleIdsWithMatch() throws Throwable {
        // arrange
        String category = "501,502,503";
        int publishCategoryId = 502;
        // act
        boolean result = invokePrivateIsBeautyMethod(category, publishCategoryId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 category 包含多个 categoryID，但没有匹配的情况
     */
    @Test
    public void testIsBeautyCategoryContainsMultipleIdsWithoutMatch() throws Throwable {
        // arrange
        String category = "501,503,504";
        int publishCategoryId = 502;
        // act
        boolean result = invokePrivateIsBeautyMethod(category, publishCategoryId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 category 包含一个 categoryID，且匹配的情况
     */
    @Test
    public void testIsBeautyCategoryContainsSingleIdWithMatch() throws Throwable {
        // arrange
        String category = "502";
        int publishCategoryId = 502;
        // act
        boolean result = invokePrivateIsBeautyMethod(category, publishCategoryId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 category 包含一个 categoryID，但不匹配的情况
     */
    @Test
    public void testIsBeautyCategoryContainsSingleIdWithoutMatch() throws Throwable {
        // arrange
        String category = "501";
        int publishCategoryId = 502;
        // act
        boolean result = invokePrivateIsBeautyMethod(category, publishCategoryId);
        // assert
        assertFalse(result);
    }
}
