package com.dianping.mobile.mapi.dztgdetail.biz.processor.imagetext.strategy;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.DealCategoryFactory;
import com.dianping.mobile.mapi.dztgdetail.biz.processor.deal.detail.factory.model.DealCategoryParam;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ContentDetailPBO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractImageTextDetailStrategyTest {

    @InjectMocks
    private AbstractImageTextDetailStrategy abstractImageTextDetailStrategy = new AbstractImageTextDetailStrategy() {

        @Override
        public ContentDetailPBO getContentDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold) {
            return super.getContentDetail(dealGroupDTO, contents, threshold);
        }

        @Override
        public ImageTextDetailPBO buildImageTextDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold, EnvCtx envCtx) {
            return null;
        }

        @Override
        public ImageTextStrategyEnum getStrategyName() {
            return null;
        }
    };

    @InjectMocks
    private ConcreteImageTextDetailStrategy concreteImageTextDetailStrategy;

    @Mock
    private DealCategoryFactory dealCategoryFactory;

    private DealGroupDTO dealGroupDTO;

    private List<ContentPBO> contents;

    private EnvCtx envCtx;

    @Before
    public void setUp() {
        dealGroupDTO = mock(DealGroupDTO.class);
        contents = new ArrayList<>();
        envCtx = mock(EnvCtx.class);
    }

    private ContentDetailPBO invokePrivateMethod(AbstractImageTextDetailStrategy strategy, String methodName, DealGroupDTO dealGroupDTO, List<ContentPBO> contents) throws Exception {
        Method method = AbstractImageTextDetailStrategy.class.getDeclaredMethod(methodName, DealGroupDTO.class, List.class);
        method.setAccessible(true);
        return (ContentDetailPBO) method.invoke(strategy, dealGroupDTO, contents);
    }

    /**
     * Tests getDefaultContentDetail method when contents list is empty.
     */
    @Test
    public void testGetDefaultContentDetailWhenContentsIsEmpty() throws Throwable {
        // Arrange
        AbstractImageTextDetailStrategy strategy = new AbstractImageTextDetailStrategy() {

            @Override
            public com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum getStrategyName() {
                return null;
            }

            @Override
            public ContentDetailPBO getContentDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold) {
                return null;
            }

            @Override
            public com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO buildImageTextDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold, com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx envCtx) {
                return null;
            }
        };
        List<ContentPBO> contents = new ArrayList<>();
        // Act
        ContentDetailPBO result = invokePrivateMethod(strategy, "getDefaultContentDetail", null, contents);
        // Assert
        assertNotNull(result);
        assertEquals("图文详情", result.getTitle());
        assertEquals(contents, result.getContents());
        assertFalse(result.isFold());
        assertEquals(1, result.getFoldThreshold());
    }

    /**
     * Tests getDefaultContentDetail method when contents list is not empty.
     */
    @Test
    public void testGetDefaultContentDetailWhenContentsIsNotEmpty() throws Throwable {
        // Arrange
        AbstractImageTextDetailStrategy strategy = new AbstractImageTextDetailStrategy() {

            @Override
            public com.dianping.mobile.mapi.dztgdetail.common.enums.ImageTextStrategyEnum getStrategyName() {
                return null;
            }

            @Override
            public ContentDetailPBO getContentDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold) {
                return null;
            }

            @Override
            public com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.ImageTextDetailPBO buildImageTextDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold, com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx envCtx) {
                return null;
            }
        };
        List<ContentPBO> contents = new ArrayList<>();
        contents.add(new ContentPBO(1, "content"));
        // Act
        ContentDetailPBO result = invokePrivateMethod(strategy, "getDefaultContentDetail", null, contents);
        // Assert
        assertNotNull(result);
        assertEquals("图文详情", result.getTitle());
        assertEquals(contents, result.getContents());
        assertFalse(result.isFold());
        assertEquals(2, result.getFoldThreshold());
    }

    @Test
    public void testGetContentDetailDealGroupDTONull() throws Throwable {
        List<ContentPBO> contents = new ArrayList<>();
        ContentDetailPBO result = abstractImageTextDetailStrategy.getContentDetail(null, contents, 1);
        assertNotNull(result);
        assertEquals("图文详情", result.getTitle());
        assertEquals(contents, result.getContents());
        assertFalse(result.isFold());
        assertEquals(contents.size() + 1, result.getFoldThreshold());
    }

    @Test(expected = NullPointerException.class)
    public void testGetContentDetailContentsNull() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // Expecting NullPointerException
        abstractImageTextDetailStrategy.getContentDetail(dealGroupDTO, null, 1);
    }

    @Test
    public void testGetContentDetailContentsEmpty() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<ContentPBO> contents = new ArrayList<>();
        ContentDetailPBO result = abstractImageTextDetailStrategy.getContentDetail(dealGroupDTO, contents, 1);
        assertNotNull(result);
        assertEquals("图文详情", result.getTitle());
        assertEquals(contents, result.getContents());
        assertFalse(result.isFold());
        // Corrected expected value
        assertEquals(1, result.getFoldThreshold());
    }

    @Test
    public void testGetContentDetailThresholdNegative() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<ContentPBO> contents = new ArrayList<>();
        contents.add(new ContentPBO(1, "content"));
        ContentDetailPBO result = abstractImageTextDetailStrategy.getContentDetail(dealGroupDTO, contents, -1);
        assertNotNull(result);
        assertEquals("图文详情", result.getTitle());
        assertEquals(contents, result.getContents());
        assertFalse(result.isFold());
        assertEquals(contents.size() + 1, result.getFoldThreshold());
    }

    @Test
    public void testGetContentDetailThresholdZero() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<ContentPBO> contents = new ArrayList<>();
        contents.add(new ContentPBO(1, "content"));
        ContentDetailPBO result = abstractImageTextDetailStrategy.getContentDetail(dealGroupDTO, contents, 0);
        assertNotNull(result);
        assertEquals("图文详情", result.getTitle());
        assertEquals(contents, result.getContents());
        assertFalse(result.isFold());
        assertEquals(contents.size() + 1, result.getFoldThreshold());
    }

    @Test
    public void testGetContentDetailThresholdPositive() throws Throwable {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<ContentPBO> contents = new ArrayList<>();
        contents.add(new ContentPBO(1, "content"));
        ContentDetailPBO result = abstractImageTextDetailStrategy.getContentDetail(dealGroupDTO, contents, 1);
        assertNotNull(result);
        assertEquals("图文详情", result.getTitle());
        assertEquals(contents, result.getContents());
        assertFalse(result.isFold());
        assertEquals(contents.size() + 1, result.getFoldThreshold());
    }

    // Create a concrete subclass for testing
    private static class ConcreteImageTextDetailStrategy extends AbstractImageTextDetailStrategy {

        @Override
        public ImageTextStrategyEnum getStrategyName() {
            return ImageTextStrategyEnum.DEFAULT;
        }

        @Override
        public ContentDetailPBO getContentDetail(DealGroupDTO dealGroupDTO, List<ContentPBO> contents, int threshold) {
            return new ContentDetailPBO();
        }
    }

    @Test
    public void testBuildImageTextDetailNormal() throws Throwable {
        // arrange
        when(dealGroupDTO.getCategory()).thenReturn(mock(DealGroupCategoryDTO.class));
        when(dealCategoryFactory.newDealStyle(any(DealCategoryParam.class))).thenReturn(true);
        // act
        ImageTextDetailPBO result = concreteImageTextDetailStrategy.buildImageTextDetail(dealGroupDTO, contents, 1, envCtx);
        // assert
        assertNotNull(result);
        assertTrue(result.isUseCardStyle());
    }

    @Test(expected = NullPointerException.class)
    public void testBuildImageTextDetailDealGroupDTONull() throws Throwable {
        // arrange
        // act
        concreteImageTextDetailStrategy.buildImageTextDetail(null, contents, 1, envCtx);
        // assert
    }

    @Test
    public void testBuildImageTextDetailContentsNull() throws Throwable {
        // arrange
        // act
        ImageTextDetailPBO result = concreteImageTextDetailStrategy.buildImageTextDetail(dealGroupDTO, null, 1, envCtx);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testBuildImageTextDetailEnvCtxNull() throws Throwable {
        // arrange
        // act
        ImageTextDetailPBO result = concreteImageTextDetailStrategy.buildImageTextDetail(dealGroupDTO, contents, 1, null);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testBuildImageTextDetailCategoryNull() throws Throwable {
        // arrange
        when(dealGroupDTO.getCategory()).thenReturn(null);
        when(dealCategoryFactory.newDealStyle(any(DealCategoryParam.class))).thenReturn(true);
        // act
        ImageTextDetailPBO result = concreteImageTextDetailStrategy.buildImageTextDetail(dealGroupDTO, contents, 1, envCtx);
        // assert
        assertNotNull(result);
        assertTrue(result.isUseCardStyle());
    }
}
