package com.dianping.mobile.mapi.dztgdetail.facade;

import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.DzDealThemeWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.HaimaWrapper;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.SkuWrapper;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealTinyInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealLowPriceItemEntranceVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealTinyInfoVO;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealTinyInfoFacadeTest {

    @InjectMocks
    private DealTinyInfoFacade dealTinyInfoFacade;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private GetDealTinyInfoRequest request;

    @Mock
    private SkuWrapper skuWrapper;

    @Mock
    private HaimaWrapper haimaWrapper;

    @Mock
    private DzDealThemeWrapper // Mocked DzDealThemeWrapper
    dzDealThemeWrapper;

    private MockedStatic<LionConfigUtils> lionConfigUtilsMockedStatic;

    @Before
    public void setUp() {
        lionConfigUtilsMockedStatic = mockStatic(LionConfigUtils.class);
    }

    @After
    public void tearDown() {
        lionConfigUtilsMockedStatic.close();
    }

    @Test
    public void test1() throws InstantiationException, IllegalAccessException, ClassNotFoundException, NoSuchMethodException, InvocationTargetException {
        Class<?> clazz = Class.forName(DealTinyInfoFacade.class.getName());
        DealTinyInfoFacade dealTinyInfoFacade = (DealTinyInfoFacade) clazz.newInstance();
        Method getDealResult = clazz.getDeclaredMethod("getDealResult", DealProductResult.class, Integer.class, String.class);
        getDealResult.setAccessible(true);
        DealProductResult dealProductResult = new DealProductResult();
        ArrayList<DealProductDTO> deals = new ArrayList<>();
        DealProductDTO dealProductDTO = new DealProductDTO();
        ArrayList<DealProductAttrDTO> attrs = new ArrayList<>();
        DealProductAttrDTO element = new DealProductAttrDTO();
        element.setName("dealSubTitle");
        element.setValue("[\"a\", null]}");
        attrs.add(element);
        dealProductDTO.setAttrs(attrs);
        dealProductDTO.setMarketPriceTag("1");
        dealProductDTO.setPromoPrice(new BigDecimal(1));
        deals.add(dealProductDTO);
        dealProductResult.setDeals(deals);
        DealTinyInfoVO result = (DealTinyInfoVO) getDealResult.invoke(dealTinyInfoFacade, dealProductResult, 1, "2");
        assertNotNull(result);
    }

    /**
     * 测试 PromoDTO 对象为 null 时的行为。
     */
    @Test
    public void testGetFinanceExtJsonWithNullPromoDTO() {
        // arrange
        PromoDTO coupon = null;

        // act
        String result = PromoInfoHelper.getFinanceExtJson(coupon);

        // assert
        assertEquals("Expected empty string for null PromoDTO", "", result);
    }

    /**
     * 测试 PromoDTO 对象非空，但 promotionOtherInfoMap 不包含 FINANCE_EXT 键值对。
     */
    @Test
    public void testGetFinanceExtJsonWithEmptyMap() {
        // arrange
        PromoDTO coupon = mock(PromoDTO.class);
        when(coupon.getPromotionOtherInfoMap()).thenReturn(new HashMap<>());

        // act
        String result = PromoInfoHelper.getFinanceExtJson(coupon);

        // assert
        assertEquals("Expected empty string for PromoDTO with empty map", "", result);
    }

    /**
     * 测试 PromoDTO 对象非空，且 promotionOtherInfoMap 包含 FINANCE_EXT 键值对。
     */
    @Test
    public void testGetFinanceExtJsonWithValidMap() {
        // arrange
        PromoDTO coupon = mock(PromoDTO.class);
        Map<String, String> map = new HashMap<>();
        map.put(PromotionPropertyEnum.FINANCE_EXT.getValue(), "finance_ext_value");
        when(coupon.getPromotionOtherInfoMap()).thenReturn(map);

        // act
        String result = PromoInfoHelper.getFinanceExtJson(coupon);

        // assert
        assertEquals("Expected finance_ext_value for valid PromoDTO", "finance_ext_value", result);
    }

    /**
     * 测试门店在黑名单中的场景
     */
    @Test
    public void testIsShowLowPriceItemEntrance_ShopInBlackList() {
        GetDealTinyInfoRequest request = new GetDealTinyInfoRequest();
        request.setShopId("2");
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);

        when(haimaWrapper.isBlackListShop(request.getShopId(), envCtx.isMt())).thenReturn(false);

        lionConfigUtilsMockedStatic.when(() -> LionConfigUtils.isComparePriceShopBlackList(envCtx.isMt(), request.getShopId()))
                .thenReturn(true);

        DealLowPriceItemEntranceVO result = dealTinyInfoFacade.isShowLowPriceItemEntrance(request, envCtx);

        assertFalse("门店在黑名单中，不应展示同款低价入口", result.isShowLowPriceDealList());
    }
}
