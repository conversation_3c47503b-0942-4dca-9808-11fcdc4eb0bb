package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.dianping.poi.base.common.enums.PoiIdTypeEnum;
import com.dianping.poi.biz.api.service.PoiBizService;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import java.util.concurrent.Future;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PoiClientWrapper_PrePoiBizAttrFutureTest {

    @InjectMocks
    private PoiClientWrapper poiClientWrapper;

    @Mock
    private PoiBizService poiBizServiceFuture;

    @Mock
    private Future mockFuture;

    /**
     * Tests the prePoiBizAttrFuture method under normal conditions.
     */
    @Test
    public void testPrePoiBizAttrFutureNormal() throws Throwable {
        // arrange
        long dpShopId = 1L;
        when(poiBizServiceFuture.getPoiBizAttr(eq(dpShopId), eq(PoiIdTypeEnum.DP_POI_ID), anyList())).thenReturn(null);
        try (MockedStatic<FutureFactory> mocked = mockStatic(FutureFactory.class)) {
            mocked.when(FutureFactory::getFuture).thenReturn(mockFuture);
            // act
            Future future = poiClientWrapper.prePoiBizAttrFuture(dpShopId);
            // assert
            verify(poiBizServiceFuture, times(1)).getPoiBizAttr(eq(dpShopId), eq(PoiIdTypeEnum.DP_POI_ID), anyList());
            assertNotNull("The future should not be null", future);
        }
    }

    /**
     * Tests the prePoiBizAttrFuture method under exception conditions.
     */
    @Test(expected = Exception.class)
    public void testPrePoiBizAttrFutureException() throws Throwable {
        // arrange
        long dpShopId = 1L;
        doThrow(new Exception()).when(poiBizServiceFuture).getPoiBizAttr(eq(dpShopId), eq(PoiIdTypeEnum.DP_POI_ID), anyList());
        // act
        poiClientWrapper.prePoiBizAttrFuture(dpShopId);
        // assert
        // Exception is expected
    }
}
