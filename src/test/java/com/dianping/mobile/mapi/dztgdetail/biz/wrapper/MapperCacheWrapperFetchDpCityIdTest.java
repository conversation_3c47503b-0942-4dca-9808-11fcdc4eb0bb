package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MapperCacheWrapperFetchDpCityIdTest {

    @Spy
    @InjectMocks
    private MapperCacheWrapper mapperCacheWrapper;

    @Mock
    private MapperWrapper mapperWrapper;

    /**
     * 测试 fetchDpCityId 方法，正常情况
     * 当缓存未命中时，应该通过 mapperWrapper 获取数据
     */
    @Test
    public void testFetchDpCityIdNormal() throws Throwable {
        // arrange
        int mtCityId = 100;
        int expectedDpCityId = 200;
        Future<Integer> future = mock(Future.class);
        CompletableFuture<Integer> cacheFuture = new CompletableFuture<>();
        cacheFuture.completeExceptionally(new ExecutionException(new RuntimeException("Cache miss")));
        when(mapperWrapper.preDpCityByMtCity(mtCityId)).thenReturn(future);
        when(mapperWrapper.getDpCityByMtCity(future)).thenReturn(expectedDpCityId);
        doReturn(cacheFuture).when(mapperCacheWrapper).getDpCityIdFromCache(mtCityId);
        // act
        int result = mapperCacheWrapper.fetchDpCityId(mtCityId);
        // assert
        assertEquals(expectedDpCityId, result);
    }

    /**
     * 测试 fetchDpCityId 方法，异常情况
     * 当缓存访问抛出异常时，应该通过备用方式获取数据
     */
    @Test
    public void testFetchDpCityIdException() throws Throwable {
        // arrange
        int mtCityId = 100;
        int expectedDpCityId = 200;
        Future<Integer> future = mock(Future.class);
        CompletableFuture<Integer> cacheFuture = new CompletableFuture<>();
        cacheFuture.completeExceptionally(new RuntimeException("Cache error"));
        when(mapperWrapper.preDpCityByMtCity(mtCityId)).thenReturn(future);
        when(mapperWrapper.getDpCityByMtCity(future)).thenReturn(expectedDpCityId);
        doReturn(cacheFuture).when(mapperCacheWrapper).getDpCityIdFromCache(mtCityId);
        // act
        int result = mapperCacheWrapper.fetchDpCityId(mtCityId);
        // assert
        assertEquals(expectedDpCityId, result);
    }

    /**
     * 测试 fetchDpCityId 方法，边界情况
     * 当入参 mtCityId 为 0 或负数时的处理
     */
    @Test
    public void testFetchDpCityIdWithInvalidInput() throws Throwable {
        // arrange
        int mtCityId = 0;
        CompletableFuture<Integer> cacheFuture = CompletableFuture.completedFuture(0);
        Future<Integer> future = mock(Future.class);
        doReturn(cacheFuture).when(mapperCacheWrapper).getDpCityIdFromCache(mtCityId);
        // act
        int result = mapperCacheWrapper.fetchDpCityId(mtCityId);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 fetchDpCityId 方法，当缓存命中时的情况
     */
    @Test
    public void testFetchDpCityIdCacheHit() throws Throwable {
        // arrange
        int mtCityId = 100;
        int expectedDpCityId = 200;
        CompletableFuture<Integer> cacheFuture = CompletableFuture.completedFuture(expectedDpCityId);
        doReturn(cacheFuture).when(mapperCacheWrapper).getDpCityIdFromCache(mtCityId);
        // act
        int result = mapperCacheWrapper.fetchDpCityId(mtCityId);
        // assert
        assertEquals(expectedDpCityId, result);
        verify(mapperWrapper, never()).preDpCityByMtCity(anyInt());
        verify(mapperWrapper, never()).getDpCityByMtCity(any());
    }
}
