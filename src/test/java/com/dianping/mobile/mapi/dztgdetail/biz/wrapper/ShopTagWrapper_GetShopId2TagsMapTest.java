package com.dianping.mobile.mapi.dztgdetail.biz.wrapper;

import com.sankuai.athena.biz.Response;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;
import org.junit.Before;

public class ShopTagWrapper_GetShopId2TagsMapTest {

    private ShopTagWrapper shopTagWrapper;

    private Future futureMock;

    /**
     * Test getShopId2TagsMap when future is null.
     */
    @Test
    public void testGetShopId2TagsMapFutureIsNull() throws Throwable {
        ShopTagWrapper shopTagWrapper = new ShopTagWrapper();
        Future future = null;
        Map<Long, List<DisplayTagDto>> result = shopTagWrapper.getShopId2TagsMap(future);
        assertTrue("Result should be empty", result.isEmpty());
    }

    /**
     * Test getShopId2TagsMap when future is not null, but getFutureResult returns null response.
     */
    @Test
    public void testGetShopId2TagsMapResponseIsNull() throws Throwable {
        ShopTagWrapper shopTagWrapper = new ShopTagWrapper();
        Future futureMock = Mockito.mock(Future.class);
        when(futureMock.get()).thenReturn(null);
        Map<Long, List<DisplayTagDto>> result = shopTagWrapper.getShopId2TagsMap(futureMock);
        assertTrue("Result should be empty", result.isEmpty());
    }

    /**
     * Test getShopId2TagsMap when future is not null, getFutureResult returns a non-null response, but isSuccess returns false.
     */
    @Test
    public void testGetShopId2TagsMapResponseIsNotSuccess() throws Throwable {
        ShopTagWrapper shopTagWrapper = new ShopTagWrapper();
        Future futureMock = Mockito.mock(Future.class);
        Response<Map<Long, List<DisplayTagDto>>> response = new Response<>(100, "Failure");
        when(futureMock.get()).thenReturn(response);
        Map<Long, List<DisplayTagDto>> result = shopTagWrapper.getShopId2TagsMap(futureMock);
        assertTrue("Result should be empty", result.isEmpty());
    }
}
