package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.PriceRangeQueryWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.req.GetDealTinyInfoRequest;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealLowPriceItemEntranceVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealPriceTrendVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.DealTinyInfoVO;
import com.sankuai.tpfun.skuoperationapi.price.dto.PriceRangeDO;
import com.sankuai.tpfun.skuoperationapi.price.dto.PriceRangeItemDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.SubjectDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.enums.PriceRangeItemTypeEnum;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.BatchPriceRangeInfoResponse;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.Response;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealTinyInfoFacadeGetTopFiftyPercentPriceTest {

    @InjectMocks
    private DealTinyInfoFacade dealTinyInfoFacade;

    @Mock
    private PriceRangeQueryWrapper priceRangeQueryWrapper;

    @Mock
    private Future future;

    private Method getTopFiftyPercentPriceMethod;

    @Before
    public void setUp() throws Exception {
        // Use Reflection to access the private method
        getTopFiftyPercentPriceMethod = DealTinyInfoFacade.class.getDeclaredMethod("getTopFiftyPercentPrice", int.class, long.class);
        getTopFiftyPercentPriceMethod.setAccessible(true);
    }

    @Test
    public void testGetTopFiftyPercentPriceNormal() throws Throwable {
        // arrange
        int dpDealId = 1;
        long dpShopId = 1L;
        PriceRangeDO priceRangeDO = new PriceRangeDO();
        Map<String, String> extra = new HashMap<>();
        extra.put("topFiftyPercentPrice", "100");
        priceRangeDO.setExtra(extra);
        PriceRangeItemDTO priceRangeItemDTO = new PriceRangeItemDTO();
        priceRangeItemDTO.setPriceRangeItemType(PriceRangeItemTypeEnum.SHOPPING_CART_SPACE_PRICE_RANGE_ITEM.getCode());
        priceRangeItemDTO.setPriceRangeDO(priceRangeDO);
        List<PriceRangeItemDTO> priceRangeItems = Collections.singletonList(priceRangeItemDTO);
        SubjectDTO subjectDTO = new SubjectDTO();
        subjectDTO.setDpShopId(dpShopId);
        subjectDTO.setProductId((long) dpDealId);
        BatchPriceRangeInfoResponse batchPriceRangeInfoResponse = new BatchPriceRangeInfoResponse();
        Map<SubjectDTO, List<PriceRangeItemDTO>> subjectDTO2PriceRangesMap = new HashMap<>();
        subjectDTO2PriceRangesMap.put(subjectDTO, priceRangeItems);
        batchPriceRangeInfoResponse.setSubjectDTO2PriceRangesMap(subjectDTO2PriceRangesMap);
        Response<BatchPriceRangeInfoResponse> response = new Response<>();
        response.setResult(batchPriceRangeInfoResponse);
        response.setSuccess(true);
        when(priceRangeQueryWrapper.preQueryPriceRange(dpDealId, dpShopId)).thenReturn(future);
        when(priceRangeQueryWrapper.getFutureResult(future)).thenReturn(response);
        // act
        String result = (String) getTopFiftyPercentPriceMethod.invoke(dealTinyInfoFacade, dpDealId, dpShopId);
        // assert
        assertEquals("100", result);
    }

    @Test
    public void testGetTopFiftyPercentPriceException1() throws Throwable {
        // arrange
        int dpDealId = 1;
        long dpShopId = 1L;
        Response<BatchPriceRangeInfoResponse> response = new Response<>();
        response.setSuccess(false);
        when(priceRangeQueryWrapper.preQueryPriceRange(dpDealId, dpShopId)).thenReturn(future);
        when(priceRangeQueryWrapper.getFutureResult(future)).thenReturn(response);
        // act
        String result = (String) getTopFiftyPercentPriceMethod.invoke(dealTinyInfoFacade, dpDealId, dpShopId);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTopFiftyPercentPriceException2() throws Throwable {
        // arrange
        int dpDealId = 1;
        long dpShopId = 1L;
        Response<BatchPriceRangeInfoResponse> response = new Response<>();
        response.setSuccess(true);
        response.setResult(null);
        when(priceRangeQueryWrapper.preQueryPriceRange(dpDealId, dpShopId)).thenReturn(future);
        when(priceRangeQueryWrapper.getFutureResult(future)).thenReturn(response);
        // act
        String result = (String) getTopFiftyPercentPriceMethod.invoke(dealTinyInfoFacade, dpDealId, dpShopId);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTopFiftyPercentPriceException3() throws Throwable {
        // arrange
        int dpDealId = 1;
        long dpShopId = 1L;
        BatchPriceRangeInfoResponse batchPriceRangeInfoResponse = new BatchPriceRangeInfoResponse();
        batchPriceRangeInfoResponse.setSubjectDTO2PriceRangesMap(null);
        Response<BatchPriceRangeInfoResponse> response = new Response<>();
        response.setSuccess(true);
        response.setResult(batchPriceRangeInfoResponse);
        when(priceRangeQueryWrapper.preQueryPriceRange(dpDealId, dpShopId)).thenReturn(future);
        when(priceRangeQueryWrapper.getFutureResult(future)).thenReturn(response);
        // act
        String result = (String) getTopFiftyPercentPriceMethod.invoke(dealTinyInfoFacade, dpDealId, dpShopId);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTopFiftyPercentPriceException4() throws Throwable {
        // arrange
        int dpDealId = 1;
        long dpShopId = 1L;
        SubjectDTO subjectDTO = new SubjectDTO();
        subjectDTO.setDpShopId(dpShopId);
        subjectDTO.setProductId((long) dpDealId);
        BatchPriceRangeInfoResponse batchPriceRangeInfoResponse = new BatchPriceRangeInfoResponse();
        Map<SubjectDTO, List<PriceRangeItemDTO>> subjectDTO2PriceRangesMap = new HashMap<>();
        subjectDTO2PriceRangesMap.put(subjectDTO, Collections.emptyList());
        batchPriceRangeInfoResponse.setSubjectDTO2PriceRangesMap(subjectDTO2PriceRangesMap);
        Response<BatchPriceRangeInfoResponse> response = new Response<>();
        response.setSuccess(true);
        response.setResult(batchPriceRangeInfoResponse);
        when(priceRangeQueryWrapper.preQueryPriceRange(dpDealId, dpShopId)).thenReturn(future);
        when(priceRangeQueryWrapper.getFutureResult(future)).thenReturn(response);
        // act
        String result = (String) getTopFiftyPercentPriceMethod.invoke(dealTinyInfoFacade, dpDealId, dpShopId);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTopFiftyPercentPriceException5() throws Throwable {
        // arrange
        int dpDealId = 1;
        long dpShopId = 1L;
        PriceRangeDO priceRangeDO = new PriceRangeDO();
        priceRangeDO.setExtra(null);
        PriceRangeItemDTO priceRangeItemDTO = new PriceRangeItemDTO();
        priceRangeItemDTO.setPriceRangeItemType(PriceRangeItemTypeEnum.SHOPPING_CART_SPACE_PRICE_RANGE_ITEM.getCode());
        priceRangeItemDTO.setPriceRangeDO(priceRangeDO);
        SubjectDTO subjectDTO = new SubjectDTO();
        subjectDTO.setDpShopId(dpShopId);
        subjectDTO.setProductId((long) dpDealId);
        BatchPriceRangeInfoResponse batchPriceRangeInfoResponse = new BatchPriceRangeInfoResponse();
        Map<SubjectDTO, List<PriceRangeItemDTO>> subjectDTO2PriceRangesMap = new HashMap<>();
        subjectDTO2PriceRangesMap.put(subjectDTO, Collections.singletonList(priceRangeItemDTO));
        batchPriceRangeInfoResponse.setSubjectDTO2PriceRangesMap(subjectDTO2PriceRangesMap);
        Response<BatchPriceRangeInfoResponse> response = new Response<>();
        response.setSuccess(true);
        response.setResult(batchPriceRangeInfoResponse);
        when(priceRangeQueryWrapper.preQueryPriceRange(dpDealId, dpShopId)).thenReturn(future);
        when(priceRangeQueryWrapper.getFutureResult(future)).thenReturn(response);
        // act
        String result = (String) getTopFiftyPercentPriceMethod.invoke(dealTinyInfoFacade, dpDealId, dpShopId);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTopFiftyPercentPriceException6() throws Throwable {
        // arrange
        int dpDealId = 1;
        long dpShopId = 1L;
        PriceRangeDO priceRangeDO = new PriceRangeDO();
        Map<String, String> extra = new HashMap<>();
        priceRangeDO.setExtra(extra);
        PriceRangeItemDTO priceRangeItemDTO = new PriceRangeItemDTO();
        priceRangeItemDTO.setPriceRangeItemType(PriceRangeItemTypeEnum.SHOPPING_CART_SPACE_PRICE_RANGE_ITEM.getCode());
        priceRangeItemDTO.setPriceRangeDO(priceRangeDO);
        SubjectDTO subjectDTO = new SubjectDTO();
        subjectDTO.setDpShopId(dpShopId);
        subjectDTO.setProductId((long) dpDealId);
        BatchPriceRangeInfoResponse batchPriceRangeInfoResponse = new BatchPriceRangeInfoResponse();
        Map<SubjectDTO, List<PriceRangeItemDTO>> subjectDTO2PriceRangesMap = new HashMap<>();
        subjectDTO2PriceRangesMap.put(subjectDTO, Collections.singletonList(priceRangeItemDTO));
        batchPriceRangeInfoResponse.setSubjectDTO2PriceRangesMap(subjectDTO2PriceRangesMap);
        Response<BatchPriceRangeInfoResponse> response = new Response<>();
        response.setSuccess(true);
        response.setResult(batchPriceRangeInfoResponse);
        when(priceRangeQueryWrapper.preQueryPriceRange(dpDealId, dpShopId)).thenReturn(future);
        when(priceRangeQueryWrapper.getFutureResult(future)).thenReturn(response);
        // act
        String result = (String) getTopFiftyPercentPriceMethod.invoke(dealTinyInfoFacade, dpDealId, dpShopId);
        // assert
        assertEquals("", result);
    }
}
