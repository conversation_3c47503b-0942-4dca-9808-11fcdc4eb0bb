package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor;

import com.dianping.mobile.mapi.dztgdetail.datatype.resp.deal.PromoDetailModule;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import static org.junit.Assert.*;

public class ParallDealBuilderProcessor_SetWuyoutongPromoTest {

    private ParallDealBuilderProcessor parallDealBuilderProcessor;

    @Before
    public void setUp() {
        parallDealBuilderProcessor = new ParallDealBuilderProcessor();
    }

    /**
     * 测试 setWuyoutongPromo 方法
     */
    @Test
    public void testSetWuyoutongPromo() {
        // arrange
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        promoDetailModule.setShowMarketPrice(true);
        promoDetailModule.setMarketPricePromo("test");
        promoDetailModule.setShowBestPromoDetails(true);
        promoDetailModule.setBestPromoDetails(null);
        promoDetailModule.setMarketPrice("test");
        promoDetailModule.setMarketPromoDiscount("test");
        // act
        parallDealBuilderProcessor.setWuyoutongPromo(promoDetailModule);
        // assert
        assertFalse(promoDetailModule.isShowMarketPrice());
        assertEquals("", promoDetailModule.getMarketPricePromo());
        assertFalse(promoDetailModule.isShowBestPromoDetails());
        assertNull(promoDetailModule.getBestPromoDetails());
        assertEquals("", promoDetailModule.getMarketPrice());
        assertEquals("", promoDetailModule.getMarketPromoDiscount());
    }
}
