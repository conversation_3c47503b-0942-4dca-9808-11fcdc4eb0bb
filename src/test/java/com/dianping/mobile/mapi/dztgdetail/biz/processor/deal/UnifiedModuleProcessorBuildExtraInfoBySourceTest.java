package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.module.ModuleConfigDo;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class UnifiedModuleProcessorBuildExtraInfoBySourceTest {

    @InjectMocks
    private UnifiedModuleProcessor processor;

    private DealCtx ctx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        processor = new UnifiedModuleProcessor();
        ctx = mock(DealCtx.class);
    }

    /**
     * Scenario 3: The request source is not "CREATE_ORDER_PREVIEW".
     * Expected Result: null is returned.
     */
    @Test
    public void testBuildExtraInfoBySource_NotCreateOrderPreview() throws Throwable {
        // arrange
        when(ctx.getRequestSource()).thenReturn("OTHER_SOURCE");
        // act
        List<ModuleConfigDo> result = processor.buildExtraInfoBySource(ctx);
        // assert
        assertNull(result);
    }

    /**
     * Scenario 4: The ctx object is null.
     * Expected Result: null is returned.
     */
    @Test
    public void testBuildExtraInfoBySource_NullCtx() throws Throwable {
        // arrange
        ctx = null;
        // act
        List<ModuleConfigDo> result = processor.buildExtraInfoBySource(ctx);
        // assert
        assertNull(result);
    }
}
