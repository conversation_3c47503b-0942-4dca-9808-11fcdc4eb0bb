package com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service;

import static com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants.CPV_CATEGORY_CONFIG;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.*;

import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.alibaba.fastjson.JSON;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.other.Pair;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.InventoryDetail;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.InventoryModuleVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.ProductDetailModule;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.StructDetailVO;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.productdetail.StructuredDetail;
import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;

@RunWith(PowerMockRunner.class)
@PrepareForTest({Lion.class})
public class CommonModuleBuilderServiceTest {

    @InjectMocks
    private CommonModuleBuilderService commonModuleBuilderService;

    @Mock
    private DealCtx dealCtx;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(Lion.class);
    }

    @Test
    public void testBuild_WhenResponseIsNull() {
        when(dealCtx.getCommonModuleResponse()).thenReturn(null);
        ProductDetailModule result = commonModuleBuilderService.build(dealCtx);
        assertNull(result);
    }

    @Test
    public void testBuild_WhenModuleResponseIsEmpty() {
        GenericProductDetailPageResponse response = new GenericProductDetailPageResponse();
        response.setModuleResponse(new HashMap<>());
        when(dealCtx.getCommonModuleResponse()).thenReturn(response);
        
        ProductDetailModule result = commonModuleBuilderService.build(dealCtx);
        assertNull(result);
    }

    @Test
    public void testBuild_Success() {
        // Prepare test data
        GenericProductDetailPageResponse response = new GenericProductDetailPageResponse();
        Map<String, GenericModuleResponse> moduleResponse = new HashMap<>();
        
        // Setup structured detail module
        GenericModuleResponse structuredDetailResponse = new GenericModuleResponse();
        StructDetailVO structDetailVO = new StructDetailVO();
        List<StructuredDetail> dealDetails = new ArrayList<>();
        dealDetails.add(new StructuredDetail());
        structDetailVO.setDealDetails(dealDetails);
        structuredDetailResponse.setModuleVO(JSON.parseObject(JSON.toJSONString(structDetailVO)));
        moduleResponse.put("module_detail_structured_detail", structuredDetailResponse);
        
        // Setup inventory module
        GenericModuleResponse inventoryResponse = new GenericModuleResponse();
        InventoryModuleVO inventoryModuleVO = new InventoryModuleVO();
        List<InventoryDetail> inventoryDetails = new ArrayList<>();
        inventoryDetails.add(new InventoryDetail());
        inventoryModuleVO.setInventoryDetails(inventoryDetails);
        inventoryResponse.setModuleVO(JSON.parseObject(JSON.toJSONString(inventoryModuleVO)));
        moduleResponse.put("module_detail_inventory_module", inventoryResponse);
        
        response.setModuleResponse(moduleResponse);
        when(dealCtx.getCommonModuleResponse()).thenReturn(response);

        ProductDetailModule result = commonModuleBuilderService.build(dealCtx);
        
        assertNotNull(result);
        assertNotNull(result.getStructDetail());
        assertNotNull(result.getDealInventory());
        assertEquals(1, result.getStructDetail().size());
        assertEquals(1, result.getDealInventory().size());
    }

    @Test
    public void testBuildRichText_WhenNotHitCpvCategoryId() {
        List<Pair> structuredDetails = Lists.newArrayList(new Pair());
        when(dealCtx.getStructedDetails()).thenReturn(structuredDetails);
        when(Lion.getList(LionConstants.APP_KEY, CPV_CATEGORY_CONFIG, Integer.class, Collections.emptyList())).thenAnswer(invocation -> Lists.newArrayList(0));

        List<Pair> result = commonModuleBuilderService.buildRichText(dealCtx);
        
        assertNotNull(result);
        assertEquals(structuredDetails, result);
    }

    @Test
    public void testBuildRichText_WhenStructuredDetailsIsEmpty() {
        when(dealCtx.getStructedDetails()).thenReturn(null);
        when(Lion.getList(LionConstants.APP_KEY, CPV_CATEGORY_CONFIG, Integer.class, Collections.emptyList())).thenAnswer(invocation -> Lists.newArrayList(0));

        List<Pair> result = commonModuleBuilderService.buildRichText(dealCtx);
        
        assertNull(result);
    }

    @Test
    public void testBuildRichText_WithExistingTargetPair() {
        // Prepare test data
        List<Pair> structuredDetails = new ArrayList<>();
        Pair targetPair = new Pair();
        targetPair.setId("套餐");
        structuredDetails.add(targetPair);
        
        when(dealCtx.getStructedDetails()).thenReturn(structuredDetails);
        when(Lion.getList(LionConstants.APP_KEY, CPV_CATEGORY_CONFIG, Integer.class, Collections.emptyList())).thenAnswer(invocation -> Lists.newArrayList(0));
        
        // Mock inventory module response
        GenericProductDetailPageResponse response = new GenericProductDetailPageResponse();
        Map<String, GenericModuleResponse> moduleResponse = new HashMap<>();
        GenericModuleResponse inventoryResponse = new GenericModuleResponse();
        InventoryModuleVO inventoryModuleVO = new InventoryModuleVO();
        inventoryModuleVO.setRichText("测试富文本");
        inventoryResponse.setModuleVO(JSON.parseObject(JSON.toJSONString(inventoryModuleVO)));
        moduleResponse.put("module_detail_inventory_module", inventoryResponse);
        response.setModuleResponse(moduleResponse);
        when(dealCtx.getCommonModuleResponse()).thenReturn(response);

        List<Pair> result = commonModuleBuilderService.buildRichText(dealCtx);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("测试富文本", result.get(0).getName());
    }

    @Test
    public void testBuildRichText_WithNewTargetPair() {
        // Prepare test data
        List<Pair> structuredDetails = new ArrayList<>();
        Pair otherPair = new Pair();
        otherPair.setId("其他");
        structuredDetails.add(otherPair);
        
        when(dealCtx.getStructedDetails()).thenReturn(structuredDetails);
        when(Lion.getList(LionConstants.APP_KEY, CPV_CATEGORY_CONFIG, Integer.class, Collections.emptyList())).thenAnswer(invocation -> Lists.newArrayList(0));
        
        // Mock inventory module response
        GenericProductDetailPageResponse response = new GenericProductDetailPageResponse();
        Map<String, GenericModuleResponse> moduleResponse = new HashMap<>();
        GenericModuleResponse inventoryResponse = new GenericModuleResponse();
        InventoryModuleVO inventoryModuleVO = new InventoryModuleVO();
        inventoryModuleVO.setRichText("新套餐富文本");
        inventoryResponse.setModuleVO(JSON.parseObject(JSON.toJSONString(inventoryModuleVO)));
        moduleResponse.put("module_detail_inventory_module", inventoryResponse);
        response.setModuleResponse(moduleResponse);
        when(dealCtx.getCommonModuleResponse()).thenReturn(response);

        List<Pair> result = commonModuleBuilderService.buildRichText(dealCtx);
        
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("新套餐富文本", result.get(1).getName());
        assertEquals("套餐", result.get(1).getId());
    }

    @Test
    public void testBuildInventoryModuleVO_Success() {
        // Prepare test data
        GenericProductDetailPageResponse response = new GenericProductDetailPageResponse();
        Map<String, GenericModuleResponse> moduleResponse = new HashMap<>();
        GenericModuleResponse inventoryResponse = new GenericModuleResponse();
        InventoryModuleVO inventoryModuleVO = new InventoryModuleVO();
        inventoryModuleVO.setRichText("测试富文本");
        inventoryResponse.setModuleVO(JSON.parseObject(JSON.toJSONString(inventoryModuleVO)));
        moduleResponse.put("module_detail_inventory_module", inventoryResponse);
        response.setModuleResponse(moduleResponse);
        
        when(dealCtx.getCommonModuleResponse()).thenReturn(response);

        InventoryModuleVO result = commonModuleBuilderService.buildInventoryModuleVO(dealCtx);
        
        assertNotNull(result);
        assertEquals("测试富文本", result.getRichText());
    }

    @Test
    public void testBuildInventoryModuleVO_WhenResponseIsNull() {
        when(dealCtx.getCommonModuleResponse()).thenReturn(null);
        
        InventoryModuleVO result = commonModuleBuilderService.buildInventoryModuleVO(dealCtx);
        
        assertNull(result);
    }

    @Test
    public void testBuildInventoryModuleVO_WhenModuleResponseIsEmpty() {
        GenericProductDetailPageResponse response = new GenericProductDetailPageResponse();
        response.setModuleResponse(new HashMap<>());
        when(dealCtx.getCommonModuleResponse()).thenReturn(response);
        
        InventoryModuleVO result = commonModuleBuilderService.buildInventoryModuleVO(dealCtx);
        
        assertNull(result);
    }
} 