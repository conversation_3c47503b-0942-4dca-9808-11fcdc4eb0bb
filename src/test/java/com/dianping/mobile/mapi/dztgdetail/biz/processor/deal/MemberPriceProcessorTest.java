package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.publishcategory.dto.DealGroupChannelDTO;
import com.dianping.mobile.mapi.dztgdetail.GenericTest;
import com.dianping.mobile.mapi.dztgdetail.common.enums.DztgClientTypeEnum;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import org.junit.Ignore;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/1/14
 */
public class MemberPriceProcessorTest extends GenericTest {

    @Resource
    private MemberPriceProcessor memberPriceProcessor;

    @Test
    @Ignore
    public void testEnable() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        DealCtx ctx = new DealCtx(envCtx);
        String channelText = "{\"dealGroupId\":426071694,\"categoryId\":1002,\"channelDTO\":{\"channelId\":10,\"channelEn\":\"child\",\"channelCn\":\"亲子\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"}}";
        DealGroupChannelDTO dealGroupChannelDTO = JSON.parseObject(channelText, DealGroupChannelDTO.class);
        ctx.setChannelDTO(dealGroupChannelDTO);
        boolean res = memberPriceProcessor.isEnable(ctx);
        assert res;
    }

    @Test
    @Ignore
    public void testPrepare() {
        EnvCtx envCtx = new EnvCtx();
        envCtx.setDztgClientTypeEnum(DztgClientTypeEnum.MEITUAN_APP);
        envCtx.setMtUserId(5034294434L);
        envCtx.setClientType(200502);
        DealCtx ctx = new DealCtx(envCtx);
        ctx.setMtId(427361679);
        ctx.setMtLongShopId(157754883L);
        memberPriceProcessor.prepare(ctx);
        assert ctx.getShopMemberDiscountInfoDTO() != null;
    }
}
