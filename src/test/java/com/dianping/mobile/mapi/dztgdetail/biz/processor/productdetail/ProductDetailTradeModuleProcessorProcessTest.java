package com.dianping.mobile.mapi.dztgdetail.biz.processor.productdetail;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.dianping.mobile.mapi.dztgdetail.biz.wrapper.ProductDetailWrapper;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.FutureCtx;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import java.util.concurrent.Future;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
@SuppressWarnings("unchecked")
public class ProductDetailTradeModuleProcessorProcessTest {

    @InjectMocks
    private ProductDetailTradeModuleProcessor processor;

    @Mock
    private ProductDetailWrapper productDetailWrapper;

    @Mock
    private FutureCtx futureCtx;

    @Mock
    private Future future;

    @Mock
    private DealCtx dealCtx;

    @Before
    public void setUp() {
        when(dealCtx.getFutureCtx()).thenReturn(futureCtx);
    }

    /**
     * Test case: Future is null
     * Expected: Method returns early without processing
     */
    @Test
    public void testProcess_WhenFutureIsNull() throws Throwable {
        // arrange
        when(futureCtx.getProductDetailTradeModuleFuture()).thenReturn(null);
        // act
        processor.process(dealCtx);
        // assert
        verify(productDetailWrapper, never()).getFutureResult(any(Future.class));
        verify(dealCtx, never()).setProductDetailTradeModuleResponse(any());
    }

    /**
     * Test case: Future result is blank
     * Expected: Method returns early without setting response
     */
    @Test
    public void testProcess_WhenFutureResultIsBlank() throws Throwable {
        // arrange
        when(futureCtx.getProductDetailTradeModuleFuture()).thenReturn(future);
        when(productDetailWrapper.getFutureResult(any(Future.class))).thenReturn("");
        // act
        processor.process(dealCtx);
        // assert
        verify(productDetailWrapper).getFutureResult(any(Future.class));
        verify(dealCtx, never()).setProductDetailTradeModuleResponse(any());
    }

    /**
     * Test case: Future returns valid JSON response
     * Expected: Response is parsed and set to context
     */
    @Test
    public void testProcess_WhenFutureReturnsValidResponse() throws Throwable {
        // arrange
        GenericProductDetailPageResponse expectedResponse = new GenericProductDetailPageResponse();
        expectedResponse.setCode(200);
        String jsonResponse = JSONObject.toJSONString(expectedResponse);
        when(futureCtx.getProductDetailTradeModuleFuture()).thenReturn(future);
        when(productDetailWrapper.getFutureResult(any(Future.class))).thenReturn(jsonResponse);
        // act
        processor.process(dealCtx);
        // assert
        verify(productDetailWrapper).getFutureResult(any(Future.class));
        verify(dealCtx).setProductDetailTradeModuleResponse(any(GenericProductDetailPageResponse.class));
    }

    /**
     * Test case: Future returns null
     * Expected: Method returns early without setting response
     */
    @Test
    public void testProcess_WhenFutureResultIsNull() throws Throwable {
        // arrange
        when(futureCtx.getProductDetailTradeModuleFuture()).thenReturn(future);
        when(productDetailWrapper.getFutureResult(any(Future.class))).thenReturn(null);
        // act
        processor.process(dealCtx);
        // assert
        verify(productDetailWrapper).getFutureResult(any(Future.class));
        verify(dealCtx, never()).setProductDetailTradeModuleResponse(any());
    }

    /**
     * Test case: Future returns empty JSON object
     * Expected: Empty response object is set
     */
    @Test
    public void testProcess_WhenEmptyJsonObject() throws Throwable {
        // arrange
        when(futureCtx.getProductDetailTradeModuleFuture()).thenReturn(future);
        when(productDetailWrapper.getFutureResult(any(Future.class))).thenReturn("{}");
        // act
        processor.process(dealCtx);
        // assert
        verify(productDetailWrapper).getFutureResult(any(Future.class));
        verify(dealCtx).setProductDetailTradeModuleResponse(any(GenericProductDetailPageResponse.class));
    }
}
