package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.dianping.mobile.mapi.dztgdetail.common.constants.LionConstants;
import com.dianping.mobile.mapi.dztgdetail.common.enums.ContentType;
import com.dianping.mobile.mapi.dztgdetail.datatype.resp.common.ContentPBO;
import com.dianping.mobile.mapi.dztgdetail.entity.ActivityConfig;
import com.dianping.mobile.mapi.dztgdetail.util.LionConfigUtils;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailFacadeBuildDescTest {

    private DealDetailFacade dealDetailFacade;

    @Before
    public void setUp() {
        dealDetailFacade = new DealDetailFacade();
    }

    private void invokeBuildDesc(List<ContentPBO> contents, String desc) throws Exception {
        Method method = DealDetailFacade.class.getDeclaredMethod("buildDesc", List.class, String.class);
        method.setAccessible(true);
        method.invoke(dealDetailFacade, contents, desc);
    }

    // Utility method to invoke private methods using reflection
    private void invokePrivateMethod(String methodName, Object targetObject, Object... args) throws Exception {
        Method method = targetObject.getClass().getDeclaredMethod(methodName, List.class, String.class);
        method.setAccessible(true);
        method.invoke(targetObject, args);
    }

    // Additional test cases would follow a similar pattern, focusing on the behavior that can be tested
    private void setImgTextActivityConfigs(List<ActivityConfig> configs) throws Exception {
        Field field = LionConfigUtils.class.getDeclaredField("IMG_TEXT_ACTIVITY_CONFIGS");
        field.setAccessible(true);
        field.set(null, configs);
    }

    private List<ContentPBO> invokePrivateMethod(String methodName, int dpId) throws Exception {
        Method method = DealDetailFacade.class.getDeclaredMethod(methodName, int.class);
        method.setAccessible(true);
        return (List<ContentPBO>) method.invoke(dealDetailFacade, dpId);
    }

    /**
     * 测试 buildDesc 方法，当 desc 为空，contents 为空列表时
     */
    @Test
    public void testBuildDescWhenDescIsEmptyAndContentsIsEmpty() throws Throwable {
        List<ContentPBO> contents = new ArrayList<>();
        invokeBuildDesc(contents, "");
        assertTrue(contents.isEmpty());
    }

    /**
     * 测试 buildDesc 方法，当 desc 为空，contents 非空列表时
     */
    @Test
    public void testBuildDescWhenDescIsEmptyAndContentsIsNotEmpty() throws Throwable {
        List<ContentPBO> contents = new ArrayList<>();
        invokeBuildDesc(contents, "");
        assertTrue(contents.isEmpty());
    }

    /**
     * 测试 buildDesc 方法，当 desc 非空，contents 为空列表时
     */
    @Test
    public void testBuildDescWhenDescIsNotEmptyAndContentsIsEmpty() throws Throwable {
        List<ContentPBO> contents = new ArrayList<>();
        invokeBuildDesc(contents, "test desc");
        assertFalse(contents.isEmpty());
        assertEquals(1, contents.size());
        assertEquals(ContentType.TEXT.getType(), contents.get(0).getType());
        assertEquals("test desc", contents.get(0).getContent());
    }

    /**
     * 测试 buildDesc 方法，当 desc 非空，contents 非空列表时
     */
    @Test
    public void testBuildDescWhenDescIsNotEmptyAndContentsIsNotEmpty() throws Throwable {
        List<ContentPBO> contents = new ArrayList<>();
        invokeBuildDesc(contents, "test desc");
        assertFalse(contents.isEmpty());
        assertEquals(1, contents.size());
        assertEquals(ContentType.TEXT.getType(), contents.get(0).getType());
        assertEquals("test desc", contents.get(0).getContent());
    }

    /**
     * 测试 title 为空的情况
     */
    @Test
    public void testBuildTitleTitleIsNull() throws Throwable {
        // arrange
        List<ContentPBO> contents = new ArrayList<>();
        String title = null;
        // act
        invokePrivateMethod("buildTitle", dealDetailFacade, contents, title);
        // assert
        assertEquals(0, contents.size());
    }



    @Test
    public void testGetActivityContentsWhenImgTextActivityConfigsIsEmpty() throws Throwable {
        // Ensure IMG_TEXT_ACTIVITY_CONFIGS is empty
        setImgTextActivityConfigs(Collections.emptyList());
        List<ContentPBO> result = invokePrivateMethod("getActivityContents", 1);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetActivityContentsWhenImgTextActivityConfigsIsNotEmptyButNoMatch() throws Throwable {
        // Setup empty configs to simulate no match
        setImgTextActivityConfigs(Collections.emptyList());
        List<ContentPBO> result = invokePrivateMethod("getActivityContents", 1);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetActivityContentsWhenImgTextActivityConfigsIsNotEmptyAndMatch() throws Throwable {
        // Setup configs to simulate a match
        ActivityConfig config = new ActivityConfig();
        config.setDealGroupIds(Collections.singletonList(1));
        setImgTextActivityConfigs(Collections.singletonList(config));
        List<ContentPBO> result = invokePrivateMethod("getActivityContents", 1);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getType());
    }
}
