package com.dianping.mobile.mapi.dztgdetail.biz.processor.deal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.mobile.mapi.dztgdetail.helper.AppCtxHelper;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShopBookProcessorTest {

    @InjectMocks
    private ShopBookProcessor shopBookProcessor;

    @Mock
    private DealCtx dealCtx;

    @Mock
    private EnvCtx envCtx;

    @Mock
    private URLEncoderWrapper urlEncoderWrapper;

    @Before
    public void setUp() {
        when(dealCtx.getEnvCtx()).thenReturn(envCtx);
    }

    private String invokePrivateMethod(Object object, String methodName, Object... params) throws Exception {
        Method method = object.getClass().getDeclaredMethod(methodName, DealCtx.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(object, params);
    }

    /**
     * Scenario 1: originOnlineConsultUrl is blank.
     * Expected Result: The method returns the original URL.
     */
    @Test
    public void testGetOnlineConsultUrl_BlankUrl() throws Throwable {
        // arrange
        String originOnlineConsultUrl = "";
        // act
        String result = invokePrivateMethod(shopBookProcessor, "getOnlineConsultUrl", dealCtx, originOnlineConsultUrl);
        // assert
        assertEquals(originOnlineConsultUrl, result);
    }

    /**
     * Scenario 3: originOnlineConsultUrl is not blank, and the environment is not KuaiShou MiniProgram.
     * Expected Result: The method returns the original URL.
     */
    @Test
    public void testGetOnlineConsultUrl_NotKuaiShouMiniProgram() throws Throwable {
        // arrange
        String originOnlineConsultUrl = "https://example.com";
        when(AppCtxHelper.isKuaiShouMiniProgram(envCtx)).thenReturn(false);
        // act
        String result = invokePrivateMethod(shopBookProcessor, "getOnlineConsultUrl", dealCtx, originOnlineConsultUrl);
        // assert
        assertEquals(originOnlineConsultUrl, result);
    }
}

class URLEncoderWrapper {

    public String encode(String url, String encoding) throws UnsupportedEncodingException {
        return URLEncoder.encode(url, encoding);
    }
}
