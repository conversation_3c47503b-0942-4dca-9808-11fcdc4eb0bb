package com.dianping.mobile.mapi.dztgdetail.facade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.mobile.mapi.dztgdetail.datatype.context.EnvCtx;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealTinyInfoFacadeBuildRequestTest {

    @InjectMocks
    private DealTinyInfoFacade dealTinyInfoFacade;

    private Object createDealProductRequestParam(Integer dealId, Long shopId, Integer skuId, Integer cityId, Double lat, Double lng, String pageSource, String planId, Boolean needPriceTrend, EnvCtx ctx) throws Exception {
        Class<?> clazz = Class.forName("com.dianping.mobile.mapi.dztgdetail.facade.DealTinyInfoFacade$DealProductRequestParam");
        Constructor<?> constructor = clazz.getDeclaredConstructor(EnvCtx.class, int.class, long.class, String.class, String.class, int.class, boolean.class, Integer.class, Double.class, Double.class);
        constructor.setAccessible(true);
        return constructor.newInstance(envCtx, dealId, shopId, pageSource, planId, skuId, needPriceTrend, cityId, lat, lng);
    }

    private DealProductRequest invokeBuildRequest(Object params) throws Exception {
        java.lang.reflect.Method method = DealTinyInfoFacade.class.getDeclaredMethod("buildRequest", Class.forName("com.dianping.mobile.mapi.dztgdetail.facade.DealTinyInfoFacade$DealProductRequestParam"));
        method.setAccessible(true);
        try {
            return (DealProductRequest) method.invoke(dealTinyInfoFacade, params);
        } catch (InvocationTargetException e) {
            Throwable cause = e.getCause();
            if (cause instanceof NullPointerException) {
                throw (NullPointerException) cause;
            }
            throw e;
        }
    }

    /**
     * 测试正常场景，所有字段都有有效值，且 ctx.isMt() 返回 true
     */
    @Test
    public void testBuildRequestNormalCaseMt() throws Throwable {
        // arrange
        Object params = createDealProductRequestParam(123, 456L, 789, 1, 39.9042, 116.4074, "deal", "10002451", true, envCtx);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getAppDeviceId()).thenReturn("device123");
        when(envCtx.getUnionId()).thenReturn("union123");
        when(envCtx.getMtUserId()).thenReturn(123L);
        when(envCtx.getVersion()).thenReturn("1.0.0");
        // act
        DealProductRequest request = invokeBuildRequest(params);
        // assert
        assertNotNull(request);
        assertEquals(Collections.singletonList(123), request.getProductIds());
        assertEquals("10002451", request.getPlanId());
        Map<String, Object> extParams = request.getExtParams();
        assertEquals(VCClientTypeEnum.MT_APP.getCode(), extParams.get("clientType"));
        assertEquals(VCPlatformEnum.MT.getType(), extParams.get("platform"));
        assertEquals("device123", extParams.get("deviceId"));
        assertEquals("union123", extParams.get("unionId"));
        assertEquals(123L, extParams.get("userId"));
        assertEquals(456, extParams.get("shopId"));
        assertEquals(456L, extParams.get("shopIdForLong"));
        assertEquals("1.0.0", extParams.get("appVersion"));
        assertEquals("comparePopup", extParams.get("scene"));
        assertTrue((Boolean) extParams.get("needPriceTrend"));
        assertEquals(1, extParams.get("cityId"));
        assertEquals(39.9042, extParams.get("lat"));
        assertEquals(116.4074, extParams.get("lng"));
        assertEquals("GCJ02", extParams.get("coordType"));
        assertEquals("dealGroupDetail", extParams.get("pageSource"));
        assertEquals(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene(), extParams.get("directPromoSceneCode"));
    }

    /**
     * 测试正常场景，所有字段都有有效值，且 ctx.isMt() 返回 false
     */
    @Test
    public void testBuildRequestNormalCaseDp() throws Throwable {
        // arrange
        Object params = createDealProductRequestParam(123, 456L, 789, 1, 39.9042, 116.4074, "shopcarnoselect", "10002451", true, envCtx);
        when(envCtx.isMt()).thenReturn(false);
        when(envCtx.getAppDeviceId()).thenReturn("device123");
        when(envCtx.getUnionId()).thenReturn("union123");
        when(envCtx.getDpUserId()).thenReturn(123L);
        when(envCtx.getVersion()).thenReturn("1.0.0");
        // act
        DealProductRequest request = invokeBuildRequest(params);
        // assert
        assertNotNull(request);
        assertEquals(Collections.singletonList(123), request.getProductIds());
        assertEquals("10002451", request.getPlanId());
        Map<String, Object> extParams = request.getExtParams();
        assertEquals(VCClientTypeEnum.DP_APP.getCode(), extParams.get("clientType"));
        assertEquals(VCPlatformEnum.DP.getType(), extParams.get("platform"));
        assertEquals("device123", extParams.get("deviceId"));
        assertEquals("union123", extParams.get("unionId"));
        assertEquals(123L, extParams.get("userId"));
        assertEquals(456, extParams.get("shopId"));
        assertEquals(456L, extParams.get("shopIdForLong"));
        assertEquals("1.0.0", extParams.get("appVersion"));
        assertEquals("comparePopup", extParams.get("scene"));
        assertTrue((Boolean) extParams.get("needPriceTrend"));
        assertEquals(1, extParams.get("cityId"));
        assertEquals(39.9042, extParams.get("lat"));
        assertEquals(116.4074, extParams.get("lng"));
        assertEquals("GCJ02", extParams.get("coordType"));
        assertEquals("dealGroupDetail", extParams.get("pageSource"));
        assertEquals(RequestSceneEnum.BUY_CAR_SINGLE_PRODUCT.getScene(), extParams.get("directPromoSceneCode"));
    }

    /**
     * 测试边界场景，dealId 为 0
     */
    @Test
    public void testBuildRequestBoundaryCaseDealIdZero() throws Throwable {
        // arrange
        Object params = createDealProductRequestParam(0, 456L, 789, 1, 39.9042, 116.4074, "deal", "10002451", true, envCtx);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getAppDeviceId()).thenReturn("device123");
        when(envCtx.getUnionId()).thenReturn("union123");
        when(envCtx.getMtUserId()).thenReturn(123L);
        when(envCtx.getVersion()).thenReturn("1.0.0");
        // act
        DealProductRequest request = invokeBuildRequest(params);
        // assert
        assertNotNull(request);
        assertEquals(Collections.singletonList(0), request.getProductIds());
    }

    /**
     * 测试边界场景，shopId 为 0
     */
    @Test
    public void testBuildRequestBoundaryCaseShopIdZero() throws Throwable {
        // arrange
        Object params = createDealProductRequestParam(123, 0L, 789, 1, 39.9042, 116.4074, "deal", "10002451", true, envCtx);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getAppDeviceId()).thenReturn("device123");
        when(envCtx.getUnionId()).thenReturn("union123");
        when(envCtx.getMtUserId()).thenReturn(123L);
        when(envCtx.getVersion()).thenReturn("1.0.0");
        // act
        DealProductRequest request = invokeBuildRequest(params);
        // assert
        assertNotNull(request);
        assertEquals(0, request.getExtParams().get("shopId"));
        assertEquals(0L, request.getExtParams().get("shopIdForLong"));
    }

    /**
     * 测试边界场景，skuId 为 0
     */
    @Test
    public void testBuildRequestBoundaryCaseSkuIdZero() throws Throwable {
        // arrange
        Object params = createDealProductRequestParam(123, 456L, 0, 1, 39.9042, 116.4074, "deal", "10002451", true, envCtx);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getAppDeviceId()).thenReturn("device123");
        when(envCtx.getUnionId()).thenReturn("union123");
        when(envCtx.getMtUserId()).thenReturn(123L);
        when(envCtx.getVersion()).thenReturn("1.0.0");
        // act
        DealProductRequest request = invokeBuildRequest(params);
        // assert
        assertNotNull(request);
        assertEquals(0, ((Map<Integer, Integer>) request.getExtParams().get("dealId2SkuId")).get(123).intValue());
    }

    /**
     * 测试边界场景，cityId 为 null
     */
    @Test
    public void testBuildRequestBoundaryCaseCityIdNull() throws Throwable {
        // arrange
        Object params = createDealProductRequestParam(123, 456L, 789, null, 39.9042, 116.4074, "deal", "10002451", true, envCtx);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getAppDeviceId()).thenReturn("device123");
        when(envCtx.getUnionId()).thenReturn("union123");
        when(envCtx.getMtUserId()).thenReturn(123L);
        when(envCtx.getVersion()).thenReturn("1.0.0");
        // act
        DealProductRequest request = invokeBuildRequest(params);
        // assert
        assertNotNull(request);
        assertEquals(0, request.getExtParams().get("cityId"));
    }

    /**
     * 测试边界场景，lat 和 lng 为 null
     */
    @Test
    public void testBuildRequestBoundaryCaseLatLngNull() throws Throwable {
        // arrange
        Object params = createDealProductRequestParam(123, 456L, 789, 1, null, null, "deal", "10002451", true, envCtx);
        when(envCtx.isMt()).thenReturn(true);
        when(envCtx.getAppDeviceId()).thenReturn("device123");
        when(envCtx.getUnionId()).thenReturn("union123");
        when(envCtx.getMtUserId()).thenReturn(123L);
        when(envCtx.getVersion()).thenReturn("1.0.0");
        // act
        DealProductRequest request = invokeBuildRequest(params);
        // assert
        assertNotNull(request);
        assertEquals(0.0, request.getExtParams().get("lat"));
        assertEquals(0.0, request.getExtParams().get("lng"));
    }



    @Mock
    private EnvCtx envCtx;
}
