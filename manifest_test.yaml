version: v1
build:
  os: centos7
  tools:
    dp-nodejs: '8'
    mt_oraclejdk: '8'
    maven: 3.9.5
  run:
    workDir: ./
    cmd:
    - mvn -Denv=qa -U -B -am -DskipTests clean package dependency:tree
  target:
    files:
    - target/*-qa-*.war
    - deploy
autodeploy:
  hulkos: centos7
  tools:
    dp-tomcat-hulk: 8.0.30
    dp-nginx-1.6.2-14: ''
    mt_oraclejdk: '8'
  run: sh deploy/run.sh
  check: sh deploy/check.sh
  checkRetry: 0
  checkInterval: 0s
